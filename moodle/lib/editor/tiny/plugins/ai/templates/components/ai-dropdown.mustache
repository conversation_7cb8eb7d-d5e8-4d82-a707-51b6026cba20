{{!
This file is part of Moodle - http://moodle.org/

Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
it under the terms of the GNU General Public License as published by
the Free Software Foundation, either version 3 of the License, or
(at your option) any later version.

Moodle is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
GNU General Public License for more details.

You should have received a copy of the GNU General Public License
along with Moodle. If not, see
<http: //www.gnu.org/licenses />.
}}
{{!
@template tiny_ai/components/ai-dropdown

Modal to select ai actions within the Tiny Editor.

Classes required for JS:
* none

Data attributes required for JS:
* none

Context variables required for this template:

Example context (json):
{}
}}
<span class="tiny_ai-dropdown-label">{{dropdownDescription}}</span>
<div id="tiny_ai-dropdown-{{uniqid}}" class="{{^dropup}}dropdown{{/dropup}}{{#dropup}}dropup{{/dropup}} tiny_ai-dropdown" data-preference="{{preference}}">
  <button class="btn btn-secondary dropdown-toggle tiny_ai-btn tiny_ai-dropdown-toggle" type="button" id="dropdownMenuButton"{{!
  }} data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" data-value="{{dropdownDefaultValue}}" data-dropdown="select">
      <span data-dropdown="selecttext">{{dropdownDefault}}</span>
      <i class="tiny_ai-icon fa {{^dropup}}fa-chevron-down{{/dropup}}{{#dropup}}fa-chevron-up{{/dropup}}"></i>
  </button>
  <div class="dropdown-menu tiny_ai-dropdown-menu" aria-labelledby="dropdownMenuButton">
  {{#dropdownOptions}}
      <a class="dropdown-item tiny_ai-dropdown-item" data-dropdown="option" data-value="{{optionValue}}">{{optionLabel}}</a>
  {{/dropdownOptions}}
  </div>
</div>
{{#js}}
    require(['tiny_ai/controllers/dropdown'], function(DropdownController) {
        DropdownController.init('#tiny_ai-dropdown-{{uniqid}}[data-preference="{{preference}}"]');
    });
{{/js}}
