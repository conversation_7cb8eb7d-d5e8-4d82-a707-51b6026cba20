{{!
This file is part of Moodle - http://moodle.org/

Mo<PERSON>le is free software: you can redistribute it and/or modify
it under the terms of the GNU General Public License as published by
the Free Software Foundation, either version 3 of the License, or
(at your option) any later version.

Moodle is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHA<PERSON>ABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
GNU General Public License for more details.

You should have received a copy of the GNU General Public License
along with Moodle. If not, see
<http: //www.gnu.org/licenses />.
}}
{{!
@template tiny_ai/components/moodle-modal-body-preferences

Modal to select ai actions within the Tiny Editor.

Classes required for JS:
* none

Data attributes required for JS:
* none

Context variables required for this template:

Example context (json):
{}
}}
<div id="tiny_ai_{{tool}}_{{uniqid}}" data-tiny_instance_uniqid={{tinyinstanceuniqid}} class="h-100">
    <div class="row">
        {{#modalDropdowns}}
            <div class="col-12 col-md-6 mb-3">
                {{> tiny_ai/components/ai-dropdown}}
            </div>
        {{/modalDropdowns}}
    </div>
        <div class="row flex-grow-1">
            <div class="col-12 mb-3 tiny_ai-prompt-wrapper">
                {{> tiny_ai/components/ai-button}}
                {{#textareas}}
                    {{> tiny_ai/components/ai-textarea}}
                {{/textareas}}
            </div>
        </div>
</div>
{{#js}}
    require(['tiny_ai/controllers/preferences', 'tiny_ai/controllers/promptedit'], function(Controller, PromptEditController) {
        const controller = new Controller('#tiny_ai_{{tool}}_{{uniqid}}', '{{tool}}');
        Promise.resolve(controller.init());
        const promptEditController = new PromptEditController('#tiny_ai_{{tool}}_{{uniqid}}');
        Promise.resolve(promptEditController.init());
    });
{{/js}}
