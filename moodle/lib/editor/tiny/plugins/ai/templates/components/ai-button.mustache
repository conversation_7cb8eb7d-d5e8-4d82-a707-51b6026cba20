{{!
This file is part of Moodle - http://moodle.org/

Moodle is free software: you can redistribute it and/or modify
it under the terms of the GNU General Public License as published by
the Free Software Foundation, either version 3 of the License, or
(at your option) any later version.

Moodle is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHA<PERSON>ABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
GNU General Public License for more details.

You should have received a copy of the GNU General Public License
along with Moodle. If not, see
<http: //www.gnu.org/licenses />.
}}
{{!
@template tiny_ai/components/ai-button

Modal to select ai actions within the Tiny Editor.

Classes required for JS:
* none

Data attributes required for JS:
* none

Context variables required for this template:

Example context (json):
{}
}}
<button class="tiny_ai-button {{#btn-iconname}}has-icon {{/btn-iconname}}{{#disabled}}disabled {{/disabled}}{{!
}}{{#primary}}primary {{/primary}}{{#secondary}}secondary {{/secondary}}{{#tertiary}}tertiary {{/tertiary}}{{!
}}{{#aiButtonHidden}}d-none {{/aiButtonHidden}}" data-action="{{action}}" {{#expanded}}data-expanded{{/expanded}}{{!
}}data-toggle="tooltip" data-placement="top" title="{{tooltip}}">

    {{#iconLeft}}
        {{> tiny_ai/components/ai-icon}}
    {{/iconLeft}}

    {{#hasText}}
        <span class="tiny_ai-button-text" data-text>{{buttonText}}</span>
    {{/hasText}}

    {{#iconRight}}
        {{> tiny_ai/components/ai-icon}}
    {{/iconRight}}
</button>
