{{!
This file is part of Moodle - http://moodle.org/

Mo<PERSON>le is free software: you can redistribute it and/or modify
it under the terms of the GNU General Public License as published by
the Free Software Foundation, either version 3 of the License, or
(at your option) any later version.

Moodle is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHA<PERSON>ABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
GNU General Public License for more details.

You should have received a copy of the GNU General Public License
along with Moodle. If not, see
<http: //www.gnu.org/licenses />.
}}
{{!
@template tiny_ai/components/ai-audio-player

Modal to select ai actions within the Tiny Editor.

Classes required for JS:
* none

Data attributes required for JS:
* none

Context variables required for this template:

Example context (json):
{}
}}
<span class="tiny_ai-audio-label">{{audio_description}}</span>
<div class="tiny_ai-audio-player">
    <audio class="tiny_ai-audio-source" src="/audio/audio-test-file.mp3"></audio>
    <button class="tiny_ai-play-pause-button">
        <i class="fa fa-play"></i>
    </button>
    <div class="tiny_ai-progress-bar">
        <div class="tiny_ai-progress-bar-filled"></div>
        <div class="tiny_ai-progress-bar-thumb"></div>
    </div>
    <div class="tiny_ai-time">00:00 / 00:08</div>
</div>
