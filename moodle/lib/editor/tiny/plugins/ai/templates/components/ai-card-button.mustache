{{!
This file is part of Moodle - http://moodle.org/

Mo<PERSON>le is free software: you can redistribute it and/or modify
it under the terms of the GNU General Public License as published by
the Free Software Foundation, either version 3 of the License, or
(at your option) any later version.

Moodle is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHA<PERSON>ABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
GNU General Public License for more details.

You should have received a copy of the GNU General Public License
along with Moodle. If not, see
<http: //www.gnu.org/licenses />.
}}
{{!
@template tiny_ai/components/ai-card-button

Modal to select ai actions within the Tiny Editor.

Classes required for JS:
* none

Data attributes required for JS:
* none

Context variables required for this template:

Example context (json):
{}
}}
<div data-action="toggletooltip" data-toggle="tooltip" data-placement="top" title="{{tooltip}}">
    <a href="#" class="tiny_ai-card-button {{#disabled}}disabled{{/disabled}}" data-action="{{action}}" data-tool="{{toolname}}">
        {{> tiny_ai/components/ai-icon}}

        <span class="tiny_ai-card-button--text-wrapper">
            <span class="tiny_ai-card-button--tool">{{tool}}</span>
            <span class="tiny_ai-card-button--description">{{description}}</span>
        </span>
    </a>
</div>
