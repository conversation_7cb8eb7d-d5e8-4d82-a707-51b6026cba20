{{!
This file is part of Moodle - http://moodle.org/

Mo<PERSON>le is free software: you can redistribute it and/or modify
it under the terms of the GNU General Public License as published by
the Free Software Foundation, either version 3 of the License, or
(at your option) any later version.

Moodle is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHA<PERSON>ABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
GNU General Public License for more details.

You should have received a copy of the GNU General Public License
along with Moodle. If not, see
<http: //www.gnu.org/licenses />.
}}
{{!
@template tiny_ai/components/moodle-modal-body-itt

Modal to select ai actions within the Tiny Editor.

Classes required for JS:
* none

Data attributes required for JS:
* none

Context variables required for this template:

Example context (json):
{}
}}
<div id="tiny_ai_{{tool}}_{{uniqid}}" data-tiny_instance_uniqid={{tinyinstanceuniqid}} class="h-100">
    <div class="row">
        <div class="tiny_ai_dropzone justify-content-center w-100" data-type="dropzone" disabled>
            {{insertimagedescription}}
        </div>
        <input type="hidden" data-preference="fileupload" />
    </div>
    <div class="row flex-grow-1">
        <div class="col-12 mb-3 tiny_ai-prompt-wrapper">
            {{> tiny_ai/components/ai-button}}
            {{> tiny_ai/components/ai-textarea}}
        </div>
    </div>

</div>
{{#js}}
    require(['tiny_ai/controllers/preferences', 'tiny_ai/controllers/promptedit', 'tiny_ai/controllers/file'], function(Controller, PromptEditController, FileController) {
    const controller = new Controller('#tiny_ai_{{tool}}_{{uniqid}}', '{{tool}}');
    Promise.resolve(controller.init());
    const promptEditController = new PromptEditController('#tiny_ai_{{tool}}_{{uniqid}}');
    Promise.resolve(promptEditController.init());
    const fileController = new FileController('#tiny_ai_{{tool}}_{{uniqid}}');
    Promise.resolve(fileController.init());
    });
{{/js}}
