<?php
// This file is part of Moodle - https://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <https://www.gnu.org/licenses/>.

/**
 * Plugin strings are defined here.
 *
 * @package     tiny_ai
 * @category    string
 * @copyright   2024, ISB Bayern
 * <AUTHOR> <PERSON>
 * @license     https://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

$string['additional_prompt'] = 'Additional Prompt';
$string['ai:view'] = 'View the AI button';
$string['aigenerating'] = 'AI is generating...';
$string['aisuggestion'] = 'AI suggestion';
$string['back'] = 'Back';
$string['backbutton_tooltip'] = 'Back to the previous page';
$string['cancel'] = 'Cancel';
$string['copybutton'] = 'Copy';
$string['copybutton_tooltip'] = 'Copy current result to clipboard';
$string['deletebutton_tooltip'] = 'Dismiss current result and go back to the preferences page';
$string['describe_baseprompt'] = 'Describe the following text';
$string['describe_headline'] = 'Detailed description of the text';
$string['describeimg_baseprompt'] = 'Describe what is being shown on the image';
$string['describeimg_headline'] = 'Image description';
$string['dismiss'] = 'Dismiss';
$string['dismisssuggestion'] = 'Do you want to dismiss the AI suggestion?';
$string['downloadbutton'] = 'Download';
$string['downloadbutton_tooltip'] = 'Download current result as file';
$string['error_filetypeclipboardnotsupported_text'] = 'Your browser does not support copying this file type into your clipboard.';
$string['error_filetypeclipboardnotsupported_title'] = 'Unsupported file type';
$string['error_nofile'] = 'No file added. Please add a file.';
$string['error_nofileinclipboard_text'] = 'Clipboard does not contain file data. Please copy a file into the clipboard before pasting.';
$string['error_nofileinclipboard_title'] = 'No file';
$string['error_nopromptgiven'] = 'No prompt given. Please insert a prompt.';
$string['error_tiny_ai_notavailable'] = 'The AI features are not availeble for you.';
$string['error_unsupportedfiletype_text'] = 'This file type is not supported. Supported types are: {$a}';
$string['error_unsupportedfiletype_title'] = 'Not supported filetype';
$string['errorwithcode'] = 'An error occured with error code {$a}';
$string['freeprompt_placeholder'] = 'Give the AI any order for generating text...';
$string['freepromptbutton_tooltip'] = 'Generate AI answer';
$string['gender'] = 'Gender';
$string['generalerror'] = 'An error has occured';
$string['generate'] = 'Generate now';
$string['generatebutton_tooltip'] = 'Let the AI generate an answer';
$string['generating'] = 'The AI answer is being generated...';
$string['imagefromeditor'] = 'Image from editor';
$string['imagetotext_baseprompt'] = 'Parse the text in the image';
$string['imagetotext_headline'] = 'Text recognition';
$string['imagetotext_insertimage'] = 'Drag&Drop a file into this area or paste a file from clipboard';
$string['imggen_headline'] = 'Image generation';
$string['imggen_placeholder'] = 'Insert or paste description of the image here, for example "Generate a photorealistic image of a monkey that holds a pen in one hand and wears a hat on the head"';
$string['insertatcaret'] = 'Insert at current position';
$string['insertatcaret_tooltip'] = 'Insert current result at the cursor\'s current position';
$string['insertbelow'] = 'Insert below';
$string['insertbelow_tooltip'] = 'Append current result to the editor content';
$string['keeplanguagetype'] = 'Keep language type';
$string['languagetype'] = 'Language type';
$string['languagetype_prompt'] = 'The text must use {$a}';
$string['mainselection_heading'] = 'What should the AI help you with?';
$string['maxwordcount'] = 'Maximum amount of words';
$string['maxwordcount_prompt'] = 'The text must not contain more than {$a} words';
$string['more_options'] = 'More Options';
$string['nomaxwordcount'] = 'No limit';
$string['nopurposesconfigured'] = 'No AI tools have been configured. Talk to your tenant manager.';
$string['pluginname'] = 'AI tools';
$string['privacy:metadata'] = 'This plugin does not store any personal data.';
$string['prompt'] = 'Prompt';
$string['prompteditmode'] = 'Prompt edit mode';
$string['prompteditmode_tooltip'] = 'Toggle prompt edit mode';
$string['prompteditmodedisable'] = 'Leave prompt edit mode';
$string['regeneratebutton_tooltip'] = 'Edit prompt and regenerate the result';
$string['replaceselection'] = 'Replace selection';
$string['replaceselection_tooltip'] = 'Replace current selection with the current result';
$string['results_heading'] = 'Result';
$string['results_please_wait'] = 'Please wait! This may take a few seconds.';
$string['reworkprompt'] = 'Rework prompt';
$string['selectionbarbuttontitle'] = 'Use AI tools for selected text';
$string['simplelanguage'] = 'Simple language';
$string['size'] = 'Size';
$string['summarize_baseprompt'] = 'Summarize the following text';
$string['summarize_headline'] = 'Summarize text';
$string['targetlanguage'] = 'Target language';
$string['technicallanguage'] = 'Technical language';
$string['texttouse'] = 'The text reads';
$string['toolbarbuttontitle'] = 'AI tools';
$string['toolname_describe'] = 'Detailed description';
$string['toolname_describeimg'] = 'Image description';
$string['toolname_imagetotext'] = 'Text recognition';
$string['toolname_imggen'] = 'Image generation';
$string['toolname_summarize'] = 'Summarize';
$string['toolname_translate'] = 'Translate';
$string['toolname_tts'] = 'Create audio';
$string['translate_baseprompt'] = 'Translate the following text to the language {$a} and only return the translated text';
$string['translate_headline'] = 'Translate text';
$string['tts_headline'] = 'Generate audio from text';
$string['voice'] = 'Voice';
