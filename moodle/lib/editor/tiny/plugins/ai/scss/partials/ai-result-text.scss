.tiny_ai-result-text {
    display: flex;
    padding: 10px;
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
    align-self: stretch;

    border-radius: 4px;
    border: 1px solid $neutral-grey-100;
    background: $neutral-white;

    p {
        margin: 0;
    }

    img {
        max-height: 50vh;
    }
}

.dark {
    .tiny_ai-result-text {

        border: 1px solid $lernplattform-400;
        background: $neutral-grey-500;
    }
}
