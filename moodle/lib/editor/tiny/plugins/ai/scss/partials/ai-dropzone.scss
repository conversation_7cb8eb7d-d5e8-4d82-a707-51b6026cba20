.tiny_ai_dropzone {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  border-width: 2px;
  border-radius: 2px;
  border-color: #eeeeee;
  border-style: dashed;
  background-color: #fafafa;
  color: #bdbdbd;
  outline: none;
  transition: border .24s ease-in-out;
  height: 15rem;
  /* Hide the cursor, because we need to set contenteditable on the div for pasting, but do not want editing */
  caret-color: transparent;
}

.tiny_ai_dragover {
  background-color: #63676c;
}

.tiny_ai_dropzone_filled {
  background-color: #ffffff;
  color: #000000;
}

.tiny_ai_dropzone_preview_container {
  max-width: 80%;
  max-height: 80%;

  img {
    max-width: 100%;
    max-height: 100%;
  }
}
