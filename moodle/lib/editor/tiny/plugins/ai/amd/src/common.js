// This file is part of Moodle - https://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <https://www.gnu.org/licenses/>.

/**
 * Common values helper for the Moodle tiny_ai plugin.
 *
 * @module      tiny_ai/common
 * @copyright   2024, ISB Bayern
 * <AUTHOR> <PERSON>
 * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

const component = 'tiny_ai';

export default {
    component,
    pluginName: `${component}/plugin`,
    icon: component,
    toolbarButtonName: "start_ai",
    selectionbarButtonName: "start_ai_selection",
    selectionbarSource: 'selectionbar',
    menubarSource: 'menubar',
    toolbarSource: 'toolbar'
};
