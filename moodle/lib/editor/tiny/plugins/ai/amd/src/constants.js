// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Mo<PERSON><PERSON> is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Tiny AI constants definition.
 *
 * @module      tiny_ai/constants
 * @copyright   2024, ISB Bayern
 * <AUTHOR>
 * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */


export const constants = {
    modalModes: {
        editor: 'editor',
        standalone: 'standalone'
    },
    toolPurposeMapping: {
        audiogen: 'tts',
        summarize: 'singleprompt',
        translate: 'translate',
        describe: 'singleprompt',
        imggen: 'imggen',
        tts: 'tts',
        freeprompt: 'singleprompt',
        describeimg: 'itt',
        imagetotext: 'itt'
    }
};
