define("tiny_ai/renderer",["exports","local_ai_manager/infobox","local_ai_manager/userquota","tiny_ai/datahandler/basedata","core/templates","jquery","tiny_ai/utils","tiny_ai/constants"],(function(_exports,_infobox,_userquota,BasedataHandler,_templates,_jquery,_utils,_constants){function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}function _getRequireWildcardCache(nodeInterop){if("function"!=typeof WeakMap)return null;var cacheBabelInterop=new WeakMap,cacheNodeInterop=new WeakMap;return(_getRequireWildcardCache=function(nodeInterop){return nodeInterop?cacheNodeInterop:cacheBabelInterop})(nodeInterop)}function _defineProperty(obj,key,value){return key in obj?Object.defineProperty(obj,key,{value:value,enumerable:!0,configurable:!0,writable:!0}):obj[key]=value,obj}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0,BasedataHandler=function(obj,nodeInterop){if(!nodeInterop&&obj&&obj.__esModule)return obj;if(null===obj||"object"!=typeof obj&&"function"!=typeof obj)return{default:obj};var cache=_getRequireWildcardCache(nodeInterop);if(cache&&cache.has(obj))return cache.get(obj);var newObj={},hasPropertyDescriptor=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var key in obj)if("default"!==key&&Object.prototype.hasOwnProperty.call(obj,key)){var desc=hasPropertyDescriptor?Object.getOwnPropertyDescriptor(obj,key):null;desc&&(desc.get||desc.set)?Object.defineProperty(newObj,key,desc):newObj[key]=obj[key]}newObj.default=obj,cache&&cache.set(obj,newObj);return newObj}(BasedataHandler),_templates=_interopRequireDefault(_templates),_jquery=_interopRequireDefault(_jquery);return _exports.default=class{constructor(uniqid,mode){_defineProperty(this,"uniqid",null),_defineProperty(this,"mode",null),_defineProperty(this,"datamanager",null),this.uniqid=uniqid,this.mode=mode,this.datamanager=(0,_utils.getDatamanager)(uniqid)}async renderStart(){this.datamanager.reset();const templateContext=await(0,_utils.getStartHandler)(this.uniqid).getTemplateContext((0,_utils.getEditorUtils)(this.uniqid));await this.renderModalContent("moodle-modal-body-start","moodle-modal-footer-info",templateContext)}async renderSummarize(){const templateContext=(0,_utils.getSummarizeHandler)(this.uniqid).getTemplateContext("summarize");await this.renderModalContent("moodle-modal-body-preferences","moodle-modal-footer-generate",templateContext)}async renderTranslate(){const templateContext=(0,_utils.getTranslateHandler)(this.uniqid).getTemplateContext();await this.renderModalContent("moodle-modal-body-preferences","moodle-modal-footer-generate",templateContext)}async renderDescribe(){const templateContext=(0,_utils.getSummarizeHandler)(this.uniqid).getTemplateContext("describe");await this.renderModalContent("moodle-modal-body-preferences","moodle-modal-footer-generate",templateContext)}async renderTts(){const templateContext=await(0,_utils.getTtsHandler)(this.uniqid).getTemplateContext();await this.renderModalContent("moodle-modal-body-preferences","moodle-modal-footer-generate",templateContext)}async renderImggen(){const templateContext=await(0,_utils.getImggenHandler)(this.uniqid).getTemplateContext();await this.renderModalContent("moodle-modal-body-mediageneration","moodle-modal-footer-generate",templateContext)}async renderDescribeimg(){const templateContext=await(0,_utils.getIttHandler)(this.uniqid).getTemplateContext("describeimg");await this.renderModalContent("moodle-modal-body-itt","moodle-modal-footer-generate",templateContext)}async renderImagetotext(){const templateContext=await(0,_utils.getIttHandler)(this.uniqid).getTemplateContext("imagetotext");await this.renderModalContent("moodle-modal-body-itt","moodle-modal-footer-generate",templateContext)}async renderLoading(){const templateContext={};templateContext.modalHeadline=BasedataHandler.getTinyAiString("aigenerating"),await this.renderModalContent("moodle-modal-body-loading","moodle-modal-footer-empty",templateContext)}async renderSuggestion(){const templateContext={};templateContext.modalHeadline=BasedataHandler.getTinyAiString("aisuggestion"),templateContext.resultText=this.renderAiResultForEditor(),this.mode===_constants.constants.modalModes.editor?Object.assign(templateContext,BasedataHandler.getReplaceButtonsContext(this.datamanager.getSelectionText().length>0)):Object.assign(templateContext,BasedataHandler.getCopyAndDownloadButtonsContext()),await this.renderModalContent("moodle-modal-body-suggestion","moodle-modal-footer-replace",templateContext)}async renderOptimizePrompt(){const templateContext=(0,_utils.getOptimizeHandler)(this.uniqid).getTemplateContext();await this.renderModalContent("moodle-modal-body-optimize","moodle-modal-footer-generate",templateContext)}async renderDismiss(){const templateContext={modalHeadline:"",centeredHeadline:BasedataHandler.getTinyAiString("dismisssuggestion"),showIcon:!1,buttons:[{hasText:!0,buttonText:BasedataHandler.getTinyAiString("cancel"),iconLeft:!1,iconRight:!1,primary:!1,secondary:!0,tertiary:!1,action:"canceldismiss"},{hasText:!0,buttonText:BasedataHandler.getTinyAiString("dismiss"),iconLeft:!1,iconRight:!1,primary:!0,secondary:!1,tertiary:!1,action:"dismiss"}]};await this.renderModalContent("moodle-modal-body-dismiss","moodle-modal-footer-empty",templateContext)}renderAiResultForEditor(){let html;switch(this.datamanager.getCurrentTool()){case"tts":case"audiogen":{const audioPlayer=document.createElement("audio");audioPlayer.controls="controls",audioPlayer.src=this.datamanager.getCurrentAiResult(),audioPlayer.type="audio/mpeg",html=audioPlayer.outerHTML;break}case"imggen":{const img=document.createElement("img");img.src=this.datamanager.getCurrentAiResult(),img.classList.add("mw-100"),html=img.outerHTML;break}default:html=this.datamanager.getCurrentAiResult()}return html}async renderModalContent(bodyComponentTemplate,footerComponentTemplate,templateContext){templateContext.tinyinstanceuniqid=this.uniqid;const modal=(0,_utils.getEditorUtils)(this.uniqid).getModal();document.querySelectorAll("button[data-action]").forEach((button=>{(0,_jquery.default)(button).tooltip("hide")}));const result=await Promise.all([_templates.default.renderForPromise("tiny_ai/components/moodle-modal-header-title",templateContext),_templates.default.renderForPromise("tiny_ai/components/"+bodyComponentTemplate,templateContext),_templates.default.renderForPromise("tiny_ai/components/"+footerComponentTemplate,templateContext)]);templateContext.hasOwnProperty("modalHeadline")&&modal.setTitle(result[0].html),document.querySelectorAll("button[data-action]").forEach((button=>{(0,_jquery.default)(button).tooltip("hide")})),modal.setBody(result[1].html),modal.setFooter(result[2].html),result.forEach((item=>{_templates.default.runTemplateJS(item.js)})),modal.getRoot().attr("data-tiny_ai_uniqid",this.uniqid),await this.insertInfoBox(),await this.insertUserQuotaBox(),document.querySelectorAll("button[data-action]").forEach((button=>{button.addEventListener("click",(event=>{(0,_jquery.default)(event.target).closest("button[data-action]").tooltip("hide")}))}))}async insertInfoBox(){document.querySelector('[data-rendertarget="infobox"]')&&await(0,_infobox.renderInfoBox)("tiny_ai",(0,_utils.getEditorUtils)(this.uniqid).getUserId(),'[data-rendertarget="infobox"]',["singleprompt","translate","tts","imggen","itt"])}async insertUserQuotaBox(){document.querySelector('[data-rendertarget="usageinfo"]')&&await(0,_userquota.renderUserQuota)('[data-rendertarget="usageinfo"]',["singleprompt","translate","tts","imggen","itt"])}},_exports.default}));

//# sourceMappingURL=renderer.min.js.map