define("tiny_ai/datahandler/imggen",["exports","local_ai_manager/config","./basedata","tiny_ai/datahandler/base"],(function(_exports,AiConfig,BasedataHandler,_base){var obj;function _getRequireWildcardCache(nodeInterop){if("function"!=typeof WeakMap)return null;var cacheBabelInterop=new WeakMap,cacheNodeInterop=new WeakMap;return(_getRequireWildcardCache=function(nodeInterop){return nodeInterop?cacheNodeInterop:cacheBabelInterop})(nodeInterop)}function _interopRequireWildcard(obj,nodeInterop){if(!nodeInterop&&obj&&obj.__esModule)return obj;if(null===obj||"object"!=typeof obj&&"function"!=typeof obj)return{default:obj};var cache=_getRequireWildcardCache(nodeInterop);if(cache&&cache.has(obj))return cache.get(obj);var newObj={},hasPropertyDescriptor=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var key in obj)if("default"!==key&&Object.prototype.hasOwnProperty.call(obj,key)){var desc=hasPropertyDescriptor?Object.getOwnPropertyDescriptor(obj,key):null;desc&&(desc.get||desc.set)?Object.defineProperty(newObj,key,desc):newObj[key]=obj[key]}return newObj.default=obj,cache&&cache.set(obj,newObj),newObj}function _defineProperty(obj,key,value){return key in obj?Object.defineProperty(obj,key,{value:value,enumerable:!0,configurable:!0,writable:!0}):obj[key]=value,obj}
/**
   * Tiny AI data manager.
   *
   * @module      tiny_ai/datahandler/imggen
   * @copyright   2024, ISB Bayern
   * <AUTHOR> Memmel
   * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
   */Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0,AiConfig=_interopRequireWildcard(AiConfig),BasedataHandler=_interopRequireWildcard(BasedataHandler),_base=(obj=_base)&&obj.__esModule?obj:{default:obj};class _default extends _base.default{constructor(){super(...arguments),_defineProperty(this,"imggenOptions",null),_defineProperty(this,"size",null)}async getSizesOptions(){return await this.loadImggenOptions(),this.imggenOptions.sizes}setSize(size){this.size=size}getOptions(){if(null===this.size)return{};const options={};return this.size&&(options.sizes=[this.size]),options}async loadImggenOptions(){if(null===this.imggenOptions){const fetchedOptions=await AiConfig.getPurposeOptions("imggen");this.imggenOptions=JSON.parse(fetchedOptions.options)}}async getTemplateContext(){const context={modalHeadline:BasedataHandler.getTinyAiString("imggen_headline"),showIcon:!0,tool:"imggen",textareatype:"prompt",placeholder:BasedataHandler.getTinyAiString("imggen_placeholder")},modalDropdowns=[],sizesOptions=await this.getSizesOptions();if(null!==sizesOptions&&Object.keys(sizesOptions).length>0){const sizesDropdownContext={preference:"sizes"};sizesDropdownContext.dropdownDefault=sizesOptions[0].displayname,sizesDropdownContext.dropdownDefaultValue=sizesOptions[0].key,sizesDropdownContext.dropdownDescription=BasedataHandler.getTinyAiString("size");const sizesDropdownOptions=[];sizesOptions.forEach((option=>{sizesDropdownOptions.push({optionValue:option.key,optionLabel:option.displayname})})),sizesDropdownContext.dropdownOptions=sizesDropdownOptions,modalDropdowns.push(sizesDropdownContext)}return modalDropdowns.forEach((dropdownContext=>{dropdownContext.dropup=!0})),Object.assign(context,{modalDropdowns:modalDropdowns}),Object.assign(context,BasedataHandler.getBackAndGenerateButtonContext()),context}}return _exports.default=_default,_exports.default}));

//# sourceMappingURL=imggen.min.js.map