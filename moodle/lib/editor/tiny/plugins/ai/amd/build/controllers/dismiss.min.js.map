{"version": 3, "file": "dismiss.min.js", "sources": ["../../src/controllers/dismiss.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Mo<PERSON>le is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Controller for dismiss page.\n *\n * @module      tiny_ai/controllers/dismiss\n * @copyright   2024, ISB Bayern\n * <AUTHOR>\n * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport BaseController from 'tiny_ai/controllers/base';\n\nexport default class extends BaseController {\n\n    async init() {\n        const cancelButton = this.baseElement.querySelector('[data-action=\"canceldismiss\"]');\n        const dismissButton = this.baseElement.querySelector('[data-action=\"dismiss\"]');\n\n        if (cancelButton) {\n            cancelButton.addEventListener('click', async() => {\n                await this.renderer.renderSuggestion();\n            });\n        }\n        if (dismissButton) {\n            dismissButton.addEventListener('click', async() => {\n                await this.callRendererFunction();\n            });\n        }\n    }\n}\n"], "names": ["BaseController", "cancelButton", "this", "baseElement", "querySelector", "dismiss<PERSON><PERSON><PERSON>", "addEventListener", "async", "renderer", "renderSuggestion", "callRendererFunction"], "mappings": ";;;;;;;;gKA0B6BA,iCAGfC,aAAeC,KAAKC,YAAYC,cAAc,iCAC9CC,cAAgBH,KAAKC,YAAYC,cAAc,2BAEjDH,cACAA,aAAaK,iBAAiB,SAASC,gBAC7BL,KAAKM,SAASC,sBAGxBJ,eACAA,cAAcC,iBAAiB,SAASC,gBAC9BL,KAAKQ"}