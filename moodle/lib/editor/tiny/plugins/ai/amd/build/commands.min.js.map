{"version": 3, "file": "commands.min.js", "sources": ["../src/commands.js"], "sourcesContent": ["// This file is part of Moodle - https://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// <PERSON><PERSON>le is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <https://www.gnu.org/licenses/>.\n\n/**\n * Commands helper for the Moodle tiny_ai plugin.\n *\n * @module      tiny_ai/commands\n * @copyright   2024, ISB Bayern\n * <AUTHOR> <PERSON>\n * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport {getButtonImage} from 'editor_tiny/utils';\nimport {\n    component,\n    toolbarButtonName,\n    selectionbarButtonName,\n    icon\n} from 'tiny_ai/common';\nimport {prefetchStrings} from 'core/prefetch';\nimport {getString} from 'core/str';\nimport {getContextId as getContextItemIdTinyCore} from 'editor_tiny/options';\nimport {constants} from 'tiny_ai/constants';\nimport {getUserId} from 'tiny_ai/options';\nimport EditorUtils from 'tiny_ai/editor_utils';\nimport * as Utils from 'tiny_ai/utils';\n\n\n/**\n * Get the setup function for the buttons.\n *\n * This is performed in an async function which ultimately returns the registration function as the\n * Tiny.AddOnManager.Add() function does not support async functions.\n *\n * @returns {function} The registration function to call within the Plugin.add function.\n */\nexport const getSetup = async() => {\n    prefetchStrings('tiny_ai', ['toolbarbuttontitle', 'selectionbarbuttontitle']);\n    const [\n        buttonImage,\n        toolbarButtonTitle,\n        selectionbarButtonTitle\n    ] = await Promise.all([\n        getButtonImage('icon', component),\n        getString('toolbarbuttontitle', 'tiny_ai'),\n        getString('selectionbarbuttontitle', 'tiny_ai')\n    ]);\n\n    const uniqid = Math.random().toString(16).slice(2);\n    await Utils.init(uniqid, constants.modalModes.editor);\n\n    return (editor) => {\n        // Register the Moodle SVG as an icon suitable for use as a TinyMCE toolbar button.\n        editor.ui.registry.addIcon(icon, buttonImage.html);\n\n        const contextId = getContextItemIdTinyCore(editor);\n        const editorUtils = new EditorUtils(uniqid, 'tiny_ai', contextId, getUserId(editor), editor);\n        Utils.setEditorUtils(uniqid, editorUtils);\n\n        // Register the AI Toolbar Button.\n        editor.ui.registry.addButton(toolbarButtonName, {\n            icon,\n            tooltip: toolbarButtonTitle,\n            onAction: async() => {\n                await injectSelectedElements(editor, Utils.getDatamanager(uniqid));\n                Utils.getEditorUtils(uniqid).displayDialogue();\n            }\n        });\n\n        // Register the menu item.\n        editor.ui.registry.addMenuItem(toolbarButtonName, {\n            icon,\n            text: toolbarButtonTitle,\n            onAction: async() => {\n                await injectSelectedElements(editor, Utils.getDatamanager(uniqid));\n                Utils.getEditorUtils(uniqid).displayDialogue();\n            }\n        });\n\n        editor.ui.registry.addButton(selectionbarButtonName, {\n            icon,\n            tooltip: selectionbarButtonTitle,\n            onAction: async() => {\n                await injectSelectedElements(editor, Utils.getDatamanager(uniqid));\n                Utils.getEditorUtils(uniqid).displayDialogue();\n            }\n        });\n    };\n};\n\nexport const injectSelectedElements = async(editor, datamanager) => {\n    const selectedEditorContentHtml = editor.selection.getContent({format: 'html'});\n    const parser = new DOMParser();\n    const editorDom = parser.parseFromString(selectedEditorContentHtml, 'text/html');\n    const images = editorDom.querySelectorAll('img');\n\n    if (images.length > 0 && images[0].src) {\n        // If there are more than one we just use the first one.\n        const image = images[0];\n        // This should work for both external and data urls.\n        const fetchResult = await fetch(image.src);\n        const data = await fetchResult.blob();\n        datamanager.setSelectionImg(data);\n    }\n    datamanager.setSelection(editor.selection.getContent());\n};\n"], "names": ["async", "buttonImage", "toolbarButtonTitle", "selectionbarButtonTitle", "Promise", "all", "component", "uniqid", "Math", "random", "toString", "slice", "Utils", "init", "constants", "modalModes", "editor", "ui", "registry", "addIcon", "icon", "html", "contextId", "editor<PERSON><PERSON><PERSON>", "EditorUtils", "setEditorUtils", "addButton", "toolbarButtonName", "tooltip", "onAction", "injectSelectedElements", "getDatamanager", "getEditorUtils", "displayDialogue", "addMenuItem", "text", "selectionbarButtonName", "datamanager", "selectedEditorContentHtml", "selection", "get<PERSON>ontent", "format", "images", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFromString", "querySelectorAll", "length", "src", "image", "fetchResult", "fetch", "data", "blob", "setSelectionImg", "setSelection"], "mappings": ";;;;;;;;ynCAgDwBA,wCACJ,UAAW,CAAC,qBAAsB,kCAE9CC,YACAC,mBACAC,+BACMC,QAAQC,IAAI,EAClB,yBAAe,OAAQC,oBACvB,kBAAU,qBAAsB,YAChC,kBAAU,0BAA2B,aAGnCC,OAASC,KAAKC,SAASC,SAAS,IAAIC,MAAM,gBAC1CC,MAAMC,KAAKN,OAAQO,qBAAUC,WAAWC,QAEtCA,SAEJA,OAAOC,GAAGC,SAASC,QAAQC,aAAMnB,YAAYoB,YAEvCC,WAAY,yBAAyBN,QACrCO,YAAc,IAAIC,sBAAYjB,OAAQ,UAAWe,WAAW,uBAAUN,QAASA,QACrFJ,MAAMa,eAAelB,OAAQgB,aAG7BP,OAAOC,GAAGC,SAASQ,UAAUC,0BAAmB,CAC5CP,KAAAA,aACAQ,QAAS1B,mBACT2B,SAAU7B,gBACA8B,uBAAuBd,OAAQJ,MAAMmB,eAAexB,SAC1DK,MAAMoB,eAAezB,QAAQ0B,qBAKrCjB,OAAOC,GAAGC,SAASgB,YAAYP,0BAAmB,CAC9CP,KAAAA,aACAe,KAAMjC,mBACN2B,SAAU7B,gBACA8B,uBAAuBd,OAAQJ,MAAMmB,eAAexB,SAC1DK,MAAMoB,eAAezB,QAAQ0B,qBAIrCjB,OAAOC,GAAGC,SAASQ,UAAUU,+BAAwB,CACjDhB,KAAAA,aACAQ,QAASzB,wBACT0B,SAAU7B,gBACA8B,uBAAuBd,OAAQJ,MAAMmB,eAAexB,SAC1DK,MAAMoB,eAAezB,QAAQ0B,6BAMhCH,uBAAyB9B,MAAMgB,OAAQqB,qBAC1CC,0BAA4BtB,OAAOuB,UAAUC,WAAW,CAACC,OAAQ,SAGjEC,QAFS,IAAIC,WACMC,gBAAgBN,0BAA2B,aAC3CO,iBAAiB,UAEtCH,OAAOI,OAAS,GAAKJ,OAAO,GAAGK,IAAK,OAE9BC,MAAQN,OAAO,GAEfO,kBAAoBC,MAAMF,MAAMD,KAChCI,WAAaF,YAAYG,OAC/Bf,YAAYgB,gBAAgBF,MAEhCd,YAAYiB,aAAatC,OAAOuB,UAAUC"}