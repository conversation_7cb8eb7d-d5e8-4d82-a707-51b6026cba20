{"version": 3, "file": "base.min.js", "sources": ["../../src/datahandler/base.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Mo<PERSON><PERSON> is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Simple data handler base class.\n *\n * All tiny_ai data handlers should inherit from this class.\n *\n * @module      tiny_ai/datahandler/base\n * @copyright   2024, ISB Bayern\n * <AUTHOR>\n * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\nexport default class {\n\n    uniqid = null;\n\n    constructor(uniqid) {\n        this.uniqid = uniqid;\n    }\n}\n"], "names": ["constructor", "uniqid"], "mappings": ";;;;;;;;;;;MA6BIA,YAAYC,gCAFH,2IAGAA,OAASA"}