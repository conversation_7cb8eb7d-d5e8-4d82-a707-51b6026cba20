{"version": 3, "file": "plugin.min.js", "sources": ["../src/plugin.js"], "sourcesContent": ["// This file is part of Moodle - https://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// <PERSON><PERSON><PERSON> is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <https://www.gnu.org/licenses/>.\n\n/**\n * Tiny tiny_ai for Moodle.\n *\n * @module      tiny_ai/plugin\n * @copyright   2024, ISB Bayern\n * <AUTHOR> <PERSON>\n * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport {getTinyMCE} from 'editor_tiny/loader';\nimport {getPluginMetadata} from 'editor_tiny/utils';\n\nimport {component, pluginName} from './common';\nimport {register as registerOptions} from './options';\nimport {getSetup as getCommandSetup} from './commands';\nimport * as Configuration from './configuration';\n\n// Setup the tiny_ai Plugin.\n// eslint-disable-next-line no-async-promise-executor\nexport default new Promise(async(resolve) => {\n    // Note: The PluginManager.add function does not support asynchronous configuration.\n    // Perform any asynchronous configuration here, and then call the PluginManager.add function.\n    const [\n        tinyMCE,\n        pluginMetadata,\n        setupCommands,\n    ] = await Promise.all([\n        getTinyMCE(),\n        getPluginMetadata(component, pluginName),\n        getCommandSetup(),\n    ]);\n\n    // Reminder: Any asynchronous code must be run before this point.\n    tinyMCE.PluginManager.add(pluginName, (editor) => {\n        // Register any options that your plugin has\n        registerOptions(editor);\n\n        // Setup any commands such as buttons, menu items, and so on.\n        setupCommands(editor);\n\n        // Return the pluginMetadata object. This is used by TinyMCE to display a help link for your plugin.\n        return pluginMetadata;\n    });\n\n    resolve([pluginName, Configuration]);\n});\n"], "names": ["Promise", "async", "tinyMCE", "pluginMetadata", "setupCommands", "all", "component", "pluginName", "Plugin<PERSON>anager", "add", "editor", "resolve", "Configuration"], "mappings": ";;;;;;;;kCAkCe,IAAIA,SAAQC,MAAAA,gBAInBC,QACAC,eACAC,qBACMJ,QAAQK,IAAI,EAClB,yBACA,4BAAkBC,kBAAWC,qBAC7B,0BAIJL,QAAQM,cAAcC,IAAIF,oBAAaG,+BAEnBA,QAGhBN,cAAcM,QAGPP,kBAGXQ,QAAQ,CAACJ,mBAAYK"}