{"version": 3, "file": "preferences.min.js", "sources": ["../../src/controllers/preferences.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// <PERSON><PERSON>le is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Controller for the main selection.\n *\n * @module      tiny_ai/controllers/preferences\n * @copyright   2024, ISB Bayern\n * <AUTHOR>\n * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport {constants} from 'tiny_ai/constants';\nimport SELECTORS from 'tiny_ai/selectors';\nimport BaseController from 'tiny_ai/controllers/base';\nimport {get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, get<PERSON><PERSON><PERSON><PERSON><PERSON>, get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, get<PERSON><PERSON><PERSON><PERSON><PERSON>} from 'tiny_ai/utils';\n\nexport default class extends BaseController {\n\n    async init() {\n        const modalFooter = document.querySelector(SELECTORS.modalFooter);\n        const backButton = modalFooter.querySelector('[data-action=\"back\"]');\n        const generateButton = modalFooter.querySelector('[data-action=\"generate\"]');\n\n        const [summarizeHandler, translateHandler, ttsHandler, imggenHandler, ittHandler] = [\n            getSummarizeHandler(this.uniqid),\n            getTranslateHandler(this.uniqid),\n            getTtsHandler(this.uniqid),\n            getImggenHandler(this.uniqid),\n            getIttHandler(this.uniqid)\n        ];\n\n        switch (this.datamanager.getCurrentTool()) {\n            case 'summarize':\n            case 'describe': {\n                summarizeHandler.setTool(this.datamanager.getCurrentTool());\n                const maxWordCountElement = this.baseElement.querySelector('[data-preference=\"maxWordCount\"]');\n                const languageTypeElement = this.baseElement.querySelector('[data-preference=\"languageType\"]');\n                summarizeHandler.setMaxWordCount(maxWordCountElement.querySelector('[data-dropdown=\"select\"]').dataset.value);\n                summarizeHandler.setLanguageType(languageTypeElement.querySelector('[data-dropdown=\"select\"]').dataset.value);\n                this.datamanager.getEventEmitterElement().addEventListener('textUpdated', async() => {\n                    if (!['summarize', 'describe'].includes(this.datamanager.currentTool)) {\n                        // We are registering multiple listeners pretty much doing the same. So we need to make sure they\n                        // don't interfere with each other.\n                        return;\n                    }\n                    const currentPromptSummarize = await summarizeHandler.getPrompt(this.datamanager.getCurrentText());\n                    this.datamanager.setCurrentPrompt(currentPromptSummarize);\n                });\n                this.datamanager.setCurrentText(this.datamanager.getSelectionText());\n                maxWordCountElement.addEventListener('dropdownSelectionUpdated', async(event) => {\n                    summarizeHandler.setMaxWordCount(event.detail.newValue);\n                    const currentPrompt = await summarizeHandler.getPrompt(this.datamanager.getCurrentText());\n                    this.datamanager.setCurrentPrompt(currentPrompt);\n                });\n                languageTypeElement.addEventListener('dropdownSelectionUpdated', async(event) => {\n                    summarizeHandler.setLanguageType(event.detail.newValue);\n                    const currentPrompt = await summarizeHandler.getPrompt(this.datamanager.getCurrentText());\n                    this.datamanager.setCurrentPrompt(currentPrompt);\n                });\n                break;\n            }\n            case 'translate': {\n                const targetLanguageElement = this.baseElement.querySelector('[data-preference=\"targetLanguage\"]');\n                translateHandler.setTargetLanguage(targetLanguageElement.querySelector('[data-dropdown=\"select\"]').dataset.value);\n                this.datamanager.getEventEmitterElement().addEventListener('textUpdated', async() => {\n                    if (this.datamanager.currentTool !== 'translate') {\n                        return;\n                    }\n                    const currentPromptTranslate = await translateHandler.getPrompt(this.datamanager.getCurrentText());\n                    this.datamanager.setCurrentPrompt(currentPromptTranslate);\n                });\n                this.datamanager.setCurrentText(this.datamanager.getSelectionText());\n                targetLanguageElement.addEventListener('dropdownSelectionUpdated', async(event) => {\n                    translateHandler.setTargetLanguage(event.detail.newValue);\n                    const currentPromptTranslate = await translateHandler.getPrompt(this.datamanager.getCurrentText());\n                    this.datamanager.setCurrentPrompt(currentPromptTranslate);\n                });\n                break;\n            }\n            case 'tts': {\n                const ttsTargetLanguageElement = this.baseElement.querySelector('[data-preference=\"targetLanguage\"]');\n                const voiceElement = this.baseElement.querySelector('[data-preference=\"voice\"]');\n                const genderElement = this.baseElement.querySelector('[data-preference=\"gender\"]');\n                if (ttsTargetLanguageElement) {\n                    ttsHandler.setTargetLanguage(ttsTargetLanguageElement.querySelector('[data-dropdown=\"select\"]').dataset.value);\n                    ttsTargetLanguageElement.addEventListener('dropdownSelectionUpdated', event => {\n                        ttsHandler.setTargetLanguage(event.detail.newValue);\n                        this.datamanager.setCurrentOptions(ttsHandler.getOptions());\n                    });\n                }\n                if (voiceElement) {\n                    ttsHandler.setVoice(voiceElement.querySelector('[data-dropdown=\"select\"]').dataset.value);\n                    voiceElement.addEventListener('dropdownSelectionUpdated', event => {\n                        ttsHandler.setVoice(event.detail.newValue);\n                        this.datamanager.setCurrentOptions(ttsHandler.getOptions());\n                    });\n                }\n                if (genderElement) {\n                    ttsHandler.setGender(genderElement.querySelector('[data-dropdown=\"select\"]').dataset.value);\n                    genderElement.addEventListener('dropdownSelectionUpdated', event => {\n                        ttsHandler.setGender(event.detail.newValue);\n                        this.datamanager.setCurrentOptions(ttsHandler.getOptions());\n                    });\n                }\n\n                this.datamanager.getEventEmitterElement().addEventListener('textUpdated', async() => {\n                    if (this.datamanager.currentTool !== 'tts') {\n                        return;\n                    }\n                    const currentPromptTts = await ttsHandler.getPrompt(this.datamanager.getCurrentText());\n                    this.datamanager.setCurrentPrompt(currentPromptTts);\n                });\n\n                this.datamanager.setCurrentText(ttsHandler.getPrompt(this.datamanager.getSelectionText()));\n                this.datamanager.setCurrentOptions(ttsHandler.getOptions());\n                break;\n            }\n            case 'imggen': {\n                const sizesElement = this.baseElement.querySelector('[data-preference=\"sizes\"]');\n\n                if (sizesElement) {\n                    imggenHandler.setSize(sizesElement.querySelector('[data-dropdown=\"select\"]').dataset.value);\n                    sizesElement.addEventListener('dropdownSelectionUpdated', event => {\n                        imggenHandler.setSize(event.detail.newValue);\n                        this.datamanager.setCurrentOptions(imggenHandler.getOptions());\n                    });\n                }\n                this.datamanager.setCurrentPrompt(this.datamanager.getSelectionText());\n                this.datamanager.setCurrentOptions(imggenHandler.getOptions());\n                break;\n            }\n            case 'describeimg':\n            case 'imagetotext': {\n                const fileUploadArea = this.baseElement.querySelector('[data-preference=\"fileupload\"]');\n                if (fileUploadArea) {\n                    this.datamanager.getEventEmitterElement().addEventListener('fileUploaded', async(event) => {\n                        this.datamanager.setCurrentFile(event.detail.newFile);\n                        this.datamanager.setCurrentOptions(ittHandler.getOptions());\n                    });\n                }\n                this.datamanager.setCurrentPrompt(ittHandler.getPrompt(this.datamanager.getCurrentTool()));\n                this.datamanager.setCurrentFile(null);\n                break;\n            }\n        }\n\n        if (backButton) {\n            backButton.addEventListener('click', async() => {\n                await this.renderer.renderStart(constants.modalModes.selection);\n            });\n        }\n\n        if (generateButton) {\n            generateButton.addEventListener('click', async() => {\n                const result = await this.generateAiAnswer();\n                if (result === null) {\n                    return;\n                }\n                await this.renderer.renderSuggestion();\n            });\n        }\n    }\n}\n"], "names": ["BaseController", "modalFooter", "document", "querySelector", "SELECTORS", "backButton", "generateButton", "summarize<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "ttsHandler", "imggenHandler", "ittHandler", "this", "uniqid", "datamanager", "getCurrentTool", "setTool", "maxWordCountElement", "baseElement", "languageTypeElement", "setMaxWordCount", "dataset", "value", "setLanguageType", "getEventEmitterElement", "addEventListener", "async", "includes", "currentTool", "currentPromptSummarize", "getPrompt", "getCurrentText", "setCurrentPrompt", "setCurrentText", "getSelectionText", "event", "detail", "newValue", "currentPrompt", "targetLanguageElement", "setTargetLanguage", "currentPromptTranslate", "ttsTargetLanguageElement", "voiceElement", "genderElement", "setCurrentOptions", "getOptions", "setVoice", "setGender", "currentPromptTts", "sizesElement", "setSize", "setCurrentFile", "newFile", "renderer", "renderStart", "constants", "modalModes", "selection", "generateAiAnswer", "renderSuggestion"], "mappings": ";;;;;;;;8LA6B6BA,iCAGfC,YAAcC,SAASC,cAAcC,mBAAUH,aAC/CI,WAAaJ,YAAYE,cAAc,wBACvCG,eAAiBL,YAAYE,cAAc,6BAE1CI,iBAAkBC,iBAAkBC,WAAYC,cAAeC,YAAc,EAChF,8BAAoBC,KAAKC,SACzB,8BAAoBD,KAAKC,SACzB,wBAAcD,KAAKC,SACnB,2BAAiBD,KAAKC,SACtB,wBAAcD,KAAKC,gBAGfD,KAAKE,YAAYC,sBAChB,gBACA,YACDR,iBAAiBS,QAAQJ,KAAKE,YAAYC,wBACpCE,oBAAsBL,KAAKM,YAAYf,cAAc,oCACrDgB,oBAAsBP,KAAKM,YAAYf,cAAc,oCAC3DI,iBAAiBa,gBAAgBH,oBAAoBd,cAAc,4BAA4BkB,QAAQC,OACvGf,iBAAiBgB,gBAAgBJ,oBAAoBhB,cAAc,4BAA4BkB,QAAQC,YAClGR,YAAYU,yBAAyBC,iBAAiB,eAAeC,cACjE,CAAC,YAAa,YAAYC,SAASf,KAAKE,YAAYc,0BAKnDC,6BAA+BtB,iBAAiBuB,UAAUlB,KAAKE,YAAYiB,uBAC5EjB,YAAYkB,iBAAiBH,gCAEjCf,YAAYmB,eAAerB,KAAKE,YAAYoB,oBACjDjB,oBAAoBQ,iBAAiB,4BAA4BC,MAAAA,QAC7DnB,iBAAiBa,gBAAgBe,MAAMC,OAAOC,gBACxCC,oBAAsB/B,iBAAiBuB,UAAUlB,KAAKE,YAAYiB,uBACnEjB,YAAYkB,iBAAiBM,kBAEtCnB,oBAAoBM,iBAAiB,4BAA4BC,MAAAA,QAC7DnB,iBAAiBgB,gBAAgBY,MAAMC,OAAOC,gBACxCC,oBAAsB/B,iBAAiBuB,UAAUlB,KAAKE,YAAYiB,uBACnEjB,YAAYkB,iBAAiBM,4BAIrC,mBACKC,sBAAwB3B,KAAKM,YAAYf,cAAc,sCAC7DK,iBAAiBgC,kBAAkBD,sBAAsBpC,cAAc,4BAA4BkB,QAAQC,YACtGR,YAAYU,yBAAyBC,iBAAiB,eAAeC,aACjC,cAAjCd,KAAKE,YAAYc,yBAGfa,6BAA+BjC,iBAAiBsB,UAAUlB,KAAKE,YAAYiB,uBAC5EjB,YAAYkB,iBAAiBS,gCAEjC3B,YAAYmB,eAAerB,KAAKE,YAAYoB,oBACjDK,sBAAsBd,iBAAiB,4BAA4BC,MAAAA,QAC/DlB,iBAAiBgC,kBAAkBL,MAAMC,OAAOC,gBAC1CI,6BAA+BjC,iBAAiBsB,UAAUlB,KAAKE,YAAYiB,uBAC5EjB,YAAYkB,iBAAiBS,qCAIrC,aACKC,yBAA2B9B,KAAKM,YAAYf,cAAc,sCAC1DwC,aAAe/B,KAAKM,YAAYf,cAAc,6BAC9CyC,cAAgBhC,KAAKM,YAAYf,cAAc,8BACjDuC,2BACAjC,WAAW+B,kBAAkBE,yBAAyBvC,cAAc,4BAA4BkB,QAAQC,OACxGoB,yBAAyBjB,iBAAiB,4BAA4BU,QAClE1B,WAAW+B,kBAAkBL,MAAMC,OAAOC,eACrCvB,YAAY+B,kBAAkBpC,WAAWqC,kBAGlDH,eACAlC,WAAWsC,SAASJ,aAAaxC,cAAc,4BAA4BkB,QAAQC,OACnFqB,aAAalB,iBAAiB,4BAA4BU,QACtD1B,WAAWsC,SAASZ,MAAMC,OAAOC,eAC5BvB,YAAY+B,kBAAkBpC,WAAWqC,kBAGlDF,gBACAnC,WAAWuC,UAAUJ,cAAczC,cAAc,4BAA4BkB,QAAQC,OACrFsB,cAAcnB,iBAAiB,4BAA4BU,QACvD1B,WAAWuC,UAAUb,MAAMC,OAAOC,eAC7BvB,YAAY+B,kBAAkBpC,WAAWqC,uBAIjDhC,YAAYU,yBAAyBC,iBAAiB,eAAeC,aACjC,QAAjCd,KAAKE,YAAYc,yBAGfqB,uBAAyBxC,WAAWqB,UAAUlB,KAAKE,YAAYiB,uBAChEjB,YAAYkB,iBAAiBiB,0BAGjCnC,YAAYmB,eAAexB,WAAWqB,UAAUlB,KAAKE,YAAYoB,0BACjEpB,YAAY+B,kBAAkBpC,WAAWqC,wBAG7C,gBACKI,aAAetC,KAAKM,YAAYf,cAAc,6BAEhD+C,eACAxC,cAAcyC,QAAQD,aAAa/C,cAAc,4BAA4BkB,QAAQC,OACrF4B,aAAazB,iBAAiB,4BAA4BU,QACtDzB,cAAcyC,QAAQhB,MAAMC,OAAOC,eAC9BvB,YAAY+B,kBAAkBnC,cAAcoC,uBAGpDhC,YAAYkB,iBAAiBpB,KAAKE,YAAYoB,yBAC9CpB,YAAY+B,kBAAkBnC,cAAcoC,wBAGhD,kBACA,cACsBlC,KAAKM,YAAYf,cAAc,wCAE7CW,YAAYU,yBAAyBC,iBAAiB,gBAAgBC,MAAAA,aAClEZ,YAAYsC,eAAejB,MAAMC,OAAOiB,cACxCvC,YAAY+B,kBAAkBlC,WAAWmC,sBAGjDhC,YAAYkB,iBAAiBrB,WAAWmB,UAAUlB,KAAKE,YAAYC,wBACnED,YAAYsC,eAAe,YAKpC/C,YACAA,WAAWoB,iBAAiB,SAASC,gBAC3Bd,KAAK0C,SAASC,YAAYC,qBAAUC,WAAWC,cAIzDpD,gBACAA,eAAemB,iBAAiB,SAASC,UAEtB,aADMd,KAAK+C,0BAIpB/C,KAAK0C,SAASM"}