{"version": 3, "file": "start.min.js", "sources": ["../../src/controllers/start.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Mo<PERSON>le is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Controller for the main selection.\n *\n * @module      tiny_ai/controllers/start\n * @copyright   2024, ISB Bayern\n * <AUTHOR>\n * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport BaseController from 'tiny_ai/controllers/base';\nimport {getStartHandler} from 'tiny_ai/utils';\nimport {errorAlert} from 'tiny_ai/utils';\n// We unfortunately need jquery for tooltip handling.\nimport $ from 'jquery';\n\nexport default class extends BaseController {\n\n    async init() {\n        if (!this.baseElement) {\n            // In rare cases (display error messages etc.) we do not have a correct modal, so there is nothing to do here.\n            return;\n        }\n        const summarizeButton = this.baseElement.querySelector('[data-action=\"loadsummarize\"]');\n        const translateButton = this.baseElement.querySelector('[data-action=\"loadtranslate\"]');\n        const describeButton = this.baseElement.querySelector('[data-action=\"loaddescribe\"]');\n        const ttsButton = this.baseElement.querySelector('[data-action=\"loadtts\"]');\n        const imggenButton = this.baseElement.querySelector('[data-action=\"loadimggen\"]');\n        const freePromptButton = this.baseElement.querySelector('[data-action=\"loadfreeprompt\"]');\n        const describeimgButton = this.baseElement.querySelector('[data-action=\"loaddescribeimg\"]');\n        const imagetotextButton = this.baseElement.querySelector('[data-action=\"loadimagetotext\"]');\n\n        const startHandler = getStartHandler(this.uniqid);\n\n        if (!(await startHandler.isTinyAiDisabled())) {\n            if (window.matchMedia(\"(pointer: coarse)\").matches) {\n                // If we have a touch device, we need to manually trigger the tooltips by touching the cards.\n                document.querySelectorAll('.tiny_ai-card-button.disabled').forEach(button => {\n                    button.parentElement.addEventListener(\n                        'click', async() => {\n                            $(button).tooltip('toggle');\n                        });\n                });\n            }\n        }\n\n        if (summarizeButton) {\n            summarizeButton.addEventListener('click', async() => {\n                this.datamanager.setCurrentTool('summarize');\n                await this.renderer.renderSummarize();\n            });\n        }\n        if (translateButton) {\n            translateButton.addEventListener('click', async() => {\n                this.datamanager.setCurrentTool('translate');\n                await this.renderer.renderTranslate();\n            });\n        }\n        if (describeButton) {\n            describeButton.addEventListener('click', async() => {\n                this.datamanager.setCurrentTool('describe');\n                await this.renderer.renderDescribe();\n            });\n        }\n        if (ttsButton) {\n            ttsButton.addEventListener('click', async() => {\n                this.datamanager.setCurrentTool('tts');\n                await this.renderer.renderTts();\n            });\n        }\n        if (imggenButton) {\n            imggenButton.addEventListener('click', async() => {\n                this.datamanager.setCurrentTool('imggen');\n                await this.renderer.renderImggen();\n            });\n        }\n        if (describeimgButton) {\n            describeimgButton.addEventListener('click', async() => {\n                this.datamanager.setCurrentTool('describeimg');\n                await this.renderer.renderDescribeimg();\n            });\n        }\n        if (imagetotextButton) {\n            imagetotextButton.addEventListener('click', async() => {\n                this.datamanager.setCurrentTool('imagetotext');\n                await this.renderer.renderImagetotext();\n            });\n        }\n        if (freePromptButton) {\n            if (!freePromptButton.classList.contains('disabled')) {\n                freePromptButton.addEventListener('click', async() => {\n                    this.datamanager.setCurrentTool('freeprompt');\n                    this.datamanager.setCurrentPrompt(this.baseElement.querySelector('[data-type=\"freepromptinput\"]').value);\n                    const result = await this.generateAiAnswer();\n                    if (result === null) {\n                        return;\n                    }\n                    await this.renderer.renderSuggestion();\n                });\n            } else {\n                if (!(await startHandler.isTinyAiDisabled())) {\n                    freePromptButton.addEventListener('click', async() => {\n                        await errorAlert(startHandler.isToolDisabled('freeprompt', this.editorUtils.getMode()));\n                    });\n                }\n            }\n        }\n    }\n}\n"], "names": ["BaseController", "this", "baseElement", "summarize<PERSON><PERSON><PERSON>", "querySelector", "translateButton", "describeButton", "ttsButton", "imggenButton", "freePromptButton", "describeimgButton", "imagetotextButton", "startHandler", "uniqid", "isTinyAiDisabled", "window", "matchMedia", "matches", "document", "querySelectorAll", "for<PERSON>ach", "button", "parentElement", "addEventListener", "async", "tooltip", "datamanager", "setCurrentTool", "renderer", "renderSummarize", "renderTranslate", "renderDescribe", "renderTts", "renderImggen", "renderDescribeimg", "renderImagetotext", "classList", "contains", "isToolDisabled", "editor<PERSON><PERSON><PERSON>", "getMode", "setCurrentPrompt", "value", "generateAiAnswer", "renderSuggestion"], "mappings": ";;;;;;;;wLA8B6BA,+BAGhBC,KAAKC,yBAIJC,gBAAkBF,KAAKC,YAAYE,cAAc,iCACjDC,gBAAkBJ,KAAKC,YAAYE,cAAc,iCACjDE,eAAiBL,KAAKC,YAAYE,cAAc,gCAChDG,UAAYN,KAAKC,YAAYE,cAAc,2BAC3CI,aAAeP,KAAKC,YAAYE,cAAc,8BAC9CK,iBAAmBR,KAAKC,YAAYE,cAAc,kCAClDM,kBAAoBT,KAAKC,YAAYE,cAAc,mCACnDO,kBAAoBV,KAAKC,YAAYE,cAAc,mCAEnDQ,cAAe,0BAAgBX,KAAKY,cAE9BD,aAAaE,oBACjBC,OAAOC,WAAW,qBAAqBC,SAEvCC,SAASC,iBAAiB,iCAAiCC,SAAQC,SAC/DA,OAAOC,cAAcC,iBACjB,SAASC,8BACHH,QAAQI,QAAQ,gBAMlCtB,iBACAA,gBAAgBoB,iBAAiB,SAASC,eACjCE,YAAYC,eAAe,mBAC1B1B,KAAK2B,SAASC,qBAGxBxB,iBACAA,gBAAgBkB,iBAAiB,SAASC,eACjCE,YAAYC,eAAe,mBAC1B1B,KAAK2B,SAASE,qBAGxBxB,gBACAA,eAAeiB,iBAAiB,SAASC,eAChCE,YAAYC,eAAe,kBAC1B1B,KAAK2B,SAASG,oBAGxBxB,WACAA,UAAUgB,iBAAiB,SAASC,eAC3BE,YAAYC,eAAe,aAC1B1B,KAAK2B,SAASI,eAGxBxB,cACAA,aAAae,iBAAiB,SAASC,eAC9BE,YAAYC,eAAe,gBAC1B1B,KAAK2B,SAASK,kBAGxBvB,mBACAA,kBAAkBa,iBAAiB,SAASC,eACnCE,YAAYC,eAAe,qBAC1B1B,KAAK2B,SAASM,uBAGxBvB,mBACAA,kBAAkBY,iBAAiB,SAASC,eACnCE,YAAYC,eAAe,qBAC1B1B,KAAK2B,SAASO,uBAGxB1B,mBACKA,iBAAiB2B,UAAUC,SAAS,kBAWzBzB,aAAaE,oBACrBL,iBAAiBc,iBAAiB,SAASC,gBACjC,qBAAWZ,aAAa0B,eAAe,aAAcrC,KAAKsC,YAAYC,eAZpF/B,iBAAiBc,iBAAiB,SAASC,eAClCE,YAAYC,eAAe,mBAC3BD,YAAYe,iBAAiBxC,KAAKC,YAAYE,cAAc,iCAAiCsC,OAEnF,aADMzC,KAAK0C,0BAIpB1C,KAAK2B,SAASgB"}