{"version": 3, "file": "options.min.js", "sources": ["../src/options.js"], "sourcesContent": ["// This file is part of Moodle - https://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Mo<PERSON>le is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <https://www.gnu.org/licenses/>.\n\n/**\n * Options helper for the Moodle tiny_ai plugin.\n *\n * @module      tiny_ai/options\n * @copyright   2024, ISB Bayern\n * <AUTHOR> <PERSON>\n * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport {getPluginOptionName} from 'editor_tiny/options';\nimport {pluginName} from './common';\n\n// Helper variables for the option names.\nconst userId = getPluginOptionName(pluginName, 'userId');\n\n/**\n * Options registration function.\n *\n * @param {tinyMCE} editor\n */\nexport const register = (editor) => {\n    const registerOption = editor.options.register;\n\n    // For each option, register it with the editor.\n    // Valid type are defined in https://www.tiny.cloud/docs/tinymce/6/apis/tinymce.editoroptions/\n    registerOption(userId, {\n        processor: 'number',\n    });\n};\n\n/**\n * Fetch the my_custom_option1 value for this editor instance.\n *\n * @param {tinyMCE} editor The editor instance to fetch the value for\n * @returns {object} The value of the my_custom_option1 option\n */\nexport const getUserId = (editor) => editor.options.get(userId);\n"], "names": ["userId", "pluginName", "editor", "registerOption", "options", "register", "processor", "get"], "mappings": ";;;;;;;;;MA4BMA,QAAS,gCAAoBC,mBAAY,4BAOtBC,UAKrBC,EAJuBD,OAAOE,QAAQC,UAIvBL,OAAQ,CACnBM,UAAW,+BAUOJ,QAAWA,OAAOE,QAAQG,IAAIP"}