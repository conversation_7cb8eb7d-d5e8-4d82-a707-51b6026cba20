{"version": 3, "file": "tts.min.js", "sources": ["../../src/datahandler/tts.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Moodle is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\nimport * as AiConfig from 'local_ai_manager/config';\nimport * as BasedataHandler from 'tiny_ai/datahandler/basedata';\nimport BaseHandler from 'tiny_ai/datahandler/base';\nimport Config from 'core/config';\n\n/**\n * Tiny AI data manager.\n *\n * @module      tiny_ai/datahandler/tts\n * @copyright   2024, ISB Bayern\n * <AUTHOR>\n * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nexport default class extends BaseHandler {\n\n    ttsOptions = null;\n\n    targetLanguage = null;\n    voice = null;\n    gender = null;\n\n    async getTargetLanguageOptions() {\n        await this.loadTtsOptions();\n        return this.ttsOptions.languages;\n    }\n\n    async getVoiceOptions() {\n        await this.loadTtsOptions();\n        return this.ttsOptions.voices;\n    }\n\n    async getGenderOptions() {\n        await this.loadTtsOptions();\n        return this.ttsOptions.gender;\n    }\n\n    setTargetLanguage(targetLanguage) {\n        this.targetLanguage = targetLanguage;\n    }\n\n    setVoice(voice) {\n        this.voice = voice;\n    }\n\n    setGender(gender) {\n        this.gender = gender;\n    }\n\n    getOptions() {\n        if (this.targetLanguage === null && this.voice === null && this.gender === null) {\n            return {};\n        }\n        const options = {};\n        if (this.targetLanguage) {\n            options.languages = [this.targetLanguage];\n        }\n        if (this.voice) {\n            options.voices = [this.voice];\n        }\n        if (this.gender) {\n            options.gender = [this.gender];\n        }\n        return options;\n    }\n\n    getPrompt(currentText) {\n        return currentText;\n    }\n\n    async loadTtsOptions() {\n        if (this.ttsOptions === null) {\n            const fetchedOptions = await AiConfig.getPurposeOptions('tts');\n            this.ttsOptions = JSON.parse(fetchedOptions.options);\n        }\n    }\n\n    /**\n     * Get the rendering context.\n     */\n    async getTemplateContext() {\n        const context = {\n            modalHeadline: BasedataHandler.getTinyAiString('tts_headline'),\n            showIcon: true,\n        };\n\n        const modalDropdowns = [];\n\n        const targetLanguageOptions = await this.getTargetLanguageOptions();\n        if (targetLanguageOptions !== null && Object.keys(targetLanguageOptions).length > 0) {\n            const targetLanguageDropdownContext = {};\n            targetLanguageDropdownContext.preference = 'targetLanguage';\n            let indexOfLanguageOption = 0;\n            const matchingEntry = targetLanguageOptions.map(entry => entry.key.startsWith(Config.language));\n\n            if (matchingEntry.length > 0) {\n                // Language keys are of the form de-DE, so we check, if current user's language starts with same language code.\n                indexOfLanguageOption = targetLanguageOptions.findIndex(value => value.key.startsWith(Config.language));\n            }\n            targetLanguageDropdownContext.dropdownDefault = targetLanguageOptions[indexOfLanguageOption].displayname;\n            targetLanguageDropdownContext.dropdownDefaultValue = targetLanguageOptions[indexOfLanguageOption].key;\n            targetLanguageDropdownContext.dropdownDescription = BasedataHandler.getTinyAiString('targetlanguage');\n            const targetLanguageDropdownOptions = [];\n            targetLanguageOptions.forEach(option => {\n                targetLanguageDropdownOptions.push({\n                    optionValue: option.key,\n                    optionLabel: option.displayname,\n                });\n            });\n            targetLanguageDropdownContext.dropdownOptions = targetLanguageDropdownOptions;\n            modalDropdowns.push(targetLanguageDropdownContext);\n        }\n\n        const voiceOptions = await this.getVoiceOptions();\n        if (voiceOptions !== null && Object.keys(voiceOptions).length > 0) {\n            const voiceDropdownContext = {};\n            voiceDropdownContext.preference = 'voice';\n            voiceDropdownContext.dropdownDefault = voiceOptions[0].displayname;\n            voiceDropdownContext.dropdownDefaultValue = voiceOptions[0].key;\n            voiceDropdownContext.dropdownDescription = BasedataHandler.getTinyAiString('voice');\n            const voiceDropdownOptions = [];\n            voiceOptions.forEach(option => {\n                voiceDropdownOptions.push({\n                    optionValue: option.key,\n                    optionLabel: option.displayname,\n                });\n            });\n            voiceDropdownContext.dropdownOptions = voiceDropdownOptions;\n            modalDropdowns.push(voiceDropdownContext);\n        }\n\n        const genderOptions = await this.getGenderOptions();\n        if (genderOptions !== null && Object.keys(genderOptions).length > 0) {\n            const genderDropdownContext = {};\n            genderDropdownContext.preference = 'gender';\n            genderDropdownContext.dropdownDefault = genderOptions[0].displayname;\n            genderDropdownContext.dropdownDefaultValue = genderOptions[0].key;\n            genderDropdownContext.dropdownDescription = BasedataHandler.getTinyAiString('gender');\n            const genderDropdownOptions = [];\n            genderOptions.forEach(option => {\n                genderDropdownOptions.push({\n                    optionValue: option.key,\n                    optionLabel: option.displayname,\n                });\n            });\n            genderDropdownContext.dropdownOptions = genderDropdownOptions;\n            modalDropdowns.push(genderDropdownContext);\n        }\n        /*\n        TODO CHECK IF STILL NEEDED\n        if (tool === 'audiogen') {\n            // In the audiogen view the dropdowns are at the bottom, so we need to make the dropdowns dropup instead of dropdown.\n            modalDropdowns.forEach(dropdownContext => {\n                dropdownContext.dropup = true;\n            });\n        }\n        */\n\n        Object.assign(context, {\n            modalDropdowns: modalDropdowns\n        });\n\n        Object.assign(context, BasedataHandler.getShowPromptButtonContext(false));\n\n        /*\n        TODO CHECK IF STILL NEEDED\n        if (tool === 'audiogen') {\n            // Overwrite some prompt textarea specific attributes.\n            context.collapsed = false;\n            context.placeholder = BasedataHandler.getTinyAiString('audiogen_placeholder');\n        }\n        */\n        Object.assign(context, BasedataHandler.getBackAndGenerateButtonContext());\n        return context;\n    }\n}\n"], "names": ["BaseHandler", "this", "loadTtsOptions", "ttsOptions", "languages", "voices", "gender", "setTargetLanguage", "targetLanguage", "setVoice", "voice", "setGender", "getOptions", "options", "getPrompt", "currentText", "fetchedOptions", "AiConfig", "getPurposeOptions", "JSON", "parse", "context", "modalHeadline", "BasedataHandler", "getTinyAiString", "showIcon", "modalDropdowns", "targetLanguageOptions", "getTargetLanguageOptions", "Object", "keys", "length", "targetLanguageDropdownContext", "indexOfLanguageOption", "map", "entry", "key", "startsWith", "Config", "language", "findIndex", "value", "dropdownDefault", "displayname", "dropdownDefaultValue", "dropdownDescription", "targetLanguageDropdownOptions", "for<PERSON>ach", "option", "push", "optionValue", "optionLabel", "dropdownOptions", "voiceOptions", "getVoiceOptions", "voiceDropdownContext", "voiceDropdownOptions", "genderOptions", "getGenderOptions", "genderDropdownContext", "genderDropdownOptions", "assign", "getShowPromptButtonContext", "getBackAndGenerateButtonContext"], "mappings": ";;;;;;;;8RA6B6BA,kFAEZ,4CAEI,mCACT,oCACC,oDAGCC,KAAKC,iBACJD,KAAKE,WAAWC,+CAIjBH,KAAKC,iBACJD,KAAKE,WAAWE,6CAIjBJ,KAAKC,iBACJD,KAAKE,WAAWG,OAG3BC,kBAAkBC,qBACTA,eAAiBA,eAG1BC,SAASC,YACAA,MAAQA,MAGjBC,UAAUL,aACDA,OAASA,OAGlBM,gBACgC,OAAxBX,KAAKO,gBAA0C,OAAfP,KAAKS,OAAkC,OAAhBT,KAAKK,aACrD,SAELO,QAAU,UACZZ,KAAKO,iBACLK,QAAQT,UAAY,CAACH,KAAKO,iBAE1BP,KAAKS,QACLG,QAAQR,OAAS,CAACJ,KAAKS,QAEvBT,KAAKK,SACLO,QAAQP,OAAS,CAACL,KAAKK,SAEpBO,QAGXC,UAAUC,oBACCA,sCAIiB,OAApBd,KAAKE,WAAqB,OACpBa,qBAAuBC,SAASC,kBAAkB,YACnDf,WAAagB,KAAKC,MAAMJ,eAAeH,2CAQ1CQ,QAAU,CACZC,cAAeC,gBAAgBC,gBAAgB,gBAC/CC,UAAU,GAGRC,eAAiB,GAEjBC,4BAA8B1B,KAAK2B,8BACX,OAA1BD,uBAAkCE,OAAOC,KAAKH,uBAAuBI,OAAS,EAAG,OAC3EC,8BAAgC,CACtCA,WAA2C,sBACvCC,sBAAwB,EACNN,sBAAsBO,KAAIC,OAASA,MAAMC,IAAIC,WAAWC,iBAAOC,YAEnER,OAAS,IAEvBE,sBAAwBN,sBAAsBa,WAAUC,OAASA,MAAML,IAAIC,WAAWC,iBAAOC,aAEjGP,8BAA8BU,gBAAkBf,sBAAsBM,uBAAuBU,YAC7FX,8BAA8BY,qBAAuBjB,sBAAsBM,uBAAuBG,IAClGJ,8BAA8Ba,oBAAsBtB,gBAAgBC,gBAAgB,wBAC9EsB,8BAAgC,GACtCnB,sBAAsBoB,SAAQC,SAC1BF,8BAA8BG,KAAK,CAC/BC,YAAaF,OAAOZ,IACpBe,YAAaH,OAAOL,iBAG5BX,8BAA8BoB,gBAAkBN,8BAChDpB,eAAeuB,KAAKjB,qCAGlBqB,mBAAqBpD,KAAKqD,qBACX,OAAjBD,cAAyBxB,OAAOC,KAAKuB,cAActB,OAAS,EAAG,OACzDwB,qBAAuB,CAC7BA,WAAkC,SAClCA,qBAAqBb,gBAAkBW,aAAa,GAAGV,YACvDY,qBAAqBX,qBAAuBS,aAAa,GAAGjB,IAC5DmB,qBAAqBV,oBAAsBtB,gBAAgBC,gBAAgB,eACrEgC,qBAAuB,GAC7BH,aAAaN,SAAQC,SACjBQ,qBAAqBP,KAAK,CACtBC,YAAaF,OAAOZ,IACpBe,YAAaH,OAAOL,iBAG5BY,qBAAqBH,gBAAkBI,qBACvC9B,eAAeuB,KAAKM,4BAGlBE,oBAAsBxD,KAAKyD,sBACX,OAAlBD,eAA0B5B,OAAOC,KAAK2B,eAAe1B,OAAS,EAAG,OAC3D4B,sBAAwB,CAC9BA,WAAmC,UACnCA,sBAAsBjB,gBAAkBe,cAAc,GAAGd,YACzDgB,sBAAsBf,qBAAuBa,cAAc,GAAGrB,IAC9DuB,sBAAsBd,oBAAsBtB,gBAAgBC,gBAAgB,gBACtEoC,sBAAwB,GAC9BH,cAAcV,SAAQC,SAClBY,sBAAsBX,KAAK,CACvBC,YAAaF,OAAOZ,IACpBe,YAAaH,OAAOL,iBAG5BgB,sBAAsBP,gBAAkBQ,sBACxClC,eAAeuB,KAAKU,8BAYxB9B,OAAOgC,OAAOxC,QAAS,CACnBK,eAAgBA,iBAGpBG,OAAOgC,OAAOxC,QAASE,gBAAgBuC,4BAA2B,IAUlEjC,OAAOgC,OAAOxC,QAASE,gBAAgBwC,mCAChC1C"}