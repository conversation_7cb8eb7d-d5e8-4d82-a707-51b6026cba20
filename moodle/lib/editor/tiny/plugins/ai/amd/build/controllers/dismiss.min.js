define("tiny_ai/controllers/dismiss",["exports","tiny_ai/controllers/base"],(function(_exports,_base){var obj;
/**
   * Controller for dismiss page.
   *
   * @module      tiny_ai/controllers/dismiss
   * @copyright   2024, ISB Bayern
   * <AUTHOR>
   * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
   */Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0,_base=(obj=_base)&&obj.__esModule?obj:{default:obj};class _default extends _base.default{async init(){const cancelButton=this.baseElement.querySelector('[data-action="canceldismiss"]'),dismissButton=this.baseElement.querySelector('[data-action="dismiss"]');cancelButton&&cancelButton.addEventListener("click",(async()=>{await this.renderer.renderSuggestion()})),dismissButton&&dismissButton.addEventListener("click",(async()=>{await this.callRendererFunction()}))}}return _exports.default=_default,_exports.default}));

//# sourceMappingURL=dismiss.min.js.map