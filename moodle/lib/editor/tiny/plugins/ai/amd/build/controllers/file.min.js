define("tiny_ai/controllers/file",["exports","tiny_ai/utils","core/templates","tiny_ai/selectors","tiny_ai/datahandler/basedata","core/str"],(function(_exports,_utils,_templates,_selectors,BasedataHandler,_str){function _getRequireWildcardCache(nodeInterop){if("function"!=typeof WeakMap)return null;var cacheBabelInterop=new WeakMap,cacheNodeInterop=new WeakMap;return(_getRequireWildcardCache=function(nodeInterop){return nodeInterop?cacheNodeInterop:cacheBabelInterop})(nodeInterop)}function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}function _defineProperty(obj,key,value){return key in obj?Object.defineProperty(obj,key,{value:value,enumerable:!0,configurable:!0,writable:!0}):obj[key]=value,obj}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0,_templates=_interopRequireDefault(_templates),_selectors=_interopRequireDefault(_selectors),BasedataHandler=function(obj,nodeInterop){if(!nodeInterop&&obj&&obj.__esModule)return obj;if(null===obj||"object"!=typeof obj&&"function"!=typeof obj)return{default:obj};var cache=_getRequireWildcardCache(nodeInterop);if(cache&&cache.has(obj))return cache.get(obj);var newObj={},hasPropertyDescriptor=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var key in obj)if("default"!==key&&Object.prototype.hasOwnProperty.call(obj,key)){var desc=hasPropertyDescriptor?Object.getOwnPropertyDescriptor(obj,key):null;desc&&(desc.get||desc.set)?Object.defineProperty(newObj,key,desc):newObj[key]=obj[key]}newObj.default=obj,cache&&cache.set(obj,newObj);return newObj}(BasedataHandler);return _exports.default=class{constructor(baseSelector){_defineProperty(this,"dropzone",null),_defineProperty(this,"dropzoneContentToResetTo",""),this.baseElement=document.querySelector(baseSelector)}async init(){this.dropzone=this.baseElement.querySelector('[data-type="dropzone"]');const dropzone=this.dropzone;dropzone.contentEditable=!0,this.setDropzoneContent(dropzone.innerHTML),dropzone.focus();const _this=this;dropzone.addEventListener("input",(()=>{dropzone.innerHTML!==_this.dropzoneContentToResetTo&&(dropzone.innerHTML=_this.dropzoneContentToResetTo)})),dropzone.addEventListener("drop",(async event=>{if(event.preventDefault(),event.dataTransfer.items){const item=[...event.dataTransfer.items].shift();"file"===item.kind&&await this.handleFile(item.getAsFile())}else await this.handleFile([...event.dataTransfer.files].shift())}));const datamanager=(0,_utils.getDatamanager)((0,_utils.getCurrentModalUniqId)(this.baseElement)),handlePaste=async event=>{if(["describeimg","imagetotext"].includes(datamanager.getCurrentTool())){event.preventDefault();const clipboardData=event.clipboardData||window.clipboardData;if(0===clipboardData.files.length)return void await(0,_utils.errorAlert)(BasedataHandler.getTinyAiString("error_nofileinclipboard_text"),BasedataHandler.getTinyAiString("error_nofileinclipboard_title"));const file=clipboardData.files[0];this.handleFile(file)}};document.querySelector(_selectors.default.modalDialog).removeEventListener("paste",handlePaste),document.querySelector(_selectors.default.modalDialog).addEventListener("paste",handlePaste),dropzone.addEventListener("dragover",(event=>{event.preventDefault(),dropzone.classList.remove("tiny_ai_dropzone_filled"),dropzone.classList.add("tiny_ai_dragover")})),dropzone.addEventListener("dragleave",(event=>{event.preventDefault(),dropzone.classList.remove("tiny_ai_dragover")})),null!==datamanager.getSelectionImg()&&await this.handleFile(datamanager.getSelectionImg())}async handleFile(file){const reader=new FileReader,_this=this;reader.addEventListener("load",(async()=>{const currentModalUniqid=(0,_utils.getCurrentModalUniqId)(this.baseElement),datamanager=(0,_utils.getDatamanager)(currentModalUniqid),fileUploadedEvent=new CustomEvent("fileUploaded",{detail:{newFile:reader.result}});datamanager.getEventEmitterElement().dispatchEvent(fileUploadedEvent);const ittHandler=(0,_utils.getIttHandler)(currentModalUniqid),allowedMimetypes=await ittHandler.getAllowedMimetypes();if(!allowedMimetypes.includes(file.type)){const errorTitle=await(0,_str.getString)("error_unsupportedfiletype_title","tiny_ai"),errorText=await(0,_str.getString)("error_unsupportedfiletype_text","tiny_ai",allowedMimetypes.toString());return void await(0,_utils.errorAlert)(errorText,errorTitle)}const fileEntryTemplateContext={icon:"application/pdf"===file.type?"fa-file-pdf":"fa-image",filename:file.name?file.name:BasedataHandler.getTinyAiString("imagefromeditor")};file.type.startsWith("image")&&(fileEntryTemplateContext.isImage=!0,fileEntryTemplateContext.dataurl=reader.result);const{html:html,js:js}=await _templates.default.renderForPromise("tiny_ai/components/ai-file-list-entry",fileEntryTemplateContext);_this.setDropzoneContent(html),_templates.default.runTemplateJS(js),_this.dropzone.classList.remove("tiny_ai_dragover"),_this.dropzone.classList.add("tiny_ai_dropzone_filled")}),!1),reader.readAsDataURL(file)}setDropzoneContent(html){this.dropzone.innerHTML=html,this.dropzoneContentToResetTo=html}},_exports.default}));

//# sourceMappingURL=file.min.js.map