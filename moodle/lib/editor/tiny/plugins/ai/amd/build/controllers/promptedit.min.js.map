{"version": 3, "file": "promptedit.min.js", "sources": ["../../src/controllers/promptedit.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// <PERSON><PERSON><PERSON> is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Controller for handling the show/hide prompt button and the associated textarea.\n *\n * @module      tiny_ai/controllers/promtedit_controller\n * @copyright   2024, ISB Bayern\n * <AUTHOR>\n * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport {getStrings} from 'core/str';\nimport {getDatamanager, getCurrentModalUniqId} from 'tiny_ai/utils';\n\nexport default class {\n\n    constructor(baseSelector) {\n        this.baseElement = document.querySelector(baseSelector);\n    }\n\n    async init() {\n        const showPromptButton = this.baseElement.querySelector('[data-action=\"showprompt\"]');\n        const textTextarea = this.baseElement.querySelector('textarea[data-type=\"text\"]');\n        const promptTextarea = this.baseElement.querySelector('textarea[data-type=\"prompt\"]');\n\n        const datamanager = getDatamanager(getCurrentModalUniqId(this.baseElement));\n        promptTextarea.innerHTML = datamanager.getCurrentPrompt();\n        datamanager.getEventEmitterElement().addEventListener('promptUpdated', (event) => {\n            promptTextarea.value = event.detail.newPrompt;\n        });\n        if (textTextarea) {\n            textTextarea.innerHTML = datamanager.getCurrentText();\n            textTextarea.addEventListener('input', () => {\n                datamanager.setCurrentText(textTextarea.value);\n            });\n        }\n        promptTextarea.addEventListener('input', () => {\n            datamanager.setCurrentPrompt(promptTextarea.value);\n        });\n\n        if (showPromptButton) {\n            const [showPromptString, hidePromptString] = await getStrings(\n                [\n                    {key: 'prompteditmode', component: 'tiny_ai'},\n                    {key: 'prompteditmodedisable', component: 'tiny_ai'}\n                ]\n            );\n            showPromptButton.addEventListener('click', () => {\n                const currentText = showPromptButton.querySelector('[data-text]').innerText;\n                showPromptButton.querySelector('[data-text]').innerText =\n                    currentText === showPromptString ? hidePromptString : showPromptString;\n                const buttonIcon = showPromptButton.querySelector('i');\n                if (buttonIcon.classList.contains('fa-edit')) {\n                    buttonIcon.classList.remove('fa-edit');\n                    buttonIcon.classList.add('fa-arrow-left');\n                } else {\n                    buttonIcon.classList.remove('fa-arrow-left');\n                    buttonIcon.classList.add('fa-edit');\n                }\n                promptTextarea.classList.toggle('d-none');\n                if (textTextarea) {\n                    textTextarea.classList.toggle('d-none');\n                }\n            });\n        }\n    }\n}\n"], "names": ["constructor", "baseSelector", "baseElement", "document", "querySelector", "showPromptButton", "this", "textTextarea", "promptTextarea", "datamanager", "innerHTML", "getCurrentPrompt", "getEventEmitterElement", "addEventListener", "event", "value", "detail", "newPrompt", "getCurrentText", "setCurrentText", "setCurrentPrompt", "showPromptString", "hidePromptString", "key", "component", "currentText", "innerText", "buttonIcon", "classList", "contains", "remove", "add", "toggle"], "mappings": ";;;;;;;;;MA6BIA,YAAYC,mBACHC,YAAcC,SAASC,cAAcH,iCAIpCI,iBAAmBC,KAAKJ,YAAYE,cAAc,8BAClDG,aAAeD,KAAKJ,YAAYE,cAAc,8BAC9CI,eAAiBF,KAAKJ,YAAYE,cAAc,gCAEhDK,aAAc,0BAAe,gCAAsBH,KAAKJ,iBAC9DM,eAAeE,UAAYD,YAAYE,mBACvCF,YAAYG,yBAAyBC,iBAAiB,iBAAkBC,QACpEN,eAAeO,MAAQD,MAAME,OAAOC,aAEpCV,eACAA,aAAaG,UAAYD,YAAYS,iBACrCX,aAAaM,iBAAiB,SAAS,KACnCJ,YAAYU,eAAeZ,aAAaQ,WAGhDP,eAAeK,iBAAiB,SAAS,KACrCJ,YAAYW,iBAAiBZ,eAAeO,UAG5CV,iBAAkB,OACXgB,iBAAkBC,wBAA0B,mBAC/C,CACI,CAACC,IAAK,iBAAkBC,UAAW,WACnC,CAACD,IAAK,wBAAyBC,UAAW,aAGlDnB,iBAAiBQ,iBAAiB,SAAS,WACjCY,YAAcpB,iBAAiBD,cAAc,eAAesB,UAClErB,iBAAiBD,cAAc,eAAesB,UAC1CD,cAAgBJ,iBAAmBC,iBAAmBD,uBACpDM,WAAatB,iBAAiBD,cAAc,KAC9CuB,WAAWC,UAAUC,SAAS,YAC9BF,WAAWC,UAAUE,OAAO,WAC5BH,WAAWC,UAAUG,IAAI,mBAEzBJ,WAAWC,UAAUE,OAAO,iBAC5BH,WAAWC,UAAUG,IAAI,YAE7BvB,eAAeoB,UAAUI,OAAO,UAC5BzB,cACAA,aAAaqB,UAAUI,OAAO"}