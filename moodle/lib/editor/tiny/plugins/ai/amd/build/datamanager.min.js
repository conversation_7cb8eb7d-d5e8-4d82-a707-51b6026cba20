define("tiny_ai/datamanager",["exports","tiny_ai/utils"],(function(_exports,_utils){function _defineProperty(obj,key,value){return key in obj?Object.defineProperty(obj,key,{value:value,enumerable:!0,configurable:!0,writable:!0}):obj[key]=value,obj}
/**
   * Tiny AI data manager.
   *
   * @module      tiny_ai/datamanager
   * @copyright   2024, ISB Bayern
   * <AUTHOR>
   * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
   */Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0;return _exports.default=class{constructor(uniqid){_defineProperty(this,"uniqid",null),_defineProperty(this,"currentTool",null),_defineProperty(this,"currentAiResult",null),_defineProperty(this,"prompt",null),_defineProperty(this,"file",null),_defineProperty(this,"options",null),_defineProperty(this,"selection",null),_defineProperty(this,"selectionImg",null),this.uniqid=uniqid,this.eventEmitterElement=document.createElement("div")}getDefaultOptions(){const defaultOptions={component:(0,_utils.getEditorUtils)(this.uniqid).getComponent(),contextid:(0,_utils.getEditorUtils)(this.uniqid).getContextId()};return"tts"===this.getCurrentTool()?(defaultOptions.filename="audio_"+Math.random().toString(16).slice(2)+".mp3",defaultOptions.itemid=(0,_utils.getEditorUtils)(this.uniqid).getDraftItemId()):"imggen"===this.getCurrentTool()&&(defaultOptions.filename="img_"+Math.random().toString(16).slice(2)+".png",defaultOptions.itemid=(0,_utils.getEditorUtils)(this.uniqid).getDraftItemId()),defaultOptions}setCurrentTool(currentTool){this.currentTool=currentTool}getCurrentTool(){return this.currentTool}setCurrentText(text){this.text=text;const textUpdatedEvent=new CustomEvent("textUpdated",{detail:{newText:text}});this.eventEmitterElement.dispatchEvent(textUpdatedEvent)}getCurrentText(){return this.text}setCurrentPrompt(prompt){this.prompt=prompt;const promptUpdatedEvent=new CustomEvent("promptUpdated",{detail:{newPrompt:prompt}});this.eventEmitterElement.dispatchEvent(promptUpdatedEvent)}getCurrentPrompt(){return this.prompt}setCurrentFile(file){this.file=file}getCurrentFile(){return this.file}getSelection(){return this.selection}getSelectionText(){const span=document.createElement("span");return span.innerHTML=this.selection,span.textContent}setSelection(selection){this.selection=selection}getSelectionImg(){return this.selectionImg}setSelectionImg(image){this.selectionImg=image}getEventEmitterElement(){return this.eventEmitterElement}setCurrentAiResult(aiResult){this.currentAiResult=aiResult}getCurrentAiResult(){return this.currentAiResult}setCurrentOptions(options){this.options=options}getCurrentOptions(){const optionsToReturn=null===this.options?{}:this.options;return Object.assign(optionsToReturn,this.getDefaultOptions()),optionsToReturn}reset(){this.setCurrentPrompt(""),this.setCurrentOptions(null),this.setCurrentTool(null),this.setCurrentAiResult(null),this.setCurrentFile(null)}},_exports.default}));

//# sourceMappingURL=datamanager.min.js.map