{"version": 3, "file": "editor_utils.min.js", "sources": ["../src/editor_utils.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Moodle is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Editor instance specific utils.\n *\n * @module      tiny_ai/editor_utils\n * @copyright   2024, ISB Bayern\n * <AUTHOR>\n * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport AiModal from 'tiny_ai/modal';\nimport ModalEvents from 'core/modal_events';\nimport {getDraftItemId as getDraftItemIdTinyCore} from 'editor_tiny/options';\nimport {getRenderer} from 'tiny_ai/utils';\n\nexport default class {\n\n    uniqid = null;\n    component = null;\n    userId = null;\n    contextId = null;\n    modal = null;\n    editor = null;\n\n    constructor(uniqid, component, contextId, userId, editor = null) {\n        this.uniqid = uniqid;\n        this.component = component;\n        this.editor = editor;\n        this.contextId = contextId;\n        this.userId = userId;\n    }\n\n    /**\n     * Shows and handles the dialog.\n     */\n    async displayDialogue() {\n\n        // We initially render the modal without content, because we need to rerender it anyway.\n        this.modal = await AiModal.create({\n            templateContext: {\n                classes: 'tiny_ai-modal--dialog',\n                headerclasses: 'tiny_ai-modal--header'\n            }\n        });\n        this.modal.show();\n        const renderer = getRenderer(this.uniqid);\n\n        // Unfortunately, the modal will not execute any JS code in the template, so we need to rerender the modal as a whole again.\n        await renderer.renderStart();\n        this.modal.getRoot().on(ModalEvents.outsideClick, event => {\n            event.preventDefault();\n        });\n    }\n\n\n    insertAfterContent(textToInsert) {\n        this.editor.setContent(this.editor.getContent() + '<p>' + textToInsert + '</p>');\n    }\n\n    /**\n     * Replaces a selected text with the given replacement.\n     *\n     * In case nothing is selected, it will be inserted at the current caret position.\n     *\n     * @param {strings} textReplacement the text by which the current selection will be replaced or which will be inserted\n     *  at the caret (if no selection), can be HTML code\n     */\n    replaceSelection(textReplacement) {\n        this.editor.selection.setContent(textReplacement);\n    }\n\n    getDraftItemId() {\n        // By sending draft item id 0 we state that we do not have a draft item area yet.\n        return this.editor !== null ? getDraftItemIdTinyCore(this.editor) : 0;\n    }\n\n    getComponent() {\n        return this.component;\n    }\n\n    getContextId() {\n        return this.contextId;\n    }\n\n    getUserId() {\n        return this.userId;\n    }\n\n    getModal() {\n        return this.modal;\n    }\n\n}\n"], "names": ["constructor", "uniqid", "component", "contextId", "userId", "editor", "modal", "AiModal", "create", "templateContext", "classes", "headerclasses", "show", "renderer", "this", "renderStart", "getRoot", "on", "ModalEvents", "outsideClick", "event", "preventDefault", "insertAfterContent", "textToInsert", "<PERSON><PERSON><PERSON><PERSON>", "get<PERSON>ontent", "replaceSelection", "textReplacement", "selection", "getDraftItemId", "getComponent", "getContextId", "getUserId", "getModal"], "mappings": "2mBAsCIA,YAAYC,OAAQC,UAAWC,UAAWC,YAAQC,8DAAS,mCAPlD,uCACG,oCACH,uCACG,mCACJ,oCACC,WAGAJ,OAASA,YACTC,UAAYA,eACZG,OAASA,YACTF,UAAYA,eACZC,OAASA,oCASTE,YAAcC,eAAQC,OAAO,CAC9BC,gBAAiB,CACbC,QAAS,wBACTC,cAAe,gCAGlBL,MAAMM,aACLC,UAAW,sBAAYC,KAAKb,cAG5BY,SAASE,mBACVT,MAAMU,UAAUC,GAAGC,sBAAYC,cAAcC,QAC9CA,MAAMC,oBAKdC,mBAAmBC,mBACVlB,OAAOmB,WAAWV,KAAKT,OAAOoB,aAAe,MAAQF,aAAe,QAW7EG,iBAAiBC,sBACRtB,OAAOuB,UAAUJ,WAAWG,iBAGrCE,wBAE2B,OAAhBf,KAAKT,QAAkB,2BAAuBS,KAAKT,QAAU,EAGxEyB,sBACWhB,KAAKZ,UAGhB6B,sBACWjB,KAAKX,UAGhB6B,mBACWlB,KAAKV,OAGhB6B,kBACWnB,KAAKR"}