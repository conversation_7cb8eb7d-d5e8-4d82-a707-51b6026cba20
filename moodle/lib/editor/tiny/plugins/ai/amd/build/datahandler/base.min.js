define("tiny_ai/datahandler/base",["exports"],(function(_exports){Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0;return _exports.default=
/**
   * Simple data handler base class.
   *
   * All tiny_ai data handlers should inherit from this class.
   *
   * @module      tiny_ai/datahandler/base
   * @copyright   2024, ISB Bayern
   * <AUTHOR>
   * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
   */
class{constructor(uniqid){var obj,key,value;value=null,(key="uniqid")in(obj=this)?Object.defineProperty(obj,key,{value:value,enumerable:!0,configurable:!0,writable:!0}):obj[key]=value,this.uniqid=uniqid}},_exports.default}));

//# sourceMappingURL=base.min.js.map