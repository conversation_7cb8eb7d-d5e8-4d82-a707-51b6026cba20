define("tiny_ai/datahandler/tts",["exports","local_ai_manager/config","tiny_ai/datahandler/basedata","tiny_ai/datahandler/base","core/config"],(function(_exports,AiConfig,BasedataHandler,_base,_config2){function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}function _getRequireWildcardCache(nodeInterop){if("function"!=typeof WeakMap)return null;var cacheBabelInterop=new WeakMap,cacheNodeInterop=new WeakMap;return(_getRequireWildcardCache=function(nodeInterop){return nodeInterop?cacheNodeInterop:cacheBabelInterop})(nodeInterop)}function _interopRequireWildcard(obj,nodeInterop){if(!nodeInterop&&obj&&obj.__esModule)return obj;if(null===obj||"object"!=typeof obj&&"function"!=typeof obj)return{default:obj};var cache=_getRequireWildcardCache(nodeInterop);if(cache&&cache.has(obj))return cache.get(obj);var newObj={},hasPropertyDescriptor=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var key in obj)if("default"!==key&&Object.prototype.hasOwnProperty.call(obj,key)){var desc=hasPropertyDescriptor?Object.getOwnPropertyDescriptor(obj,key):null;desc&&(desc.get||desc.set)?Object.defineProperty(newObj,key,desc):newObj[key]=obj[key]}return newObj.default=obj,cache&&cache.set(obj,newObj),newObj}function _defineProperty(obj,key,value){return key in obj?Object.defineProperty(obj,key,{value:value,enumerable:!0,configurable:!0,writable:!0}):obj[key]=value,obj}
/**
   * Tiny AI data manager.
   *
   * @module      tiny_ai/datahandler/tts
   * @copyright   2024, ISB Bayern
   * <AUTHOR> Memmel
   * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
   */Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0,AiConfig=_interopRequireWildcard(AiConfig),BasedataHandler=_interopRequireWildcard(BasedataHandler),_base=_interopRequireDefault(_base),_config2=_interopRequireDefault(_config2);class _default extends _base.default{constructor(){super(...arguments),_defineProperty(this,"ttsOptions",null),_defineProperty(this,"targetLanguage",null),_defineProperty(this,"voice",null),_defineProperty(this,"gender",null)}async getTargetLanguageOptions(){return await this.loadTtsOptions(),this.ttsOptions.languages}async getVoiceOptions(){return await this.loadTtsOptions(),this.ttsOptions.voices}async getGenderOptions(){return await this.loadTtsOptions(),this.ttsOptions.gender}setTargetLanguage(targetLanguage){this.targetLanguage=targetLanguage}setVoice(voice){this.voice=voice}setGender(gender){this.gender=gender}getOptions(){if(null===this.targetLanguage&&null===this.voice&&null===this.gender)return{};const options={};return this.targetLanguage&&(options.languages=[this.targetLanguage]),this.voice&&(options.voices=[this.voice]),this.gender&&(options.gender=[this.gender]),options}getPrompt(currentText){return currentText}async loadTtsOptions(){if(null===this.ttsOptions){const fetchedOptions=await AiConfig.getPurposeOptions("tts");this.ttsOptions=JSON.parse(fetchedOptions.options)}}async getTemplateContext(){const context={modalHeadline:BasedataHandler.getTinyAiString("tts_headline"),showIcon:!0},modalDropdowns=[],targetLanguageOptions=await this.getTargetLanguageOptions();if(null!==targetLanguageOptions&&Object.keys(targetLanguageOptions).length>0){const targetLanguageDropdownContext={preference:"targetLanguage"};let indexOfLanguageOption=0;targetLanguageOptions.map((entry=>entry.key.startsWith(_config2.default.language))).length>0&&(indexOfLanguageOption=targetLanguageOptions.findIndex((value=>value.key.startsWith(_config2.default.language)))),targetLanguageDropdownContext.dropdownDefault=targetLanguageOptions[indexOfLanguageOption].displayname,targetLanguageDropdownContext.dropdownDefaultValue=targetLanguageOptions[indexOfLanguageOption].key,targetLanguageDropdownContext.dropdownDescription=BasedataHandler.getTinyAiString("targetlanguage");const targetLanguageDropdownOptions=[];targetLanguageOptions.forEach((option=>{targetLanguageDropdownOptions.push({optionValue:option.key,optionLabel:option.displayname})})),targetLanguageDropdownContext.dropdownOptions=targetLanguageDropdownOptions,modalDropdowns.push(targetLanguageDropdownContext)}const voiceOptions=await this.getVoiceOptions();if(null!==voiceOptions&&Object.keys(voiceOptions).length>0){const voiceDropdownContext={preference:"voice"};voiceDropdownContext.dropdownDefault=voiceOptions[0].displayname,voiceDropdownContext.dropdownDefaultValue=voiceOptions[0].key,voiceDropdownContext.dropdownDescription=BasedataHandler.getTinyAiString("voice");const voiceDropdownOptions=[];voiceOptions.forEach((option=>{voiceDropdownOptions.push({optionValue:option.key,optionLabel:option.displayname})})),voiceDropdownContext.dropdownOptions=voiceDropdownOptions,modalDropdowns.push(voiceDropdownContext)}const genderOptions=await this.getGenderOptions();if(null!==genderOptions&&Object.keys(genderOptions).length>0){const genderDropdownContext={preference:"gender"};genderDropdownContext.dropdownDefault=genderOptions[0].displayname,genderDropdownContext.dropdownDefaultValue=genderOptions[0].key,genderDropdownContext.dropdownDescription=BasedataHandler.getTinyAiString("gender");const genderDropdownOptions=[];genderOptions.forEach((option=>{genderDropdownOptions.push({optionValue:option.key,optionLabel:option.displayname})})),genderDropdownContext.dropdownOptions=genderDropdownOptions,modalDropdowns.push(genderDropdownContext)}return Object.assign(context,{modalDropdowns:modalDropdowns}),Object.assign(context,BasedataHandler.getShowPromptButtonContext(!1)),Object.assign(context,BasedataHandler.getBackAndGenerateButtonContext()),context}}return _exports.default=_default,_exports.default}));

//# sourceMappingURL=tts.min.js.map