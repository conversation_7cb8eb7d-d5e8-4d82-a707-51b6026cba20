{"version": 3, "file": "translate.min.js", "sources": ["../../src/datahandler/translate.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// <PERSON>odle is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\nimport * as BasedataHandler from 'tiny_ai/datahandler/basedata';\nimport BaseHandler from 'tiny_ai/datahandler/base';\nimport {getTranslateHandler} from 'tiny_ai/utils';\nimport Config from 'core/config';\nimport {getString} from 'core/str';\n\n/**\n * Tiny AI data manager.\n *\n * @module      tiny_ai/datahandler/translate\n * @copyright   2024, ISB Bayern\n * <AUTHOR>\n * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nexport default class extends BaseHandler {\n\n    // English will always be added to the front of the list. All other languages can be defined here.\n    // The user's current language will be shown right after English, if it is contained in this list.\n    targetLanguageCodes = [\n        'de', 'fr', 'it', 'es', 'cs', 'zh', 'ru', 'uk', 'el', 'la', 'tr', 'ro', 'pl', 'bg', 'ar', 'sq',\n        'bs', 'sr', 'hr', 'ku', 'fa', 'ps', 'sk', 'hu'\n    ];\n    targetLanguageOptions = [];\n    targetLanguage = null;\n\n    constructor(uniqid) {\n        super(uniqid);\n        this.initTargetLanguages();\n    }\n\n    setTargetLanguage(targetLanguage) {\n        this.targetLanguage = targetLanguage;\n    }\n\n    async getPrompt(selectionText) {\n        const selectedLanguageEntry =\n            this.targetLanguageOptions.filter(languageEntry => languageEntry.key === this.targetLanguage)[0];\n        let prompt = await getString('translate_baseprompt', 'tiny_ai', selectedLanguageEntry.value);\n        prompt += ': ' + selectionText;\n        return prompt;\n    }\n\n    getTemplateContext() {\n        const translateHandler = getTranslateHandler(this.uniqid);\n        const context = {\n            modalHeadline: BasedataHandler.getTinyAiString('translate_headline'),\n            showIcon: true,\n            tool: 'translate',\n        };\n        const targetLanguageDropdownContext = {};\n        targetLanguageDropdownContext.preference = 'targetLanguage';\n        targetLanguageDropdownContext.dropdownDefault = translateHandler.targetLanguageOptions[0].value;\n        targetLanguageDropdownContext.dropdownDefaultValue = translateHandler.targetLanguageOptions[0].key;\n        targetLanguageDropdownContext.dropdownDescription = BasedataHandler.getTinyAiString('targetlanguage');\n        const targetLanguageDropdownOptions = [];\n        translateHandler.targetLanguageOptions.forEach(languageEntry => {\n            targetLanguageDropdownOptions.push({\n                optionValue: languageEntry.key,\n                optionLabel: languageEntry.value,\n            });\n        });\n        targetLanguageDropdownContext.dropdownOptions = targetLanguageDropdownOptions;\n\n        Object.assign(context, {\n            modalDropdowns: [\n                targetLanguageDropdownContext,\n            ]\n        });\n        Object.assign(context, BasedataHandler.getShowPromptButtonContext());\n        Object.assign(context, BasedataHandler.getBackAndGenerateButtonContext());\n        return context;\n    }\n\n    initTargetLanguages() {\n        // Ensure to only have a two-character lang string for the user's current language.\n        // In case of extended language packs like for example \"de_du\" the attribute Config.language contains \"de_du\", for example.\n        const currentUserLanguage = Config.language.substring(0, 2);\n        const languageNameInCurrentUserLanguage = new Intl.DisplayNames([currentUserLanguage], {type: 'language'});\n        const firstLanguages = [\n            {\n                key: 'en',\n                value: languageNameInCurrentUserLanguage.of('en')\n            }\n        ];\n        if (currentUserLanguage !== 'en' && this.targetLanguageCodes.includes(currentUserLanguage)) {\n            firstLanguages.push(\n                {\n                    key: currentUserLanguage,\n                    value: languageNameInCurrentUserLanguage.of(currentUserLanguage)\n                }\n            );\n            // Remove current user's language from the list.\n            const index = this.targetLanguageCodes.indexOf(currentUserLanguage);\n            this.targetLanguageCodes.splice(index, 1);\n        }\n        this.targetLanguageCodes.forEach(languageCode => {\n            this.targetLanguageOptions[languageCode] = languageNameInCurrentUserLanguage.of(languageCode);\n        });\n\n        const sortedLanguages = Object\n            .entries(this.targetLanguageOptions)\n            .sort((a, b) => a[1].localeCompare(b[1]))\n            .map(([key, value]) => ({'key': key, 'value': value}));\n        this.targetLanguageOptions = [...firstLanguages, ...sortedLanguages];\n    }\n}\n"], "names": ["BaseHandler", "constructor", "uniqid", "initTargetLanguages", "setTargetLanguage", "targetLanguage", "selectionText", "selectedLanguageEntry", "this", "targetLanguageOptions", "filter", "languageEntry", "key", "prompt", "value", "getTemplateContext", "<PERSON><PERSON><PERSON><PERSON>", "context", "modalHeadline", "BasedataHandler", "getTinyAiString", "showIcon", "tool", "targetLanguageDropdownContext", "dropdownDefault", "dropdownDefaultValue", "dropdownDescription", "targetLanguageDropdownOptions", "for<PERSON>ach", "push", "optionValue", "optionLabel", "dropdownOptions", "Object", "assign", "modalDropdowns", "getShowPromptButtonContext", "getBackAndGenerateButtonContext", "currentUserLanguage", "Config", "language", "substring", "languageNameInCurrentUserLanguage", "Intl", "DisplayNames", "type", "firstLanguages", "of", "targetLanguageCodes", "includes", "index", "indexOf", "splice", "languageCode", "sortedLanguages", "entries", "sort", "a", "b", "localeCompare", "map", "_ref"], "mappings": ";;;;;;;;s2BA8B6BA,cAWzBC,YAAYC,cACFA,mDARY,CAClB,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAC1F,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,oDAEtB,0CACP,WAIRC,sBAGTC,kBAAkBC,qBACTA,eAAiBA,+BAGVC,qBACNC,sBACFC,KAAKC,sBAAsBC,QAAOC,eAAiBA,cAAcC,MAAQJ,KAAKH,iBAAgB,OAC9FQ,aAAe,kBAAU,uBAAwB,UAAWN,sBAAsBO,cACtFD,QAAU,KAAOP,cACVO,OAGXE,2BACUC,kBAAmB,8BAAoBR,KAAKN,QAC5Ce,QAAU,CACZC,cAAeC,gBAAgBC,gBAAgB,sBAC/CC,UAAU,EACVC,KAAM,aAEJC,8BAAgC,CACtCA,WAA2C,kBAC3CA,8BAA8BC,gBAAkBR,iBAAiBP,sBAAsB,GAAGK,MAC1FS,8BAA8BE,qBAAuBT,iBAAiBP,sBAAsB,GAAGG,IAC/FW,8BAA8BG,oBAAsBP,gBAAgBC,gBAAgB,wBAC9EO,8BAAgC,UACtCX,iBAAiBP,sBAAsBmB,SAAQjB,gBAC3CgB,8BAA8BE,KAAK,CAC/BC,YAAanB,cAAcC,IAC3BmB,YAAapB,cAAcG,WAGnCS,8BAA8BS,gBAAkBL,8BAEhDM,OAAOC,OAAOjB,QAAS,CACnBkB,eAAgB,CACZZ,iCAGRU,OAAOC,OAAOjB,QAASE,gBAAgBiB,8BACvCH,OAAOC,OAAOjB,QAASE,gBAAgBkB,mCAChCpB,QAGXd,4BAGUmC,oBAAsBC,gBAAOC,SAASC,UAAU,EAAG,GACnDC,kCAAoC,IAAIC,KAAKC,aAAa,CAACN,qBAAsB,CAACO,KAAM,aACxFC,eAAiB,CACnB,CACIlC,IAAK,KACLE,MAAO4B,kCAAkCK,GAAG,WAGxB,OAAxBT,qBAAgC9B,KAAKwC,oBAAoBC,SAASX,qBAAsB,CACxFQ,eAAejB,KACX,CACIjB,IAAK0B,oBACLxB,MAAO4B,kCAAkCK,GAAGT,6BAI9CY,MAAQ1C,KAAKwC,oBAAoBG,QAAQb,0BAC1CU,oBAAoBI,OAAOF,MAAO,QAEtCF,oBAAoBpB,SAAQyB,oBACxB5C,sBAAsB4C,cAAgBX,kCAAkCK,GAAGM,uBAG9EC,gBAAkBrB,OACnBsB,QAAQ/C,KAAKC,uBACb+C,MAAK,CAACC,EAAGC,IAAMD,EAAE,GAAGE,cAAcD,EAAE,MACpCE,KAAIC,WAAEjD,IAAKE,kBAAY,KAAQF,UAAcE,eAC7CL,sBAAwB,IAAIqC,kBAAmBQ"}