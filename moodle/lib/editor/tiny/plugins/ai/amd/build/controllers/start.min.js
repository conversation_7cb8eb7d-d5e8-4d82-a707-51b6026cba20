define("tiny_ai/controllers/start",["exports","tiny_ai/controllers/base","tiny_ai/utils","jquery"],(function(_exports,_base,_utils,_jquery){function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}
/**
   * Controller for the main selection.
   *
   * @module      tiny_ai/controllers/start
   * @copyright   2024, ISB Bayern
   * <AUTHOR>
   * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
   */Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0,_base=_interopRequireDefault(_base),_jquery=_interopRequireDefault(_jquery);class _default extends _base.default{async init(){if(!this.baseElement)return;const summarizeButton=this.baseElement.querySelector('[data-action="loadsummarize"]'),translateButton=this.baseElement.querySelector('[data-action="loadtranslate"]'),describeButton=this.baseElement.querySelector('[data-action="loaddescribe"]'),ttsButton=this.baseElement.querySelector('[data-action="loadtts"]'),imggenButton=this.baseElement.querySelector('[data-action="loadimggen"]'),freePromptButton=this.baseElement.querySelector('[data-action="loadfreeprompt"]'),describeimgButton=this.baseElement.querySelector('[data-action="loaddescribeimg"]'),imagetotextButton=this.baseElement.querySelector('[data-action="loadimagetotext"]'),startHandler=(0,_utils.getStartHandler)(this.uniqid);await startHandler.isTinyAiDisabled()||window.matchMedia("(pointer: coarse)").matches&&document.querySelectorAll(".tiny_ai-card-button.disabled").forEach((button=>{button.parentElement.addEventListener("click",(async()=>{(0,_jquery.default)(button).tooltip("toggle")}))})),summarizeButton&&summarizeButton.addEventListener("click",(async()=>{this.datamanager.setCurrentTool("summarize"),await this.renderer.renderSummarize()})),translateButton&&translateButton.addEventListener("click",(async()=>{this.datamanager.setCurrentTool("translate"),await this.renderer.renderTranslate()})),describeButton&&describeButton.addEventListener("click",(async()=>{this.datamanager.setCurrentTool("describe"),await this.renderer.renderDescribe()})),ttsButton&&ttsButton.addEventListener("click",(async()=>{this.datamanager.setCurrentTool("tts"),await this.renderer.renderTts()})),imggenButton&&imggenButton.addEventListener("click",(async()=>{this.datamanager.setCurrentTool("imggen"),await this.renderer.renderImggen()})),describeimgButton&&describeimgButton.addEventListener("click",(async()=>{this.datamanager.setCurrentTool("describeimg"),await this.renderer.renderDescribeimg()})),imagetotextButton&&imagetotextButton.addEventListener("click",(async()=>{this.datamanager.setCurrentTool("imagetotext"),await this.renderer.renderImagetotext()})),freePromptButton&&(freePromptButton.classList.contains("disabled")?await startHandler.isTinyAiDisabled()||freePromptButton.addEventListener("click",(async()=>{await(0,_utils.errorAlert)(startHandler.isToolDisabled("freeprompt",this.editorUtils.getMode()))})):freePromptButton.addEventListener("click",(async()=>{this.datamanager.setCurrentTool("freeprompt"),this.datamanager.setCurrentPrompt(this.baseElement.querySelector('[data-type="freepromptinput"]').value);null!==await this.generateAiAnswer()&&await this.renderer.renderSuggestion()})))}}return _exports.default=_default,_exports.default}));

//# sourceMappingURL=start.min.js.map