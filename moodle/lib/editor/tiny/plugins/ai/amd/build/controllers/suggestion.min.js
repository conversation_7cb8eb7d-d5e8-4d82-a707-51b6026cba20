define("tiny_ai/controllers/suggestion",["exports","tiny_ai/controllers/base","tiny_ai/datahandler/basedata","tiny_ai/utils","local_ai_manager/warningbox"],(function(_exports,_base,BasedataHandler,_utils,_warningbox){var obj;
/**
   * Controller for the main selection.
   *
   * @module      tiny_ai/controllers/suggestion
   * @copyright   2024, ISB Bayern
   * <AUTHOR>
   * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
   */function _getRequireWildcardCache(nodeInterop){if("function"!=typeof WeakMap)return null;var cacheBabelInterop=new WeakMap,cacheNodeInterop=new WeakMap;return(_getRequireWildcardCache=function(nodeInterop){return nodeInterop?cacheNodeInterop:cacheBabelInterop})(nodeInterop)}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0,_base=(obj=_base)&&obj.__esModule?obj:{default:obj},BasedataHandler=function(obj,nodeInterop){if(!nodeInterop&&obj&&obj.__esModule)return obj;if(null===obj||"object"!=typeof obj&&"function"!=typeof obj)return{default:obj};var cache=_getRequireWildcardCache(nodeInterop);if(cache&&cache.has(obj))return cache.get(obj);var newObj={},hasPropertyDescriptor=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var key in obj)if("default"!==key&&Object.prototype.hasOwnProperty.call(obj,key)){var desc=hasPropertyDescriptor?Object.getOwnPropertyDescriptor(obj,key):null;desc&&(desc.get||desc.set)?Object.defineProperty(newObj,key,desc):newObj[key]=obj[key]}newObj.default=obj,cache&&cache.set(obj,newObj);return newObj}(BasedataHandler);class _default extends _base.default{async init(){const trashButton=this.footer.querySelector('[data-action="delete"]'),regenerateButton=this.footer.querySelector('[data-action="regenerate"]'),insertBelowButton=this.footer.querySelector('[data-action="insertbelow"]'),replaceButton=this.footer.querySelector('[data-action="replace"]'),insertAtCaretButton=this.footer.querySelector('[data-action="insertatcaret"]'),copyButton=this.footer.querySelector('[data-action="copy"]'),downloadButton=this.footer.querySelector('[data-action="download"]');trashButton&&trashButton.addEventListener("click",(async()=>{await this.renderer.renderDismiss()})),regenerateButton&&regenerateButton.addEventListener("click",(async()=>{await this.renderer.renderOptimizePrompt()})),insertBelowButton&&insertBelowButton.addEventListener("click",(()=>{this.editorUtils.insertAfterContent(this.renderer.renderAiResultForEditor()),this.editorUtils.getModal().destroy()})),replaceButton&&replaceButton.addEventListener("click",(()=>{this.editorUtils.replaceSelection(this.renderer.renderAiResultForEditor()),this.editorUtils.getModal().destroy()})),insertAtCaretButton&&insertAtCaretButton.addEventListener("click",(()=>{this.editorUtils.replaceSelection(this.renderer.renderAiResultForEditor()),this.editorUtils.getModal().destroy()})),copyButton&&copyButton.addEventListener("click",(async()=>{if("tts"===this.datamanager.getCurrentTool()||"imggen"===this.datamanager.getCurrentTool()){if(!await(0,_utils.copyFileToClipboard)(this.datamanager.getCurrentAiResult()))return void await(0,_utils.errorAlert)(BasedataHandler.getTinyAiString("error_filetypeclipboardnotsupported_text"),BasedataHandler.getTinyAiString("error_filetypeclipboardnotsupported_title"))}else(0,_utils.copyTextToClipboard)(this.datamanager.getCurrentAiResult())})),downloadButton&&downloadButton.addEventListener("click",(async()=>{"tts"===this.datamanager.getCurrentTool()||"imggen"===this.datamanager.getCurrentTool()?(0,_utils.downloadFile)(this.datamanager.getCurrentAiResult()):(0,_utils.downloadTextAsFile)(this.datamanager.getCurrentAiResult())}));document.querySelector('[data-rendertarget="warningbox"]')&&await(0,_warningbox.renderWarningBox)('[data-rendertarget="warningbox"]')}}return _exports.default=_default,_exports.default}));

//# sourceMappingURL=suggestion.min.js.map