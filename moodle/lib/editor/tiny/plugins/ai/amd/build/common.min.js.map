{"version": 3, "file": "common.min.js", "sources": ["../src/common.js"], "sourcesContent": ["// This file is part of Moodle - https://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Moodle is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <https://www.gnu.org/licenses/>.\n\n/**\n * Common values helper for the Moodle tiny_ai plugin.\n *\n * @module      tiny_ai/common\n * @copyright   2024, ISB Bayern\n * <AUTHOR> <PERSON>\n * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nconst component = 'tiny_ai';\n\nexport default {\n    component,\n    pluginName: `${component}/plugin`,\n    icon: component,\n    toolbarButtonName: \"start_ai\",\n    selectionbarButtonName: \"start_ai_selection\",\n    selectionbarSource: 'selectionbar',\n    menubarSource: 'menubar',\n    toolbarSource: 'toolbar'\n};\n"], "names": ["component", "pluginName", "icon", "toolbarButtonName", "selectionbarButtonName", "selectionbarSource", "menubarSource", "toolbarSource"], "mappings": "qJA0Be,CACXA,UAHc,UAIdC,qBAJc,qBAKdC,KALc,UAMdC,kBAAmB,WACnBC,uBAAwB,qBACxBC,mBAAoB,eACpBC,cAAe,UACfC,cAAe"}