{"version": 3, "file": "utils.min.js", "sources": ["../src/utils.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Moodle is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Tiny AI utils library.\n *\n * @module      tiny_ai/utils\n * @copyright   2024, ISB Bayern\n * <AUTHOR> <PERSON>\n * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport ModalEvents from 'core/modal_events';\nimport Renderer from 'tiny_ai/renderer';\nimport DataManager from 'tiny_ai/datamanager';\nimport ImggenHandler from 'tiny_ai/datahandler/imggen';\nimport OptimizeHandler from 'tiny_ai/datahandler/optimize';\nimport StartHandler from 'tiny_ai/datahandler/start';\nimport SummarizeHandler from 'tiny_ai/datahandler/summarize';\nimport TranslateHandler from 'tiny_ai/datahandler/translate';\nimport TtsHandler from 'tiny_ai/datahandler/tts';\nimport IttHandler from 'tiny_ai/datahandler/itt';\nimport {alert as moodleAlert, exception as displayException} from 'core/notification';\nimport {getString} from 'core/str';\nimport {makeRequest} from 'local_ai_manager/make_request';\nimport * as BasedataHandler from 'tiny_ai/datahandler/basedata';\nimport $ from 'jquery';\nimport Log from 'core/log';\n\nconst objectStore = {};\n\nexport const init = async(uniqid, mode) => {\n    if (!objectStore.hasOwnProperty(uniqid)) {\n        objectStore[uniqid] = {};\n        // The order in which these objects are being created is actually pretty important, because Renderer\n        // object depends on DataManager object.\n        objectStore[uniqid].datamanager = new DataManager(uniqid);\n        await BasedataHandler.init();\n        objectStore[uniqid].imggenhandler = new ImggenHandler(uniqid);\n        objectStore[uniqid].optimizehandler = new OptimizeHandler(uniqid);\n        objectStore[uniqid].starthandler = new StartHandler(uniqid);\n        await objectStore[uniqid].starthandler.init();\n        objectStore[uniqid].summarizehandler = new SummarizeHandler(uniqid);\n        objectStore[uniqid].translatehandler = new TranslateHandler(uniqid);\n        objectStore[uniqid].ttshandler = new TtsHandler(uniqid);\n        objectStore[uniqid].itthandler = new IttHandler(uniqid);\n        objectStore[uniqid].renderer = new Renderer(uniqid, mode);\n    }\n};\n\nexport const getAiAnswer = async(prompt, purpose, options = {}) => {\n    let result = null;\n    const contextid = options.contextid;\n    delete options.contextid;\n    const component = options.component;\n    delete options.component;\n\n    try {\n        result = await makeRequest(purpose, prompt, component, contextid, options);\n    } catch (exception) {\n        await displayException(exception);\n        return null;\n    }\n    if (result.code !== 200) {\n        const alertTitle = await getString('errorwithcode', 'tiny_ai', result.code);\n        const parsedResult = JSON.parse(result.result);\n        if (parsedResult.debuginfo) {\n            Log.error(parsedResult.debuginfo);\n        }\n        await errorAlert(parsedResult.message, alertTitle);\n        return null;\n    }\n    return result.result;\n};\n\nexport const errorAlert = async(message, title = null) => {\n    if (title === null) {\n        title = BasedataHandler.getTinyAiString('generalerror');\n    }\n    const alertModal = await moodleAlert(title, message);\n    alertModal.getRoot().on(ModalEvents.hidden, () => {\n        document.querySelectorAll('button[data-action]').forEach(button => {\n            $(button).tooltip('hide');\n        });\n    });\n};\n\nexport const stripHtmlTags = (textWithTags) => {\n    // Place selected content into a temporary span and extract the plain text from it to strip HTML tags.\n    const span = document.createElement('span');\n    span.innerHTML = textWithTags;\n    return span.textContent;\n};\n\nexport const setEditorUtils = (uniqid, editorUtils) => {\n    objectStore[uniqid].editorUtils = editorUtils;\n};\n\nexport const getEditorUtils = (uniqid) => {\n    return objectStore[uniqid].editorUtils;\n};\n\nexport const getRenderer = (uniqid) => {\n    return objectStore[uniqid].renderer;\n};\n\nexport const getDatamanager = (uniqid) => {\n    return objectStore[uniqid].datamanager;\n};\n\nexport const getImggenHandler = (uniqid) => {\n    return objectStore[uniqid].imggenhandler;\n};\n\nexport const getOptimizeHandler = (uniqid) => {\n    return objectStore[uniqid].optimizehandler;\n};\n\nexport const getStartHandler = (uniqid) => {\n    return objectStore[uniqid].starthandler;\n};\n\nexport const getSummarizeHandler = (uniqid) => {\n    return objectStore[uniqid].summarizehandler;\n};\n\nexport const getTranslateHandler = (uniqid) => {\n    return objectStore[uniqid].translatehandler;\n};\n\nexport const getTtsHandler = (uniqid) => {\n    return objectStore[uniqid].ttshandler;\n};\n\nexport const getIttHandler = (uniqid) => {\n    return objectStore[uniqid].itthandler;\n};\n\nexport const getCurrentModalUniqId = (element) => {\n    return element.closest('[data-tiny_instance_uniqid]').dataset.tiny_instance_uniqid;\n};\n\nexport const copyTextToClipboard = (text) => {\n    const clipboardItemData = {\n        'text/plain': text\n    };\n    navigator.clipboard.write([new ClipboardItem(clipboardItemData)]);\n};\n\nexport const copyFileToClipboard = async(url) => {\n    const data = await fetch(url);\n    const blob = await data.blob();\n    if (!ClipboardItem.supports(blob.type)) {\n        return false;\n    }\n\n    const clipboardItemData = {\n        [blob.type]: blob\n    };\n    navigator.clipboard.write([new ClipboardItem(clipboardItemData)]);\n    return true;\n};\n\nexport const downloadFile = (url, filename = null) => {\n    const link = document.createElement('a');\n    link.href = url;\n    if (!filename) {\n        filename = url.split('/').pop();\n    }\n\n    link.download = filename;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n};\n\nexport const downloadTextAsFile = (text) => {\n    const blob = new Blob([text], {type: 'text/plain'});\n    const url = URL.createObjectURL(blob);\n    downloadFile(url, 'airesult.txt');\n    URL.revokeObjectURL(url);\n};\n"], "names": ["objectStore", "async", "uniqid", "mode", "hasOwnProperty", "datamanager", "DataManager", "BasedataHandler", "init", "imggenhandler", "ImggenHandler", "optimizehandler", "OptimizeHandler", "starthandler", "Start<PERSON><PERSON>ler", "summarizehandler", "Summa<PERSON><PERSON><PERSON><PERSON><PERSON>", "translatehandler", "TranslateHandler", "ttshandler", "TtsHandler", "<PERSON><PERSON><PERSON>", "IttH<PERSON>ler", "renderer", "<PERSON><PERSON><PERSON>", "prompt", "purpose", "options", "result", "contextid", "component", "exception", "code", "alertTitle", "parsedResult", "JSON", "parse", "debuginfo", "error", "<PERSON><PERSON><PERSON><PERSON>", "message", "title", "getTinyAiString", "alertModal", "getRoot", "on", "ModalEvents", "hidden", "document", "querySelectorAll", "for<PERSON>ach", "button", "tooltip", "textWithTags", "span", "createElement", "innerHTML", "textContent", "editor<PERSON><PERSON><PERSON>", "element", "closest", "dataset", "tiny_instance_uniqid", "text", "clipboardItemData", "navigator", "clipboard", "write", "ClipboardItem", "data", "fetch", "url", "blob", "supports", "type", "downloadFile", "filename", "link", "href", "split", "pop", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "Blob", "URL", "createObjectURL", "revokeObjectURL"], "mappings": ";;;;;;;;2tDAyCMA,YAAc,iBAEAC,MAAMC,OAAQC,QACzBH,YAAYI,eAAeF,UAC5BF,YAAYE,QAAU,GAGtBF,YAAYE,QAAQG,YAAc,IAAIC,qBAAYJ,cAC5CK,gBAAgBC,OACtBR,YAAYE,QAAQO,cAAgB,IAAIC,gBAAcR,QACtDF,YAAYE,QAAQS,gBAAkB,IAAIC,kBAAgBV,QAC1DF,YAAYE,QAAQW,aAAe,IAAIC,eAAaZ,cAC9CF,YAAYE,QAAQW,aAAaL,OACvCR,YAAYE,QAAQa,iBAAmB,IAAIC,mBAAiBd,QAC5DF,YAAYE,QAAQe,iBAAmB,IAAIC,mBAAiBhB,QAC5DF,YAAYE,QAAQiB,WAAa,IAAIC,aAAWlB,QAChDF,YAAYE,QAAQmB,WAAa,IAAIC,aAAWpB,QAChDF,YAAYE,QAAQqB,SAAW,IAAIC,kBAAStB,OAAQC,6BAIjCF,eAAMwB,OAAQC,aAASC,+DAAU,GACpDC,OAAS,WACPC,UAAYF,QAAQE,iBACnBF,QAAQE,gBACTC,UAAYH,QAAQG,iBACnBH,QAAQG,cAGXF,aAAe,6BAAYF,QAASD,OAAQK,UAAWD,UAAWF,SACpE,MAAOI,wBACC,2BAAiBA,WAChB,QAES,MAAhBH,OAAOI,KAAc,OACfC,iBAAmB,kBAAU,gBAAiB,UAAWL,OAAOI,MAChEE,aAAeC,KAAKC,MAAMR,OAAOA,eACnCM,aAAaG,wBACTC,MAAMJ,aAAaG,iBAErBE,WAAWL,aAAaM,QAASP,YAChC,YAEJL,OAAOA,cAGLW,WAAatC,eAAMuC,aAASC,6DAAQ,KAC/B,OAAVA,QACAA,MAAQlC,gBAAgBmC,gBAAgB,uBAEtCC,iBAAmB,uBAAYF,MAAOD,SAC5CG,WAAWC,UAAUC,GAAGC,sBAAYC,QAAQ,KACxCC,SAASC,iBAAiB,uBAAuBC,SAAQC,6BACnDA,QAAQC,QAAQ,qEAKAC,qBAEpBC,KAAON,SAASO,cAAc,eACpCD,KAAKE,UAAYH,aACVC,KAAKG,qCAGc,CAACvD,OAAQwD,eACnC1D,YAAYE,QAAQwD,YAAcA,qCAGPxD,QACpBF,YAAYE,QAAQwD,iCAGHxD,QACjBF,YAAYE,QAAQqB,iCAGArB,QACpBF,YAAYE,QAAQG,sCAGEH,QACtBF,YAAYE,QAAQO,0CAGIP,QACxBF,YAAYE,QAAQS,yCAGCT,QACrBF,YAAYE,QAAQW,0CAGKX,QACzBF,YAAYE,QAAQa,8CAGKb,QACzBF,YAAYE,QAAQe,wCAGDf,QACnBF,YAAYE,QAAQiB,kCAGDjB,QACnBF,YAAYE,QAAQmB,0CAGOsC,SAC3BA,QAAQC,QAAQ,+BAA+BC,QAAQC,kDAG9BC,aAC1BC,kBAAoB,cACRD,MAElBE,UAAUC,UAAUC,MAAM,CAAC,IAAIC,cAAcJ,mDAGd/D,MAAAA,YACzBoE,WAAaC,MAAMC,KACnBC,WAAaH,KAAKG,WACnBJ,cAAcK,SAASD,KAAKE,aACtB,QAGLV,kBAAoB,EACrBQ,KAAKE,MAAOF,aAEjBP,UAAUC,UAAUC,MAAM,CAAC,IAAIC,cAAcJ,sBACtC,SAGEW,aAAe,SAACJ,SAAKK,gEAAW,WACnCC,KAAO7B,SAASO,cAAc,KACpCsB,KAAKC,KAAOP,IACPK,WACDA,SAAWL,IAAIQ,MAAM,KAAKC,OAG9BH,KAAKI,SAAWL,SAChB5B,SAASkC,KAAKC,YAAYN,MAC1BA,KAAKO,QACLpC,SAASkC,KAAKG,YAAYR,sEAGKd,aACzBS,KAAO,IAAIc,KAAK,CAACvB,MAAO,CAACW,KAAM,eAC/BH,IAAMgB,IAAIC,gBAAgBhB,MAChCG,aAAaJ,IAAK,gBAClBgB,IAAIE,gBAAgBlB"}