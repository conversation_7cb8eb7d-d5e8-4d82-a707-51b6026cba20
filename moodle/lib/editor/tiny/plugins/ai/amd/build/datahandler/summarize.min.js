define("tiny_ai/datahandler/summarize",["exports","tiny_ai/datahandler/basedata","tiny_ai/datahandler/base","core/str"],(function(_exports,BasedataHandler,_base,_str){var obj;function _getRequireWildcardCache(nodeInterop){if("function"!=typeof WeakMap)return null;var cacheBabelInterop=new WeakMap,cacheNodeInterop=new WeakMap;return(_getRequireWildcardCache=function(nodeInterop){return nodeInterop?cacheNodeInterop:cacheBabelInterop})(nodeInterop)}function _defineProperty(obj,key,value){return key in obj?Object.defineProperty(obj,key,{value:value,enumerable:!0,configurable:!0,writable:!0}):obj[key]=value,obj}
/**
   * Tiny AI data manager.
   *
   * @module      tiny_ai/datahandler/summarize
   * @copyright   2024, ISB Bayern
   * <AUTHOR>
   * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
   */Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0,BasedataHandler=function(obj,nodeInterop){if(!nodeInterop&&obj&&obj.__esModule)return obj;if(null===obj||"object"!=typeof obj&&"function"!=typeof obj)return{default:obj};var cache=_getRequireWildcardCache(nodeInterop);if(cache&&cache.has(obj))return cache.get(obj);var newObj={},hasPropertyDescriptor=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var key in obj)if("default"!==key&&Object.prototype.hasOwnProperty.call(obj,key)){var desc=hasPropertyDescriptor?Object.getOwnPropertyDescriptor(obj,key):null;desc&&(desc.get||desc.set)?Object.defineProperty(newObj,key,desc):newObj[key]=obj[key]}newObj.default=obj,cache&&cache.set(obj,newObj);return newObj}(BasedataHandler),_base=(obj=_base)&&obj.__esModule?obj:{default:obj};class _default extends _base.default{constructor(){super(...arguments),_defineProperty(this,"currentTool",null),_defineProperty(this,"languageType",null),_defineProperty(this,"maxWordCount",0)}getLanguageTypeOptions(){return{nospeciallanguage:BasedataHandler.getTinyAiString("keeplanguagetype"),simplelanguage:BasedataHandler.getTinyAiString("simplelanguage"),technicallanguage:BasedataHandler.getTinyAiString("technicallanguage")}}getMaxWordCountOptions(){return{0:BasedataHandler.getTinyAiString("nomaxwordcount"),10:"10",20:"20",50:"50",100:"100",200:"200",300:"300"}}setMaxWordCount(maxWordCount){this.maxWordCount=maxWordCount}setLanguageType(languageType){this.languageType=languageType}async getPrompt(selectionText){let prompt="";return"summarize"===this.currentTool?prompt+=BasedataHandler.getTinyAiString("summarize_baseprompt"):"describe"===this.currentTool&&(prompt+=BasedataHandler.getTinyAiString("describe_baseprompt")),0===parseInt(this.maxWordCount)&&"nospeciallanguage"===this.languageType?prompt+": "+selectionText:(prompt+=". ",0!==parseInt(this.maxWordCount)&&(prompt+=" ",prompt+=await(0,_str.getString)("maxwordcount_prompt","tiny_ai",this.maxWordCount),prompt+="."),"nospeciallanguage"!==this.languageType&&(prompt+=" ",prompt+=await(0,_str.getString)("languagetype_prompt","tiny_ai",this.getLanguageTypeOptions()[this.languageType]),prompt+="."),prompt+="\n",prompt+=BasedataHandler.getTinyAiString("texttouse")+": "+selectionText,prompt)}setTool(currentTool){this.currentTool=currentTool}getTemplateContext(tool){const context={modalHeadline:BasedataHandler.getTinyAiString(tool+"_headline"),showIcon:!0,tool:tool};Object.assign(context,BasedataHandler.getShowPromptButtonContext()),Object.assign(context,BasedataHandler.getBackAndGenerateButtonContext());const maxWordCountDropdownContext={preference:"maxWordCount"};maxWordCountDropdownContext.dropdownDefault=Object.values(this.getMaxWordCountOptions())[0],maxWordCountDropdownContext.dropdownDefaultValue=Object.keys(this.getMaxWordCountOptions())[0],maxWordCountDropdownContext.dropdownDescription=BasedataHandler.getTinyAiString("maxwordcount");const maxWordCountDropdownOptions=[];for(const[key,value]of Object.entries(this.getMaxWordCountOptions()))maxWordCountDropdownOptions.push({optionValue:key,optionLabel:value});delete maxWordCountDropdownOptions[Object.keys(this.getLanguageTypeOptions())[0]],maxWordCountDropdownContext.dropdownOptions=maxWordCountDropdownOptions;const languageTypeDropdownContext={preference:"languageType"};languageTypeDropdownContext.dropdownDefault=Object.values(this.getLanguageTypeOptions())[0],languageTypeDropdownContext.dropdownDefaultValue=Object.keys(this.getLanguageTypeOptions())[0],languageTypeDropdownContext.dropdownDescription=BasedataHandler.getTinyAiString("languagetype");const languageTypeDropdownOptions=[];for(const[key,value]of Object.entries(this.getLanguageTypeOptions()))languageTypeDropdownOptions.push({optionValue:key,optionLabel:value});return delete languageTypeDropdownOptions[Object.keys(this.getLanguageTypeOptions)[0]],languageTypeDropdownContext.dropdownOptions=languageTypeDropdownOptions,Object.assign(context,{modalDropdowns:[maxWordCountDropdownContext,languageTypeDropdownContext]}),context}}return _exports.default=_default,_exports.default}));

//# sourceMappingURL=summarize.min.js.map