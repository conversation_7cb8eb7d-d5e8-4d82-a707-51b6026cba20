{"version": 3, "file": "imggen.min.js", "sources": ["../../src/datahandler/imggen.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// <PERSON><PERSON>le is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\nimport * as AiConfig from 'local_ai_manager/config';\nimport * as BasedataHandler from \"./basedata\";\nimport BaseHandler from 'tiny_ai/datahandler/base';\n\n/**\n * Tiny AI data manager.\n *\n * @module      tiny_ai/datahandler/imggen\n * @copyright   2024, ISB Bayern\n * <AUTHOR>\n * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nexport default class extends BaseHandler {\n\n    imggenOptions = null;\n\n    size = null;\n\n    async getSizesOptions() {\n        await this.loadImggenOptions();\n        return this.imggenOptions.sizes;\n    }\n\n    setSize(size) {\n        this.size = size;\n    }\n\n    getOptions() {\n        if (this.size === null) {\n            return {};\n        }\n        const options = {};\n        if (this.size) {\n            options.sizes = [this.size];\n        }\n        return options;\n    }\n\n    async loadImggenOptions() {\n        if (this.imggenOptions === null) {\n            const fetchedOptions = await AiConfig.getPurposeOptions('imggen');\n            this.imggenOptions = JSON.parse(fetchedOptions.options);\n        }\n    }\n\n    async getTemplateContext() {\n        const context = {\n            modalHeadline: BasedataHandler.getTinyAiString('imggen_headline'),\n            showIcon: true,\n            tool: 'imggen',\n            textareatype: 'prompt',\n            placeholder: BasedataHandler.getTinyAiString('imggen_placeholder'),\n        };\n\n        const modalDropdowns = [];\n\n        const sizesOptions = await this.getSizesOptions();\n        if (sizesOptions !== null && Object.keys(sizesOptions).length > 0) {\n            const sizesDropdownContext = {};\n            sizesDropdownContext.preference = 'sizes';\n            sizesDropdownContext.dropdownDefault = sizesOptions[0].displayname;\n            sizesDropdownContext.dropdownDefaultValue = sizesOptions[0].key;\n            sizesDropdownContext.dropdownDescription = BasedataHandler.getTinyAiString('size');\n            const sizesDropdownOptions = [];\n            sizesOptions.forEach(option => {\n                sizesDropdownOptions.push({\n                    optionValue: option.key,\n                    optionLabel: option.displayname,\n                });\n            });\n            sizesDropdownContext.dropdownOptions = sizesDropdownOptions;\n            modalDropdowns.push(sizesDropdownContext);\n        }\n        // In the imggen view the dropdowns are at the bottom, so we need to make the dropdowns dropup instead of dropdown.\n        // We only have one here of course, but in case we will have more options, we use a forEach.\n        modalDropdowns.forEach(dropdownContext => {\n            dropdownContext.dropup = true;\n        });\n\n        Object.assign(context, {\n            modalDropdowns: modalDropdowns\n        });\n        Object.assign(context, BasedataHandler.getBackAndGenerateButtonContext());\n        return context;\n    }\n}\n"], "names": ["BaseHandler", "this", "loadImggenOptions", "imggenOptions", "sizes", "setSize", "size", "getOptions", "options", "fetchedOptions", "AiConfig", "getPurposeOptions", "JSON", "parse", "context", "modalHeadline", "BasedataHandler", "getTinyAiString", "showIcon", "tool", "textareatype", "placeholder", "modalDropdowns", "sizesOptions", "getSizesOptions", "Object", "keys", "length", "sizesDropdownContext", "dropdownDefault", "displayname", "dropdownDefaultValue", "key", "dropdownDescription", "sizesDropdownOptions", "for<PERSON>ach", "option", "push", "optionValue", "optionLabel", "dropdownOptions", "dropdownContext", "dropup", "assign", "getBackAndGenerateButtonContext"], "mappings": ";;;;;;;;oQA4B6BA,qFAET,kCAET,2CAGGC,KAAKC,oBACJD,KAAKE,cAAcC,MAG9BC,QAAQC,WACCA,KAAOA,KAGhBC,gBACsB,OAAdN,KAAKK,WACE,SAELE,QAAU,UACZP,KAAKK,OACLE,QAAQJ,MAAQ,CAACH,KAAKK,OAEnBE,qCAIoB,OAAvBP,KAAKE,cAAwB,OACvBM,qBAAuBC,SAASC,kBAAkB,eACnDR,cAAgBS,KAAKC,MAAMJ,eAAeD,2CAK7CM,QAAU,CACZC,cAAeC,gBAAgBC,gBAAgB,mBAC/CC,UAAU,EACVC,KAAM,SACNC,aAAc,SACdC,YAAaL,gBAAgBC,gBAAgB,uBAG3CK,eAAiB,GAEjBC,mBAAqBtB,KAAKuB,qBACX,OAAjBD,cAAyBE,OAAOC,KAAKH,cAAcI,OAAS,EAAG,OACzDC,qBAAuB,CAC7BA,WAAkC,SAClCA,qBAAqBC,gBAAkBN,aAAa,GAAGO,YACvDF,qBAAqBG,qBAAuBR,aAAa,GAAGS,IAC5DJ,qBAAqBK,oBAAsBjB,gBAAgBC,gBAAgB,cACrEiB,qBAAuB,GAC7BX,aAAaY,SAAQC,SACjBF,qBAAqBG,KAAK,CACtBC,YAAaF,OAAOJ,IACpBO,YAAaH,OAAON,iBAG5BF,qBAAqBY,gBAAkBN,qBACvCZ,eAAee,KAAKT,6BAIxBN,eAAea,SAAQM,kBACnBA,gBAAgBC,QAAS,KAG7BjB,OAAOkB,OAAO7B,QAAS,CACnBQ,eAAgBA,iBAEpBG,OAAOkB,OAAO7B,QAASE,gBAAgB4B,mCAChC9B"}