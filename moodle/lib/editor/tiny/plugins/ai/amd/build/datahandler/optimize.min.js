define("tiny_ai/datahandler/optimize",["exports","tiny_ai/datahandler/basedata","tiny_ai/datahandler/base"],(function(_exports,BasedataHandler,_base){var obj;function _getRequireWildcardCache(nodeInterop){if("function"!=typeof WeakMap)return null;var cacheBabelInterop=new WeakMap,cacheNodeInterop=new WeakMap;return(_getRequireWildcardCache=function(nodeInterop){return nodeInterop?cacheNodeInterop:cacheBabelInterop})(nodeInterop)}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0,BasedataHandler=function(obj,nodeInterop){if(!nodeInterop&&obj&&obj.__esModule)return obj;if(null===obj||"object"!=typeof obj&&"function"!=typeof obj)return{default:obj};var cache=_getRequireWildcardCache(nodeInterop);if(cache&&cache.has(obj))return cache.get(obj);var newObj={},hasPropertyDescriptor=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var key in obj)if("default"!==key&&Object.prototype.hasOwnProperty.call(obj,key)){var desc=hasPropertyDescriptor?Object.getOwnPropertyDescriptor(obj,key):null;desc&&(desc.get||desc.set)?Object.defineProperty(newObj,key,desc):newObj[key]=obj[key]}newObj.default=obj,cache&&cache.set(obj,newObj);return newObj}(BasedataHandler),_base=(obj=_base)&&obj.__esModule?obj:{default:obj};
/**
   * Tiny AI data handler for optimize prompt page.
   *
   * @module      tiny_ai/datahandler/optimize
   * @copyright   2024, ISB Bayern
   * <AUTHOR> Memmel
   * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
   */
class _default extends _base.default{constructor(){super(...arguments),function(obj,key,value){key in obj?Object.defineProperty(obj,key,{value:value,enumerable:!0,configurable:!0,writable:!0}):obj[key]=value}(this,"getTemplateContext",(()=>{const context={modalHeadline:BasedataHandler.getTinyAiString("reworkprompt"),showIcon:!0,textareatype:"prompt"};return Object.assign(context,BasedataHandler.getBackAndGenerateButtonContext()),context}))}}return _exports.default=_default,_exports.default}));

//# sourceMappingURL=optimize.min.js.map