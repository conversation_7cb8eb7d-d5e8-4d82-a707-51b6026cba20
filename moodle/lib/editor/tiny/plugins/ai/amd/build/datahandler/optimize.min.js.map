{"version": 3, "file": "optimize.min.js", "sources": ["../../src/datahandler/optimize.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Moodle is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\nimport * as BasedataHandler from 'tiny_ai/datahandler/basedata';\nimport BaseHandler from 'tiny_ai/datahandler/base';\n\n/**\n * Tiny AI data handler for optimize prompt page.\n *\n * @module      tiny_ai/datahandler/optimize\n * @copyright   2024, ISB Bayern\n * <AUTHOR>\n * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nexport default class extends BaseHandler {\n\n    getTemplateContext = () => {\n        const context = {\n            modalHeadline: BasedataHandler.getTinyAiString('reworkprompt'),\n            showIcon: true,\n            textareatype: 'prompt'\n        };\n        Object.assign(context, BasedataHandler.getBackAndGenerateButtonContext());\n        return context;\n    };\n}\n\n\n"], "names": ["BaseHandler", "context", "modalHeadline", "BasedataHandler", "getTinyAiString", "showIcon", "textareatype", "Object", "assign", "getBackAndGenerateButtonContext"], "mappings": ";;;;;;;;;uBA2B6BA,qNAEJ,WACXC,QAAU,CACZC,cAAeC,gBAAgBC,gBAAgB,gBAC/CC,UAAU,EACVC,aAAc,iBAElBC,OAAOC,OAAOP,QAASE,gBAAgBM,mCAChCR"}