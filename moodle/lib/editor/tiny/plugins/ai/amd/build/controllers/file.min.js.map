{"version": 3, "file": "file.min.js", "sources": ["../../src/controllers/file.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// <PERSON><PERSON>le is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Controller for handling the show/hide prompt button and the associated textarea.\n *\n * @module      tiny_ai/controllers/file\n * @copyright   2024, ISB Bayern\n * <AUTHOR>\n * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport {getDatamanager, getCurrentModalUniqId, getIttHandler} from 'tiny_ai/utils';\nimport Templates from 'core/templates';\nimport SELECTORS from 'tiny_ai/selectors';\nimport {errorAlert} from 'tiny_ai/utils';\nimport * as BasedataHandler from 'tiny_ai/datahandler/basedata';\nimport {getString} from 'core/str';\n\n\nexport default class {\n\n    dropzone = null;\n    dropzoneContentToResetTo = '';\n\n    constructor(baseSelector) {\n        this.baseElement = document.querySelector(baseSelector);\n    }\n\n    async init() {\n        this.dropzone = this.baseElement.querySelector('[data-type=\"dropzone\"]');\n        const dropzone = this.dropzone;\n        // Setting contentEditable to true makes the browser show a \"paste\" option in the context menu when\n        // right-clicking the drop zone.\n        dropzone.contentEditable = true;\n        this.setDropzoneContent(dropzone.innerHTML);\n        // Instantly focus the drop zone, so you can directly paste the image.\n        dropzone.focus();\n\n        const _this = this;\n        // The drop zone has \"contentEditable\" enabled, so we have to take care of user input\n        // and reset the content whenever a user tries to input something.\n        dropzone.addEventListener('input', () => {\n            if (dropzone.innerHTML !== _this.dropzoneContentToResetTo) {\n                dropzone.innerHTML = _this.dropzoneContentToResetTo;\n            }\n        });\n        dropzone.addEventListener('drop', async(event) => {\n            event.preventDefault();\n\n            if (event.dataTransfer.items) {\n                // Use DataTransferItemList interface to access the file(s)\n                const item = [...event.dataTransfer.items].shift();\n                // If dropped item is no file, reject it.\n                if (item.kind === 'file') {\n                    await this.handleFile(item.getAsFile());\n                }\n            } else {\n                // Use DataTransfer interface to access the file(s)\n                await this.handleFile([...event.dataTransfer.files].shift());\n            }\n        });\n\n        const datamanager = getDatamanager(getCurrentModalUniqId(this.baseElement));\n\n        const handlePaste = async(event) => {\n            // We have to be careful. We are registering this listener globally onto the modal dialog to catch all the\n            // paste events. We have to ensure we do not interfere with pasting into text fields of other tools though.\n            if (['describeimg', 'imagetotext'].includes(datamanager.getCurrentTool())) {\n                event.preventDefault();\n                const clipboardData = (event.clipboardData || window.clipboardData);\n                if (clipboardData.files.length === 0) {\n                    await errorAlert(BasedataHandler.getTinyAiString('error_nofileinclipboard_text'),\n                        BasedataHandler.getTinyAiString('error_nofileinclipboard_title'));\n                    return;\n                }\n                const file = clipboardData.files[0];\n                this.handleFile(file);\n            }\n        };\n        // Avoid re-adding event paste listener.\n        document.querySelector(SELECTORS.modalDialog).removeEventListener('paste', handlePaste);\n        document.querySelector(SELECTORS.modalDialog).addEventListener('paste', handlePaste);\n        dropzone.addEventListener('dragover', (event) => {\n            event.preventDefault();\n            dropzone.classList.remove('tiny_ai_dropzone_filled');\n            dropzone.classList.add('tiny_ai_dragover');\n        });\n        dropzone.addEventListener('dragleave', (event) => {\n            event.preventDefault();\n            dropzone.classList.remove('tiny_ai_dragover');\n        });\n\n        if (datamanager.getSelectionImg() !== null) {\n            await this.handleFile(datamanager.getSelectionImg());\n        }\n    }\n\n    async handleFile(file) {\n        const reader = new FileReader();\n        const _this = this;\n        reader.addEventListener(\n            'load',\n            async() => {\n                const currentModalUniqid = getCurrentModalUniqId(this.baseElement);\n                const datamanager = getDatamanager(currentModalUniqid);\n                const fileUploadedEvent = new CustomEvent('fileUploaded', {\n                    detail: {\n                        newFile: reader.result,\n                    }\n                });\n                datamanager.getEventEmitterElement().dispatchEvent(fileUploadedEvent);\n                const ittHandler = getIttHandler(currentModalUniqid);\n                const allowedMimetypes = await ittHandler.getAllowedMimetypes();\n\n                if (!allowedMimetypes.includes(file.type)) {\n                    const errorTitle = await getString('error_unsupportedfiletype_title', 'tiny_ai');\n                    const errorText = await getString('error_unsupportedfiletype_text', 'tiny_ai', allowedMimetypes.toString());\n                    await errorAlert(errorText, errorTitle);\n                    return;\n                }\n\n                const fileEntryTemplateContext = {\n                    icon: file.type === 'application/pdf' ? 'fa-file-pdf' : 'fa-image',\n                    filename: file.name ? file.name : BasedataHandler.getTinyAiString('imagefromeditor'),\n                };\n                if (file.type.startsWith('image')) {\n                    fileEntryTemplateContext.isImage = true;\n                    fileEntryTemplateContext.dataurl = reader.result;\n                }\n                const {html, js} = await Templates.renderForPromise('tiny_ai/components/ai-file-list-entry',\n                    fileEntryTemplateContext);\n                _this.setDropzoneContent(html);\n                // We probably have no JS, but let's be safe here.\n                Templates.runTemplateJS(js);\n                // There should be no tiny_ai_dragover class, just to be safe.\n                _this.dropzone.classList.remove('tiny_ai_dragover');\n                _this.dropzone.classList.add('tiny_ai_dropzone_filled');\n            },\n            false,\n        );\n        reader.readAsDataURL(file);\n    }\n\n    setDropzoneContent(html) {\n        this.dropzone.innerHTML = html;\n        // Keep track of the state.\n        this.dropzoneContentToResetTo = html;\n    }\n}\n"], "names": ["constructor", "baseSelector", "baseElement", "document", "querySelector", "dropzone", "this", "contentEditable", "set<PERSON>ropzone<PERSON><PERSON>nt", "innerHTML", "focus", "_this", "addEventListener", "dropzoneContentToResetTo", "async", "event", "preventDefault", "dataTransfer", "items", "item", "shift", "kind", "handleFile", "getAsFile", "files", "datamanager", "handlePaste", "includes", "getCurrentTool", "clipboardData", "window", "length", "BasedataHandler", "getTinyAiString", "file", "SELECTORS", "modalDialog", "removeEventListener", "classList", "remove", "add", "getSelectionImg", "reader", "FileReader", "currentModalUniqid", "fileUploadedEvent", "CustomEvent", "detail", "newFile", "result", "getEventEmitterElement", "dispatchEvent", "ittHandler", "allowedMimetypes", "getAllowedMimetypes", "type", "errorTitle", "errorText", "toString", "fileEntryTemplateContext", "icon", "filename", "name", "startsWith", "isImage", "dataurl", "html", "js", "Templates", "renderForPromise", "runTemplateJS", "readAsDataURL"], "mappings": "olDAqCIA,YAAYC,8CAHD,sDACgB,SAGlBC,YAAcC,SAASC,cAAcH,gCAIrCI,SAAWC,KAAKJ,YAAYE,cAAc,gCACzCC,SAAWC,KAAKD,SAGtBA,SAASE,iBAAkB,OACtBC,mBAAmBH,SAASI,WAEjCJ,SAASK,cAEHC,MAAQL,KAGdD,SAASO,iBAAiB,SAAS,KAC3BP,SAASI,YAAcE,MAAME,2BAC7BR,SAASI,UAAYE,MAAME,6BAGnCR,SAASO,iBAAiB,QAAQE,MAAAA,WAC9BC,MAAMC,iBAEFD,MAAME,aAAaC,MAAO,OAEpBC,KAAO,IAAIJ,MAAME,aAAaC,OAAOE,QAEzB,SAAdD,KAAKE,YACCf,KAAKgB,WAAWH,KAAKI,wBAIzBjB,KAAKgB,WAAW,IAAIP,MAAME,aAAaO,OAAOJ,kBAItDK,aAAc,0BAAe,gCAAsBnB,KAAKJ,cAExDwB,YAAcZ,MAAAA,WAGZ,CAAC,cAAe,eAAea,SAASF,YAAYG,kBAAmB,CACvEb,MAAMC,uBACAa,cAAiBd,MAAMc,eAAiBC,OAAOD,iBAClB,IAA/BA,cAAcL,MAAMO,yBACd,qBAAWC,gBAAgBC,gBAAgB,gCAC7CD,gBAAgBC,gBAAgB,wCAGlCC,KAAOL,cAAcL,MAAM,QAC5BF,WAAWY,QAIxB/B,SAASC,cAAc+B,mBAAUC,aAAaC,oBAAoB,QAASX,aAC3EvB,SAASC,cAAc+B,mBAAUC,aAAaxB,iBAAiB,QAASc,aACxErB,SAASO,iBAAiB,YAAaG,QACnCA,MAAMC,iBACNX,SAASiC,UAAUC,OAAO,2BAC1BlC,SAASiC,UAAUE,IAAI,uBAE3BnC,SAASO,iBAAiB,aAAcG,QACpCA,MAAMC,iBACNX,SAASiC,UAAUC,OAAO,uBAGQ,OAAlCd,YAAYgB,yBACNnC,KAAKgB,WAAWG,YAAYgB,oCAIzBP,YACPQ,OAAS,IAAIC,WACbhC,MAAQL,KACdoC,OAAO9B,iBACH,QACAE,gBACU8B,oBAAqB,gCAAsBtC,KAAKJ,aAChDuB,aAAc,yBAAemB,oBAC7BC,kBAAoB,IAAIC,YAAY,eAAgB,CACtDC,OAAQ,CACJC,QAASN,OAAOO,UAGxBxB,YAAYyB,yBAAyBC,cAAcN,yBAC7CO,YAAa,wBAAcR,oBAC3BS,uBAAyBD,WAAWE,0BAErCD,iBAAiB1B,SAASO,KAAKqB,MAAO,OACjCC,iBAAmB,kBAAU,kCAAmC,WAChEC,gBAAkB,kBAAU,iCAAkC,UAAWJ,iBAAiBK,8BAC1F,qBAAWD,UAAWD,kBAI1BG,yBAA2B,CAC7BC,KAAoB,oBAAd1B,KAAKqB,KAA6B,cAAgB,WACxDM,SAAU3B,KAAK4B,KAAO5B,KAAK4B,KAAO9B,gBAAgBC,gBAAgB,oBAElEC,KAAKqB,KAAKQ,WAAW,WACrBJ,yBAAyBK,SAAU,EACnCL,yBAAyBM,QAAUvB,OAAOO,cAExCiB,KAACA,KAADC,GAAOA,UAAYC,mBAAUC,iBAAiB,wCAChDV,0BACJhD,MAAMH,mBAAmB0D,yBAEfI,cAAcH,IAExBxD,MAAMN,SAASiC,UAAUC,OAAO,oBAChC5B,MAAMN,SAASiC,UAAUE,IAAI,8BAEjC,GAEJE,OAAO6B,cAAcrC,MAGzB1B,mBAAmB0D,WACV7D,SAASI,UAAYyD,UAErBrD,yBAA2BqD"}