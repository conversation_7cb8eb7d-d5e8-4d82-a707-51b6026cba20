{"version": 3, "file": "constants.min.js", "sources": ["../src/constants.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Mo<PERSON><PERSON> is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Tiny AI constants definition.\n *\n * @module      tiny_ai/constants\n * @copyright   2024, ISB Bayern\n * <AUTHOR>\n * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\n\nexport const constants = {\n    modalModes: {\n        editor: 'editor',\n        standalone: 'standalone'\n    },\n    toolPurposeMapping: {\n        audiogen: 'tts',\n        summarize: 'singleprompt',\n        translate: 'translate',\n        describe: 'singleprompt',\n        imggen: 'imggen',\n        tts: 'tts',\n        freeprompt: 'singleprompt',\n        describeimg: 'itt',\n        imagetotext: 'itt'\n    }\n};\n"], "names": ["modalModes", "editor", "standalone", "toolPurposeMapping", "audiogen", "summarize", "translate", "describe", "imggen", "tts", "freeprompt", "describeimg", "imagetotext"], "mappings": "gKAyByB,CACrBA,WAAY,CACRC,OAAQ,SACRC,WAAY,cAEhBC,mBAAoB,CAChBC,SAAU,MACVC,UAAW,eACXC,UAAW,YACXC,SAAU,eACVC,OAAQ,SACRC,IAAK,MACLC,WAAY,eACZC,YAAa,MACbC,YAAa"}