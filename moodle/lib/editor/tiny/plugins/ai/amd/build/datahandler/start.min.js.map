{"version": 3, "file": "start.min.js", "sources": ["../../src/datahandler/start.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// <PERSON><PERSON><PERSON> is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\nimport * as config from 'core/config';\nimport {getString, getStrings} from 'core/str';\nimport {constants} from 'tiny_ai/constants';\nimport * as BasedataHandler from 'tiny_ai/datahandler/basedata';\nimport BaseHandler from 'tiny_ai/datahandler/base';\nimport {getAiConfig} from 'local_ai_manager/config';\nimport {errorAlert, stripHtmlTags} from 'tiny_ai/utils';\n\n\n/**\n * Tiny AI data handler for start page.\n *\n * @module      tiny_ai/datahandler/start\n * @copyright   2024, ISB Bayern\n * <AUTHOR> Memmel\n * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nexport default class extends BaseHandler {\n\n    stringKeys = [\n        'error_limitreached',\n        'error_pleaseconfirm',\n        'error_purposenotconfigured',\n        'error_tenantdisabled',\n        'error_unavailable_noselection',\n        'error_unavailable_selection',\n        'error_userlocked',\n        'error_usernotconfirmed'\n    ];\n\n    aiConfig = null;\n    strings = new Map();\n\n    async init() {\n        this.aiConfig = await getAiConfig();\n        // It's easier to fetch alle these strings before even if we do not use them\n        // instead of making all functions async just because of getString returning a promise.\n        const stringRequest = this.stringKeys.map(key => {\n            return {key, component: 'local_ai_manager'};\n        });\n\n        const fetchedStrings = await getStrings(stringRequest);\n        for (let i = 0; i < this.stringKeys.length; i++) {\n            this.strings.set(this.stringKeys[i], fetchedStrings[i]);\n        }\n        const tinyNotAvailableString = await getString('error_tiny_ai_notavailable', 'tiny_ai');\n        this.strings.set('error_editor_notavailable', tinyNotAvailableString);\n        const confirmLink = document.createElement('a');\n        confirmLink.href = `${config.wwwroot}/local/ai_manager/confirm_ai_usage.php`;\n        confirmLink.innerText = this.strings.get('error_pleaseconfirm');\n        confirmLink.target = '_blank';\n        this.strings.set('combinedusernotconfirmederror', this.strings.get('error_usernotconfirmed') + ' ' + confirmLink.outerHTML);\n    }\n\n    getPurposeConfig(tool) {\n        if (this.aiConfig === null) {\n            throw new Error('Coding error: init function was not called before accessing this.getPurposeConfig!');\n        }\n        const toolPurpose = constants.toolPurposeMapping[tool];\n        return this.aiConfig.purposes.filter(purpose => purpose.purpose === toolPurpose)[0];\n    }\n\n    isTinyAiDisabled() {\n        if (!this.aiConfig.tenantenabled) {\n            return this.strings.get('error_tenantdisabled');\n        }\n        if (!this.aiConfig.userconfirmed) {\n            return this.strings.get('combinedusernotconfirmederror');\n        }\n        if (this.aiConfig.userlocked) {\n            return this.strings.get('error_userlocked');\n        }\n        return '';\n    }\n\n    isToolDisabled(tool) {\n        if (this.isTinyAiDisabled()) {\n            return this.isTinyAiDisabled();\n        }\n        const purposeInfo = this.getPurposeConfig(tool);\n        if (!purposeInfo.isconfigured) {\n            return this.strings.get('error_purposenotconfigured');\n        }\n        if (purposeInfo.limitreached) {\n            return this.strings.get('error_limitreached');\n        }\n        return '';\n    }\n\n    isToolHidden(tool) {\n        const purposeInfo = this.getPurposeConfig(tool);\n        // If the tenant is not allowed the plugin is being disabled completely, so we do not need\n        // to check this case here.\n        if (this.aiConfig.role === 'role_basic') {\n            if (!this.aiConfig.tenantenabled) {\n                return true;\n            }\n            if (!purposeInfo.isconfigured) {\n                return true;\n            }\n        }\n        return false;\n    }\n\n    async getTemplateContext(editorUtils) {\n        let toolButtons = [];\n        if (this.aiConfig.role === 'role_basic' && this.isTinyAiDisabled()) {\n            await errorAlert(await getString('error_tiny_ai_notavailable', 'tiny_ai') + '<br/>'\n                + this.isTinyAiDisabled());\n            editorUtils.getModal().destroy();\n        }\n\n        if (!this.isToolHidden('summarize')) {\n            toolButtons.push({\n                toolname: 'summarize',\n                tool: BasedataHandler.getTinyAiString('toolname_summarize'),\n                customicon: true,\n                iconname: 'shorten',\n                disabled: this.isToolDisabled('summarize').length > 0,\n                tooltip: stripHtmlTags(this.isToolDisabled('summarize')),\n                action: 'loadsummarize'\n            });\n        }\n        if (!this.isToolHidden('translate')) {\n            toolButtons.push({\n                toolname: 'translate',\n                tool: BasedataHandler.getTinyAiString('toolname_translate'),\n                iconname: 'language',\n                iconstyle: 'solid',\n                disabled: this.isToolDisabled('translate').length > 0,\n                tooltip: stripHtmlTags(this.isToolDisabled('translate')),\n                action: 'loadtranslate'\n            });\n        }\n        if (!this.isToolHidden('describe')) {\n            toolButtons.push({\n                toolname: 'describe',\n                tool: BasedataHandler.getTinyAiString('toolname_describe'),\n                customicon: true,\n                iconname: 'extend',\n                disabled: this.isToolDisabled('describe').length > 0,\n                tooltip: stripHtmlTags(this.isToolDisabled('describe')),\n                action: 'loaddescribe'\n            });\n        }\n        if (!this.isToolHidden('tts')) {\n            toolButtons.push({\n                toolname: 'tts',\n                tool: BasedataHandler.getTinyAiString('toolname_tts'),\n                iconstyle: 'solid',\n                iconname: 'microphone',\n                disabled: this.isToolDisabled('tts').length > 0,\n                tooltip: stripHtmlTags(this.isToolDisabled('tts')),\n                action: 'loadtts'\n            });\n        }\n        if (!this.isToolHidden('imggen')) {\n            toolButtons.push({\n                toolname: 'imggen',\n                tool: BasedataHandler.getTinyAiString('toolname_imggen'),\n                iconstyle: 'solid',\n                iconname: 'image',\n                disabled: this.isToolDisabled('imggen').length > 0,\n                tooltip: stripHtmlTags(this.isToolDisabled('imggen')),\n                action: 'loadimggen'\n            });\n        }\n        if (!this.isToolHidden('describeimg')) {\n            toolButtons.push({\n                toolname: 'describeimg',\n                tool: BasedataHandler.getTinyAiString('toolname_describeimg'),\n                iconstyle: 'solid',\n                iconname: 'file-image',\n                disabled: this.isToolDisabled('describeimg').length > 0,\n                tooltip: stripHtmlTags(this.isToolDisabled('describeimg')),\n                action: 'loaddescribeimg'\n            });\n        }\n        if (!this.isToolHidden('imagetotext')) {\n            toolButtons.push({\n                toolname: 'imagetotext',\n                tool: BasedataHandler.getTinyAiString('toolname_imagetotext'),\n                iconstyle: 'solid',\n                iconname: 'signature',\n                disabled: this.isToolDisabled('imagetotext').length > 0,\n                tooltip: stripHtmlTags(this.isToolDisabled('imagetotext')),\n                action: 'loadimagetotext'\n            });\n        }\n        // We sort the not disabled tools to the top while keeping the groups \"disabled tools\" and \"not disabled tools\"\n        // in the same order inside the groups.\n        toolButtons.sort((a, b) => {\n            if (a.disabled && !b.disabled) {\n                return 1;\n            } else if (b.disabled && !a.disabled) {\n                return -1;\n            } else {\n                return 0;\n            }\n        });\n\n        const templateContext = {\n            showIcon: true,\n            modalHeadline: BasedataHandler.getTinyAiString('mainselection_heading'),\n            action: 'loadfreeprompt',\n            modalButtons: toolButtons,\n            freeprompthidden: true\n        };\n\n        Object.assign(templateContext, BasedataHandler.getInputContext());\n        if (this.isTinyAiDisabled()) {\n            templateContext.input[0].disabled = true;\n            templateContext.input[0].hasError = true;\n            templateContext.input[0].errorMessage = this.isTinyAiDisabled();\n        }\n\n        if (this.isToolDisabled('freeprompt')) {\n            templateContext.input[0].disabled = true;\n        }\n        return templateContext;\n    }\n}\n"], "names": ["BaseHandler", "Map", "aiConfig", "stringRequest", "this", "stringKeys", "map", "key", "component", "fetchedStrings", "i", "length", "strings", "set", "tinyNotAvailableString", "confirmLink", "document", "createElement", "href", "config", "wwwroot", "innerText", "get", "target", "outerHTML", "getPurposeConfig", "tool", "Error", "toolPurpose", "constants", "toolPurposeMapping", "purposes", "filter", "purpose", "isTinyAiDisabled", "tenantenabled", "userconfirmed", "userlocked", "isToolDisabled", "purposeInfo", "isconfigured", "limitreached", "isToolHidden", "role", "editor<PERSON><PERSON><PERSON>", "toolButtons", "getModal", "destroy", "push", "toolname", "BasedataHandler", "getTinyAiString", "customicon", "iconname", "disabled", "tooltip", "action", "iconstyle", "sort", "a", "b", "templateContext", "showIcon", "modalHeadline", "modalButtons", "freeprompthidden", "Object", "assign", "getInputContext", "input", "<PERSON><PERSON><PERSON><PERSON>", "errorMessage"], "mappings": ";;;;;;;;gQAiC6BA,kFAEZ,CACT,qBACA,sBACA,6BACA,uBACA,gCACA,8BACA,mBACA,2DAGO,qCACD,IAAIC,uBAGLC,eAAiB,gCAGhBC,cAAgBC,KAAKC,WAAWC,KAAIC,MAC/B,CAACA,IAAAA,IAAKC,UAAW,uBAGtBC,qBAAuB,mBAAWN,mBACnC,IAAIO,EAAI,EAAGA,EAAIN,KAAKC,WAAWM,OAAQD,SACnCE,QAAQC,IAAIT,KAAKC,WAAWK,GAAID,eAAeC,UAElDI,6BAA+B,kBAAU,6BAA8B,gBACxEF,QAAQC,IAAI,4BAA6BC,8BACxCC,YAAcC,SAASC,cAAc,KAC3CF,YAAYG,eAAUC,OAAOC,kDAC7BL,YAAYM,UAAYjB,KAAKQ,QAAQU,IAAI,uBACzCP,YAAYQ,OAAS,cAChBX,QAAQC,IAAI,gCAAiCT,KAAKQ,QAAQU,IAAI,0BAA4B,IAAMP,YAAYS,WAGrHC,iBAAiBC,SACS,OAAlBtB,KAAKF,eACC,IAAIyB,MAAM,4FAEdC,YAAcC,qBAAUC,mBAAmBJ,aAC1CtB,KAAKF,SAAS6B,SAASC,QAAOC,SAAWA,QAAQA,UAAYL,cAAa,GAGrFM,0BACS9B,KAAKF,SAASiC,cAGd/B,KAAKF,SAASkC,cAGfhC,KAAKF,SAASmC,WACPjC,KAAKQ,QAAQU,IAAI,oBAErB,GALIlB,KAAKQ,QAAQU,IAAI,iCAHjBlB,KAAKQ,QAAQU,IAAI,wBAWhCgB,eAAeZ,SACPtB,KAAK8B,0BACE9B,KAAK8B,yBAEVK,YAAcnC,KAAKqB,iBAAiBC,aACrCa,YAAYC,aAGbD,YAAYE,aACLrC,KAAKQ,QAAQU,IAAI,sBAErB,GALIlB,KAAKQ,QAAQU,IAAI,8BAQhCoB,aAAahB,YACHa,YAAcnC,KAAKqB,iBAAiBC,SAGf,eAAvBtB,KAAKF,SAASyC,KAAuB,KAChCvC,KAAKF,SAASiC,qBACR,MAENI,YAAYC,oBACN,SAGR,2BAGcI,iBACjBC,YAAc,GACS,eAAvBzC,KAAKF,SAASyC,MAAyBvC,KAAK8B,2BACtC,2BAAiB,kBAAU,6BAA8B,WAAa,QACtE9B,KAAK8B,oBACXU,YAAYE,WAAWC,WAGtB3C,KAAKsC,aAAa,cACnBG,YAAYG,KAAK,CACbC,SAAU,YACVvB,KAAMwB,gBAAgBC,gBAAgB,sBACtCC,YAAY,EACZC,SAAU,UACVC,SAAUlD,KAAKkC,eAAe,aAAa3B,OAAS,EACpD4C,SAAS,wBAAcnD,KAAKkC,eAAe,cAC3CkB,OAAQ,kBAGXpD,KAAKsC,aAAa,cACnBG,YAAYG,KAAK,CACbC,SAAU,YACVvB,KAAMwB,gBAAgBC,gBAAgB,sBACtCE,SAAU,WACVI,UAAW,QACXH,SAAUlD,KAAKkC,eAAe,aAAa3B,OAAS,EACpD4C,SAAS,wBAAcnD,KAAKkC,eAAe,cAC3CkB,OAAQ,kBAGXpD,KAAKsC,aAAa,aACnBG,YAAYG,KAAK,CACbC,SAAU,WACVvB,KAAMwB,gBAAgBC,gBAAgB,qBACtCC,YAAY,EACZC,SAAU,SACVC,SAAUlD,KAAKkC,eAAe,YAAY3B,OAAS,EACnD4C,SAAS,wBAAcnD,KAAKkC,eAAe,aAC3CkB,OAAQ,iBAGXpD,KAAKsC,aAAa,QACnBG,YAAYG,KAAK,CACbC,SAAU,MACVvB,KAAMwB,gBAAgBC,gBAAgB,gBACtCM,UAAW,QACXJ,SAAU,aACVC,SAAUlD,KAAKkC,eAAe,OAAO3B,OAAS,EAC9C4C,SAAS,wBAAcnD,KAAKkC,eAAe,QAC3CkB,OAAQ,YAGXpD,KAAKsC,aAAa,WACnBG,YAAYG,KAAK,CACbC,SAAU,SACVvB,KAAMwB,gBAAgBC,gBAAgB,mBACtCM,UAAW,QACXJ,SAAU,QACVC,SAAUlD,KAAKkC,eAAe,UAAU3B,OAAS,EACjD4C,SAAS,wBAAcnD,KAAKkC,eAAe,WAC3CkB,OAAQ,eAGXpD,KAAKsC,aAAa,gBACnBG,YAAYG,KAAK,CACbC,SAAU,cACVvB,KAAMwB,gBAAgBC,gBAAgB,wBACtCM,UAAW,QACXJ,SAAU,aACVC,SAAUlD,KAAKkC,eAAe,eAAe3B,OAAS,EACtD4C,SAAS,wBAAcnD,KAAKkC,eAAe,gBAC3CkB,OAAQ,oBAGXpD,KAAKsC,aAAa,gBACnBG,YAAYG,KAAK,CACbC,SAAU,cACVvB,KAAMwB,gBAAgBC,gBAAgB,wBACtCM,UAAW,QACXJ,SAAU,YACVC,SAAUlD,KAAKkC,eAAe,eAAe3B,OAAS,EACtD4C,SAAS,wBAAcnD,KAAKkC,eAAe,gBAC3CkB,OAAQ,oBAKhBX,YAAYa,MAAK,CAACC,EAAGC,IACbD,EAAEL,WAAaM,EAAEN,SACV,EACAM,EAAEN,WAAaK,EAAEL,UAChB,EAED,UAITO,gBAAkB,CACpBC,UAAU,EACVC,cAAeb,gBAAgBC,gBAAgB,yBAC/CK,OAAQ,iBACRQ,aAAcnB,YACdoB,kBAAkB,UAGtBC,OAAOC,OAAON,gBAAiBX,gBAAgBkB,mBAC3ChE,KAAK8B,qBACL2B,gBAAgBQ,MAAM,GAAGf,UAAW,EACpCO,gBAAgBQ,MAAM,GAAGC,UAAW,EACpCT,gBAAgBQ,MAAM,GAAGE,aAAenE,KAAK8B,oBAG7C9B,KAAKkC,eAAe,gBACpBuB,gBAAgBQ,MAAM,GAAGf,UAAW,GAEjCO"}