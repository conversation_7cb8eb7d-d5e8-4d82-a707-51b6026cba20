{"version": 3, "file": "basedata.min.js", "sources": ["../../src/datahandler/basedata.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// <PERSON><PERSON><PERSON> is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\nimport {getStrings} from 'core/str';\nimport {prefetchStrings} from 'core/prefetch';\n\n/**\n * Tiny AI base data provider.\n *\n * @module      tiny_ai/datahandler/basedata\n * @copyright   2024, ISB Bayern\n * <AUTHOR> <PERSON>\n * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nconst stringKeys = [\n    'aigenerating',\n    'aisuggestion',\n    'back',\n    'backbutton_tooltip',\n    'cancel',\n    'copybutton',\n    'copybutton_tooltip',\n    'deletebutton_tooltip',\n    'describeimg_baseprompt',\n    'describeimg_headline',\n    'describe_baseprompt',\n    'describe_headline',\n    'dismiss',\n    'dismisssuggestion',\n    'downloadbutton',\n    'downloadbutton_tooltip',\n    'error_filetypeclipboardnotsupported_title',\n    'error_filetypeclipboardnotsupported_text',\n    'error_nofile',\n    'error_nofileinclipboard_text',\n    'error_nofileinclipboard_title',\n    'error_nopromptgiven',\n    'freeprompt_placeholder',\n    'freepromptbutton_tooltip',\n    'gender',\n    'generalerror',\n    'generate',\n    'generatebutton_tooltip',\n    'imagefromeditor',\n    'imagetotext_baseprompt',\n    'imagetotext_headline',\n    'imagetotext_insertimage',\n    'imggen_headline',\n    'imggen_placeholder',\n    'insertatcaret',\n    'insertatcaret_tooltip',\n    'insertbelow',\n    'insertbelow_tooltip',\n    'keeplanguagetype',\n    'languagetype',\n    'languagetype_prompt',\n    'mainselection_heading',\n    'maxwordcount',\n    'maxwordcount_prompt',\n    'nomaxwordcount',\n    'regeneratebutton_tooltip',\n    'replaceselection',\n    'replaceselectionbutton_tooltip',\n    'reworkprompt',\n    'simplelanguage',\n    'size',\n    'prompteditmode',\n    'prompteditmode_tooltip',\n    'summarize_baseprompt',\n    'summarize_headline',\n    'targetlanguage',\n    'technicallanguage',\n    'texttouse',\n    'toolname_describe',\n    'toolname_describeimg',\n    'toolname_imggen',\n    'toolname_imagetotext',\n    'toolname_summarize',\n    'toolname_translate',\n    'toolname_tts',\n    'translate_baseprompt',\n    'translate_headline',\n    'tts_headline',\n    'voice'\n];\n\nlet strings = new Map();\n\nexport const init = async() => {\n    prefetchStrings('tiny_ai', stringKeys);\n    const stringRequest = stringKeys.map(key => {\n        return {key, component: 'tiny_ai'};\n    });\n    // We now get the strings. They are already prefetched, so this is not a performance feature.\n    // We just use this to avoid having to code asynchronously all the time just for retrieving the\n    // strings by using getString which returns a promise.\n    const fetchedStrings = await getStrings(stringRequest);\n    for (let i = 0; i < stringKeys.length; i++) {\n        strings.set(stringKeys[i], fetchedStrings[i]);\n    }\n};\n\nexport const getTinyAiString = (string) => {\n    return strings.get(string);\n};\n\nexport const getBackAndGenerateButtonContext = () => {\n    return {\n        footerButtons: [\n            {\n                hasText: true,\n                buttonText: getTinyAiString('back'),\n                iconLeft: true,\n                iconRight: false,\n                primary: false,\n                secondary: false,\n                tertiary: true,\n                iconname: 'arrow-left',\n                iconstyle: 'solid',\n                action: 'back',\n                aiButtonHidden: false,\n                tooltip: getTinyAiString('backbutton_tooltip')\n            },\n            {\n                hasText: true,\n                buttonText: getTinyAiString('generate'),\n                iconLeft: true,\n                iconRight: false,\n                primary: true,\n                secondary: false,\n                tertiary: false,\n                iconname: 'sparkle',\n                customicon: true,\n                action: 'generate',\n                aiButtonHidden: false,\n                tooltip: getTinyAiString('generatebutton_tooltip')\n            }\n        ]\n    };\n};\n\nexport const getReplaceButtonsContext = (selectionExists) => {\n\n    return {\n        footerIconButtons:\n            [\n                {\n                    action: 'delete',\n                    iconname: 'trash',\n                    aiButtonHidden: false,\n                    tooltip: getTinyAiString('deletebutton_tooltip')\n                },\n                {\n                    action: 'regenerate',\n                    iconname: 'arrows-rotate',\n                    aiButtonHidden: false,\n                    tooltip: getTinyAiString('regeneratebutton_tooltip')\n                }\n            ],\n        footerButtons:\n            [\n                {\n                    action: 'insertbelow',\n                    hasText: true,\n                    buttonText: getTinyAiString('insertbelow'),\n                    iconLeft: true,\n                    iconRight: false,\n                    secondary: true,\n                    iconname: 'text-insert-last',\n                    customicon: true,\n                    aiButtonHidden: false,\n                    tooltip: getTinyAiString('insertbelow_tooltip')\n                },\n                {\n                    action: selectionExists ? 'replace' : 'insertatcaret',\n                    hasText: true,\n                    buttonText: selectionExists\n                        ? getTinyAiString('replaceselection') : getTinyAiString('insertatcaret'),\n                    iconLeft: true,\n                    iconRight: false,\n                    primary: true,\n                    iconname: 'check',\n                    iconstyle: 'solid',\n                    aiButtonHidden: false,\n                    tooltip: selectionExists\n                        ? getTinyAiString('replaceselection_tooltip') : getTinyAiString('insertatcaret_tooltip')\n                }\n            ],\n    };\n};\n\nexport const getCopyAndDownloadButtonsContext = () => {\n    const buttonsContext = getReplaceButtonsContext(false);\n    delete buttonsContext.footerButtons;\n    buttonsContext.footerButtons = [\n        {\n            action: 'copy',\n            hasText: true,\n            buttonText: getTinyAiString('copybutton'),\n            iconLeft: true,\n            iconRight: false,\n            secondary: true,\n            iconname: 'copy',\n            iconstyle: 'solid',\n            aiButtonHidden: false,\n            tooltip: getTinyAiString('copybutton_tooltip')\n        },\n        {\n            action: 'download',\n            hasText: true,\n            buttonText: getTinyAiString('downloadbutton'),\n            iconLeft: true,\n            iconRight: false,\n            primary: true,\n            iconname: 'file-download',\n            iconstyle: 'solid',\n            aiButtonHidden: false,\n            tooltip: getTinyAiString('downloadbutton_tooltip'),\n        }\n    ];\n    return buttonsContext;\n};\n\nexport const getInputContext = () => {\n    return {\n        input: [\n            {\n                iconname: 'sparkle',\n                customicon: true,\n                button: [\n                    {\n                        customicon: false,\n                        iconname: 'arrow-right',\n                        iconstyle: 'solid',\n                        iconLeft: false,\n                        iconRight: true,\n                        tooltip: getTinyAiString('freepromptbutton_tooltip')\n                    }\n                ]\n            }\n        ],\n    };\n};\n\nexport const getShowPromptButtonContext = (showButton = true) => {\n    return {\n        hasText: true,\n        buttonText: getTinyAiString('prompteditmode'),\n        iconLeft: true,\n        iconRight: false,\n        tertiary: true,\n        iconname: 'edit',\n        iconstyle: 'solid',\n        action: 'showprompt',\n        aiButtonHidden: !showButton,\n        textareas: [\n            {\n                textareatype: 'text',\n                collapsed: false,\n            },\n            {\n                textareatype: 'prompt',\n                collapsed: true,\n            }\n        ],\n        collapsed: true,\n        tooltip: getTinyAiString('prompteditmode_tooltip')\n    };\n};\n"], "names": ["stringKeys", "strings", "Map", "async", "stringRequest", "map", "key", "component", "fetchedStrings", "i", "length", "set", "getTinyAiString", "string", "get", "footerButtons", "hasText", "buttonText", "iconLeft", "iconRight", "primary", "secondary", "tertiary", "iconname", "iconstyle", "action", "aiButtonHidden", "tooltip", "customicon", "getReplaceButtonsContext", "selectionExists", "footerIconButtons", "buttonsContext", "input", "button", "showButton", "textareas", "textareatype", "collapsed"], "mappings": ";;;;;;;;;MA2BMA,WAAa,CACf,eACA,eACA,OACA,qBACA,SACA,aACA,qBACA,uBACA,yBACA,uBACA,sBACA,oBACA,UACA,oBACA,iBACA,yBACA,4CACA,2CACA,eACA,+BACA,gCACA,sBACA,yBACA,2BACA,SACA,eACA,WACA,yBACA,kBACA,yBACA,uBACA,0BACA,kBACA,qBACA,gBACA,wBACA,cACA,sBACA,mBACA,eACA,sBACA,wBACA,eACA,sBACA,iBACA,2BACA,mBACA,iCACA,eACA,iBACA,OACA,iBACA,yBACA,uBACA,qBACA,iBACA,oBACA,YACA,oBACA,uBACA,kBACA,uBACA,qBACA,qBACA,eACA,uBACA,qBACA,eACA,aAGAC,QAAU,IAAIC,kBAEEC,wCACA,UAAWH,kBACrBI,cAAgBJ,WAAWK,KAAIC,MAC1B,CAACA,IAAAA,IAAKC,UAAW,cAKtBC,qBAAuB,mBAAWJ,mBACnC,IAAIK,EAAI,EAAGA,EAAIT,WAAWU,OAAQD,IACnCR,QAAQU,IAAIX,WAAWS,GAAID,eAAeC,WAIrCG,gBAAmBC,QACrBZ,QAAQa,IAAID,0FAGwB,KACpC,CACHE,cAAe,CACX,CACIC,SAAS,EACTC,WAAYL,gBAAgB,QAC5BM,UAAU,EACVC,WAAW,EACXC,SAAS,EACTC,WAAW,EACXC,UAAU,EACVC,SAAU,aACVC,UAAW,QACXC,OAAQ,OACRC,gBAAgB,EAChBC,QAASf,gBAAgB,uBAE7B,CACII,SAAS,EACTC,WAAYL,gBAAgB,YAC5BM,UAAU,EACVC,WAAW,EACXC,SAAS,EACTC,WAAW,EACXC,UAAU,EACVC,SAAU,UACVK,YAAY,EACZH,OAAQ,WACRC,gBAAgB,EAChBC,QAASf,gBAAgB,oCAM5BiB,yBAA4BC,kBAE9B,CACHC,kBACI,CACI,CACIN,OAAQ,SACRF,SAAU,QACVG,gBAAgB,EAChBC,QAASf,gBAAgB,yBAE7B,CACIa,OAAQ,aACRF,SAAU,gBACVG,gBAAgB,EAChBC,QAASf,gBAAgB,8BAGrCG,cACI,CACI,CACIU,OAAQ,cACRT,SAAS,EACTC,WAAYL,gBAAgB,eAC5BM,UAAU,EACVC,WAAW,EACXE,WAAW,EACXE,SAAU,mBACVK,YAAY,EACZF,gBAAgB,EAChBC,QAASf,gBAAgB,wBAE7B,CACIa,OAAQK,gBAAkB,UAAY,gBACtCd,SAAS,EACTC,WACML,gBADMkB,gBACU,mBAAsC,iBAC5DZ,UAAU,EACVC,WAAW,EACXC,SAAS,EACTG,SAAU,QACVC,UAAW,QACXE,gBAAgB,EAChBC,QACMf,gBADGkB,gBACa,2BAA8C,kIAMxC,WACtCE,eAAiBH,0BAAyB,iBACzCG,eAAejB,cACtBiB,eAAejB,cAAgB,CAC3B,CACIU,OAAQ,OACRT,SAAS,EACTC,WAAYL,gBAAgB,cAC5BM,UAAU,EACVC,WAAW,EACXE,WAAW,EACXE,SAAU,OACVC,UAAW,QACXE,gBAAgB,EAChBC,QAASf,gBAAgB,uBAE7B,CACIa,OAAQ,WACRT,SAAS,EACTC,WAAYL,gBAAgB,kBAC5BM,UAAU,EACVC,WAAW,EACXC,SAAS,EACTG,SAAU,gBACVC,UAAW,QACXE,gBAAgB,EAChBC,QAASf,gBAAgB,4BAG1BoB,yCAGoB,KACpB,CACHC,MAAO,CACH,CACIV,SAAU,UACVK,YAAY,EACZM,OAAQ,CACJ,CACIN,YAAY,EACZL,SAAU,cACVC,UAAW,QACXN,UAAU,EACVC,WAAW,EACXQ,QAASf,gBAAgB,sEAQP,eAACuB,4EAChC,CACHnB,SAAS,EACTC,WAAYL,gBAAgB,kBAC5BM,UAAU,EACVC,WAAW,EACXG,UAAU,EACVC,SAAU,OACVC,UAAW,QACXC,OAAQ,aACRC,gBAAiBS,WACjBC,UAAW,CACP,CACIC,aAAc,OACdC,WAAW,GAEf,CACID,aAAc,SACdC,WAAW,IAGnBA,WAAW,EACXX,QAASf,gBAAgB"}