define("tiny_ai/datahandler/basedata",["exports","core/str","core/prefetch"],(function(_exports,_str,_prefetch){Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.init=_exports.getTinyAiString=_exports.getShowPromptButtonContext=_exports.getReplaceButtonsContext=_exports.getInputContext=_exports.getCopyAndDownloadButtonsContext=_exports.getBackAndGenerateButtonContext=void 0;
/**
   * Tiny AI base data provider.
   *
   * @module      tiny_ai/datahandler/basedata
   * @copyright   2024, ISB Bayern
   * <AUTHOR>
   * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
   */
const stringKeys=["aigenerating","aisuggestion","back","backbutton_tooltip","cancel","copybutton","copybutton_tooltip","deletebutton_tooltip","describeimg_baseprompt","describeimg_headline","describe_baseprompt","describe_headline","dismiss","dismisssuggestion","downloadbutton","downloadbutton_tooltip","error_filetypeclipboardnotsupported_title","error_filetypeclipboardnotsupported_text","error_nofile","error_nofileinclipboard_text","error_nofileinclipboard_title","error_nopromptgiven","freeprompt_placeholder","freepromptbutton_tooltip","gender","generalerror","generate","generatebutton_tooltip","imagefromeditor","imagetotext_baseprompt","imagetotext_headline","imagetotext_insertimage","imggen_headline","imggen_placeholder","insertatcaret","insertatcaret_tooltip","insertbelow","insertbelow_tooltip","keeplanguagetype","languagetype","languagetype_prompt","mainselection_heading","maxwordcount","maxwordcount_prompt","nomaxwordcount","regeneratebutton_tooltip","replaceselection","replaceselectionbutton_tooltip","reworkprompt","simplelanguage","size","prompteditmode","prompteditmode_tooltip","summarize_baseprompt","summarize_headline","targetlanguage","technicallanguage","texttouse","toolname_describe","toolname_describeimg","toolname_imggen","toolname_imagetotext","toolname_summarize","toolname_translate","toolname_tts","translate_baseprompt","translate_headline","tts_headline","voice"];let strings=new Map;_exports.init=async()=>{(0,_prefetch.prefetchStrings)("tiny_ai",stringKeys);const stringRequest=stringKeys.map((key=>({key:key,component:"tiny_ai"}))),fetchedStrings=await(0,_str.getStrings)(stringRequest);for(let i=0;i<stringKeys.length;i++)strings.set(stringKeys[i],fetchedStrings[i])};const getTinyAiString=string=>strings.get(string);_exports.getTinyAiString=getTinyAiString;_exports.getBackAndGenerateButtonContext=()=>({footerButtons:[{hasText:!0,buttonText:getTinyAiString("back"),iconLeft:!0,iconRight:!1,primary:!1,secondary:!1,tertiary:!0,iconname:"arrow-left",iconstyle:"solid",action:"back",aiButtonHidden:!1,tooltip:getTinyAiString("backbutton_tooltip")},{hasText:!0,buttonText:getTinyAiString("generate"),iconLeft:!0,iconRight:!1,primary:!0,secondary:!1,tertiary:!1,iconname:"sparkle",customicon:!0,action:"generate",aiButtonHidden:!1,tooltip:getTinyAiString("generatebutton_tooltip")}]});const getReplaceButtonsContext=selectionExists=>({footerIconButtons:[{action:"delete",iconname:"trash",aiButtonHidden:!1,tooltip:getTinyAiString("deletebutton_tooltip")},{action:"regenerate",iconname:"arrows-rotate",aiButtonHidden:!1,tooltip:getTinyAiString("regeneratebutton_tooltip")}],footerButtons:[{action:"insertbelow",hasText:!0,buttonText:getTinyAiString("insertbelow"),iconLeft:!0,iconRight:!1,secondary:!0,iconname:"text-insert-last",customicon:!0,aiButtonHidden:!1,tooltip:getTinyAiString("insertbelow_tooltip")},{action:selectionExists?"replace":"insertatcaret",hasText:!0,buttonText:getTinyAiString(selectionExists?"replaceselection":"insertatcaret"),iconLeft:!0,iconRight:!1,primary:!0,iconname:"check",iconstyle:"solid",aiButtonHidden:!1,tooltip:getTinyAiString(selectionExists?"replaceselection_tooltip":"insertatcaret_tooltip")}]});_exports.getReplaceButtonsContext=getReplaceButtonsContext;_exports.getCopyAndDownloadButtonsContext=()=>{const buttonsContext=getReplaceButtonsContext(!1);return delete buttonsContext.footerButtons,buttonsContext.footerButtons=[{action:"copy",hasText:!0,buttonText:getTinyAiString("copybutton"),iconLeft:!0,iconRight:!1,secondary:!0,iconname:"copy",iconstyle:"solid",aiButtonHidden:!1,tooltip:getTinyAiString("copybutton_tooltip")},{action:"download",hasText:!0,buttonText:getTinyAiString("downloadbutton"),iconLeft:!0,iconRight:!1,primary:!0,iconname:"file-download",iconstyle:"solid",aiButtonHidden:!1,tooltip:getTinyAiString("downloadbutton_tooltip")}],buttonsContext};_exports.getInputContext=()=>({input:[{iconname:"sparkle",customicon:!0,button:[{customicon:!1,iconname:"arrow-right",iconstyle:"solid",iconLeft:!1,iconRight:!0,tooltip:getTinyAiString("freepromptbutton_tooltip")}]}]});_exports.getShowPromptButtonContext=function(){let showButton=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];return{hasText:!0,buttonText:getTinyAiString("prompteditmode"),iconLeft:!0,iconRight:!1,tertiary:!0,iconname:"edit",iconstyle:"solid",action:"showprompt",aiButtonHidden:!showButton,textareas:[{textareatype:"text",collapsed:!1},{textareatype:"prompt",collapsed:!0}],collapsed:!0,tooltip:getTinyAiString("prompteditmode_tooltip")}}}));

//# sourceMappingURL=basedata.min.js.map