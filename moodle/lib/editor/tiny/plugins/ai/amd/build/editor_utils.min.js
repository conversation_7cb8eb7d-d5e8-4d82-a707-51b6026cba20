define("tiny_ai/editor_utils",["exports","tiny_ai/modal","core/modal_events","editor_tiny/options","tiny_ai/utils"],(function(_exports,_modal,_modal_events,_options,_utils){function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}function _defineProperty(obj,key,value){return key in obj?Object.defineProperty(obj,key,{value:value,enumerable:!0,configurable:!0,writable:!0}):obj[key]=value,obj}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0,_modal=_interopRequireDefault(_modal),_modal_events=_interopRequireDefault(_modal_events);return _exports.default=class{constructor(uniqid,component,contextId,userId){let editor=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null;_defineProperty(this,"uniqid",null),_defineProperty(this,"component",null),_defineProperty(this,"userId",null),_defineProperty(this,"contextId",null),_defineProperty(this,"modal",null),_defineProperty(this,"editor",null),this.uniqid=uniqid,this.component=component,this.editor=editor,this.contextId=contextId,this.userId=userId}async displayDialogue(){this.modal=await _modal.default.create({templateContext:{classes:"tiny_ai-modal--dialog",headerclasses:"tiny_ai-modal--header"}}),this.modal.show();const renderer=(0,_utils.getRenderer)(this.uniqid);await renderer.renderStart(),this.modal.getRoot().on(_modal_events.default.outsideClick,(event=>{event.preventDefault()}))}insertAfterContent(textToInsert){this.editor.setContent(this.editor.getContent()+"<p>"+textToInsert+"</p>")}replaceSelection(textReplacement){this.editor.selection.setContent(textReplacement)}getDraftItemId(){return null!==this.editor?(0,_options.getDraftItemId)(this.editor):0}getComponent(){return this.component}getContextId(){return this.contextId}getUserId(){return this.userId}getModal(){return this.modal}},_exports.default}));

//# sourceMappingURL=editor_utils.min.js.map