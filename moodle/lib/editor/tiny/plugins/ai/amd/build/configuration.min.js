define("tiny_ai/configuration",["exports","tiny_ai/common","editor_tiny/utils"],(function(_exports,_common,_utils){Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.configure=void 0;
/**
   * Tiny tiny_ai for Moodle.
   *
   * @module      tiny_ai/configuration
   * @copyright   2024, ISB Bayern
   * <AUTHOR> <PERSON>
   * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
   */
const getToolbarConfiguration=instanceConfig=>{let toolbar=instanceConfig.toolbar;return toolbar=(0,_utils.addToolbarButtons)(toolbar,"formatting",[_common.toolbarButtonName]),toolbar},getMenuConfiguration=instanceConfig=>{let menu=instanceConfig.menu;return menu=(0,_utils.addMenubarItem)(menu,"tools",[_common.toolbarButtonName].join(" ")),menu},getSelectionToolbarConfiguration=instanceConfig=>{let toolbar=instanceConfig.quickbars_selection_toolbar;return!1===toolbar&&(toolbar=void 0),toolbar=(0,_utils.addQuickbarsToolbarItem)(toolbar,"|",_common.selectionbarButtonName),toolbar};_exports.configure=instanceConfig=>({toolbar:getToolbarConfiguration(instanceConfig),menu:getMenuConfiguration(instanceConfig),quickbars_selection_toolbar:getSelectionToolbarConfiguration(instanceConfig)})}));

//# sourceMappingURL=configuration.min.js.map