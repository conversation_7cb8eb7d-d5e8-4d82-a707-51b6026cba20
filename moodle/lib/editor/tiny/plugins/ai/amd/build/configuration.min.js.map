{"version": 3, "file": "configuration.min.js", "sources": ["../src/configuration.js"], "sourcesContent": ["// This file is part of Moodle - https://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Mo<PERSON>le is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <https://www.gnu.org/licenses/>.\n\n/**\n * Tiny tiny_ai for Moodle.\n *\n * @module      tiny_ai/configuration\n * @copyright   2024, ISB Bayern\n * <AUTHOR> <PERSON>\n * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport {\n    toolbarButtonName,\n    selectionbarButtonName\n} from 'tiny_ai/common';\n\nimport {\n    addMenubarItem,\n    addToolbarButtons,\n    addQuickbarsToolbarItem\n} from 'editor_tiny/utils';\n\nconst getToolbarConfiguration = (instanceConfig) => {\n    let toolbar = instanceConfig.toolbar;\n\n    toolbar = addToolbarButtons(toolbar, 'formatting', [\n        toolbarButtonName,\n    ]);\n\n    return toolbar;\n};\n\nconst getMenuConfiguration = (instanceConfig) => {\n    let menu = instanceConfig.menu;\n    menu = addMenubarItem(menu, 'tools', [\n        toolbarButtonName,\n    ].join(' '));\n    return menu;\n};\n\nconst getSelectionToolbarConfiguration = (instanceConfig) => {\n    let toolbar = instanceConfig.quickbars_selection_toolbar;\n    // The following is a dirty workaround until MDL-82724 has been integrated.\n    if (toolbar === false) {\n        toolbar = undefined;\n    }\n    toolbar = addQuickbarsToolbarItem(toolbar, '|', selectionbarButtonName);\n    return toolbar;\n};\n\nexport const configure = (instanceConfig) => {\n    return {\n        toolbar: getToolbarConfiguration(instanceConfig),\n        menu: getMenuConfiguration(instanceConfig),\n        // eslint-disable-next-line camelcase\n        quickbars_selection_toolbar: getSelectionToolbarConfiguration(instanceConfig)\n    };\n};\n"], "names": ["getToolbarConfiguration", "instanceConfig", "toolbar", "toolbarButtonName", "getMenuConfiguration", "menu", "join", "getSelectionToolbarConfiguration", "quickbars_selection_toolbar", "undefined", "selectionbarButtonName"], "mappings": ";;;;;;;;;MAmCMA,wBAA2BC,qBACzBC,QAAUD,eAAeC,eAE7BA,SAAU,4BAAkBA,QAAS,aAAc,CAC/CC,4BAGGD,SAGLE,qBAAwBH,qBACtBI,KAAOJ,eAAeI,YAC1BA,MAAO,yBAAeA,KAAM,QAAS,CACjCF,2BACFG,KAAK,MACAD,MAGLE,iCAAoCN,qBAClCC,QAAUD,eAAeO,mCAEb,IAAZN,UACAA,aAAUO,GAEdP,SAAU,kCAAwBA,QAAS,IAAKQ,gCACzCR,4BAGeD,iBACf,CACHC,QAASF,wBAAwBC,gBACjCI,KAAMD,qBAAqBH,gBAE3BO,4BAA6BD,iCAAiCN"}