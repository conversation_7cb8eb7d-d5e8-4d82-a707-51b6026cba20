{"version": 3, "file": "modal.min.js", "sources": ["../src/modal.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// Mood<PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Moodle is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Equation Modal for Tiny.\n *\n * @module      tiny_ai/modal\n * @copyright   2024, ISB Bayern\n * <AUTHOR> <PERSON>\n * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport Modal from 'core/modal';\n\nexport default class AiModal extends Modal {\n    static TYPE = 'ai-modal';\n    static TEMPLATE = 'tiny_ai/components/moodle-modal';\n\n    registerEventListeners() {\n        // Call the parent registration.\n        super.registerEventListeners();\n    }\n\n    configure(modalConfig) {\n        modalConfig.large = true;\n        modalConfig.removeOnClose = true;\n        super.configure(modalConfig);\n    }\n}\n"], "names": ["AiModal", "Modal", "registerEventListeners", "configure", "modalConfig", "large", "removeOnClose"], "mappings": "mYA0BqBA,gBAAgBC,eAIjCC,+BAEUA,yBAGVC,UAAUC,aACNA,YAAYC,OAAQ,EACpBD,YAAYE,eAAgB,QACtBH,UAAUC,8DAZHJ,eACH,4BADGA,mBAEC"}