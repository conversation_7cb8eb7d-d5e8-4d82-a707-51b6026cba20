{"version": 3, "file": "dropdown.min.js", "sources": ["../../src/controllers/dropdown.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// <PERSON><PERSON><PERSON> is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Controller for the main selection.\n *\n * This controller is needed to update the \"select button\"\n *\n * @module      tiny_ai/controllers/dropdown\n * @copyright   2024, ISB Bayern\n * <AUTHOR>\n * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nexport const init = (dropDownSelector) => {\n    const dropdown = document.querySelector(dropDownSelector);\n    dropdown.querySelectorAll('[data-dropdown=\"option\"]').forEach((item) => {\n        item.addEventListener('click', () => {\n            const dropdownSelect = dropdown.querySelector('[data-dropdown=\"select\"]');\n            const dropdownTextElement = dropdown.querySelector('[data-dropdown=\"selecttext\"]');\n            dropdownTextElement.innerText = item.innerText;\n            dropdownSelect.dataset.value = item.dataset.value;\n            const event = new CustomEvent('dropdownSelectionUpdated', {\n                detail: {\n                    dropdownPreference: dropdown.dataset.preference,\n                    newValue: dropdownSelect.dataset.value,\n                }\n            });\n            dropdown.dispatchEvent(event);\n        });\n    });\n};\n"], "names": ["dropDownSelector", "dropdown", "document", "querySelector", "querySelectorAll", "for<PERSON>ach", "item", "addEventListener", "dropdownSelect", "innerText", "dataset", "value", "event", "CustomEvent", "detail", "dropdownPreference", "preference", "newValue", "dispatchEvent"], "mappings": "iKA0BqBA,yBACXC,SAAWC,SAASC,cAAcH,kBACxCC,SAASG,iBAAiB,4BAA4BC,SAASC,OAC3DA,KAAKC,iBAAiB,SAAS,WACrBC,eAAiBP,SAASE,cAAc,4BAClBF,SAASE,cAAc,gCAC/BM,UAAYH,KAAKG,UACrCD,eAAeE,QAAQC,MAAQL,KAAKI,QAAQC,YACtCC,MAAQ,IAAIC,YAAY,2BAA4B,CACtDC,OAAQ,CACJC,mBAAoBd,SAASS,QAAQM,WACrCC,SAAUT,eAAeE,QAAQC,SAGzCV,SAASiB,cAAcN"}