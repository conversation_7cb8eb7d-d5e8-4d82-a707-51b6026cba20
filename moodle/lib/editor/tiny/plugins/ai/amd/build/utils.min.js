define("tiny_ai/utils",["exports","core/modal_events","tiny_ai/renderer","tiny_ai/datamanager","tiny_ai/datahandler/imggen","tiny_ai/datahandler/optimize","tiny_ai/datahandler/start","tiny_ai/datahandler/summarize","tiny_ai/datahandler/translate","tiny_ai/datahandler/tts","tiny_ai/datahandler/itt","core/notification","core/str","local_ai_manager/make_request","tiny_ai/datahandler/basedata","jquery","core/log"],(function(_exports,_modal_events,_renderer,_datamanager,_imggen,_optimize,_start,_summarize,_translate,_tts,_itt,_notification,_str,_make_request,BasedataHandler,_jquery,_log){function _getRequireWildcardCache(nodeInterop){if("function"!=typeof WeakMap)return null;var cacheBabelInterop=new WeakMap,cacheNodeInterop=new WeakMap;return(_getRequireWildcardCache=function(nodeInterop){return nodeInterop?cacheNodeInterop:cacheBabelInterop})(nodeInterop)}function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}
/**
   * Tiny AI utils library.
   *
   * @module      tiny_ai/utils
   * @copyright   2024, ISB Bayern
   * <AUTHOR> Peter Mayer
   * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
   */Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.stripHtmlTags=_exports.setEditorUtils=_exports.init=_exports.getTtsHandler=_exports.getTranslateHandler=_exports.getSummarizeHandler=_exports.getStartHandler=_exports.getRenderer=_exports.getOptimizeHandler=_exports.getIttHandler=_exports.getImggenHandler=_exports.getEditorUtils=_exports.getDatamanager=_exports.getCurrentModalUniqId=_exports.getAiAnswer=_exports.errorAlert=_exports.downloadTextAsFile=_exports.downloadFile=_exports.copyTextToClipboard=_exports.copyFileToClipboard=void 0,_modal_events=_interopRequireDefault(_modal_events),_renderer=_interopRequireDefault(_renderer),_datamanager=_interopRequireDefault(_datamanager),_imggen=_interopRequireDefault(_imggen),_optimize=_interopRequireDefault(_optimize),_start=_interopRequireDefault(_start),_summarize=_interopRequireDefault(_summarize),_translate=_interopRequireDefault(_translate),_tts=_interopRequireDefault(_tts),_itt=_interopRequireDefault(_itt),BasedataHandler=function(obj,nodeInterop){if(!nodeInterop&&obj&&obj.__esModule)return obj;if(null===obj||"object"!=typeof obj&&"function"!=typeof obj)return{default:obj};var cache=_getRequireWildcardCache(nodeInterop);if(cache&&cache.has(obj))return cache.get(obj);var newObj={},hasPropertyDescriptor=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var key in obj)if("default"!==key&&Object.prototype.hasOwnProperty.call(obj,key)){var desc=hasPropertyDescriptor?Object.getOwnPropertyDescriptor(obj,key):null;desc&&(desc.get||desc.set)?Object.defineProperty(newObj,key,desc):newObj[key]=obj[key]}newObj.default=obj,cache&&cache.set(obj,newObj);return newObj}(BasedataHandler),_jquery=_interopRequireDefault(_jquery),_log=_interopRequireDefault(_log);const objectStore={};_exports.init=async(uniqid,mode)=>{objectStore.hasOwnProperty(uniqid)||(objectStore[uniqid]={},objectStore[uniqid].datamanager=new _datamanager.default(uniqid),await BasedataHandler.init(),objectStore[uniqid].imggenhandler=new _imggen.default(uniqid),objectStore[uniqid].optimizehandler=new _optimize.default(uniqid),objectStore[uniqid].starthandler=new _start.default(uniqid),await objectStore[uniqid].starthandler.init(),objectStore[uniqid].summarizehandler=new _summarize.default(uniqid),objectStore[uniqid].translatehandler=new _translate.default(uniqid),objectStore[uniqid].ttshandler=new _tts.default(uniqid),objectStore[uniqid].itthandler=new _itt.default(uniqid),objectStore[uniqid].renderer=new _renderer.default(uniqid,mode))};_exports.getAiAnswer=async function(prompt,purpose){let options=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},result=null;const contextid=options.contextid;delete options.contextid;const component=options.component;delete options.component;try{result=await(0,_make_request.makeRequest)(purpose,prompt,component,contextid,options)}catch(exception){return await(0,_notification.exception)(exception),null}if(200!==result.code){const alertTitle=await(0,_str.getString)("errorwithcode","tiny_ai",result.code),parsedResult=JSON.parse(result.result);return parsedResult.debuginfo&&_log.default.error(parsedResult.debuginfo),await errorAlert(parsedResult.message,alertTitle),null}return result.result};const errorAlert=async function(message){let title=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;null===title&&(title=BasedataHandler.getTinyAiString("generalerror"));const alertModal=await(0,_notification.alert)(title,message);alertModal.getRoot().on(_modal_events.default.hidden,(()=>{document.querySelectorAll("button[data-action]").forEach((button=>{(0,_jquery.default)(button).tooltip("hide")}))}))};_exports.errorAlert=errorAlert;_exports.stripHtmlTags=textWithTags=>{const span=document.createElement("span");return span.innerHTML=textWithTags,span.textContent};_exports.setEditorUtils=(uniqid,editorUtils)=>{objectStore[uniqid].editorUtils=editorUtils};_exports.getEditorUtils=uniqid=>objectStore[uniqid].editorUtils;_exports.getRenderer=uniqid=>objectStore[uniqid].renderer;_exports.getDatamanager=uniqid=>objectStore[uniqid].datamanager;_exports.getImggenHandler=uniqid=>objectStore[uniqid].imggenhandler;_exports.getOptimizeHandler=uniqid=>objectStore[uniqid].optimizehandler;_exports.getStartHandler=uniqid=>objectStore[uniqid].starthandler;_exports.getSummarizeHandler=uniqid=>objectStore[uniqid].summarizehandler;_exports.getTranslateHandler=uniqid=>objectStore[uniqid].translatehandler;_exports.getTtsHandler=uniqid=>objectStore[uniqid].ttshandler;_exports.getIttHandler=uniqid=>objectStore[uniqid].itthandler;_exports.getCurrentModalUniqId=element=>element.closest("[data-tiny_instance_uniqid]").dataset.tiny_instance_uniqid;_exports.copyTextToClipboard=text=>{const clipboardItemData={"text/plain":text};navigator.clipboard.write([new ClipboardItem(clipboardItemData)])};_exports.copyFileToClipboard=async url=>{const data=await fetch(url),blob=await data.blob();if(!ClipboardItem.supports(blob.type))return!1;const clipboardItemData={[blob.type]:blob};return navigator.clipboard.write([new ClipboardItem(clipboardItemData)]),!0};const downloadFile=function(url){let filename=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;const link=document.createElement("a");link.href=url,filename||(filename=url.split("/").pop()),link.download=filename,document.body.appendChild(link),link.click(),document.body.removeChild(link)};_exports.downloadFile=downloadFile;_exports.downloadTextAsFile=text=>{const blob=new Blob([text],{type:"text/plain"}),url=URL.createObjectURL(blob);downloadFile(url,"airesult.txt"),URL.revokeObjectURL(url)}}));

//# sourceMappingURL=utils.min.js.map