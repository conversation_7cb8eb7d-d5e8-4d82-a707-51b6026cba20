{"version": 3, "file": "datamanager.min.js", "sources": ["../src/datamanager.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// <PERSON><PERSON><PERSON> is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\nimport {getEditorUtils} from 'tiny_ai/utils';\n\n/**\n * Tiny AI data manager.\n *\n * @module      tiny_ai/datamanager\n * @copyright   2024, ISB Bayern\n * <AUTHOR>\n * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nexport default class DataManager {\n\n    uniqid = null;\n\n    constructor(uniqid) {\n        this.uniqid = uniqid;\n        this.eventEmitterElement = document.createElement('div');\n    }\n\n    currentTool = null;\n    currentAiResult = null;\n    prompt = null;\n    file = null;\n    options = null;\n    selection = null;\n    selectionImg = null;\n\n    getDefaultOptions() {\n        const defaultOptions = {\n            component: getEditorUtils(this.uniqid).getComponent(),\n            contextid: getEditorUtils(this.uniqid).getContextId()\n        };\n        if (this.getCurrentTool() === 'tts') {\n            defaultOptions.filename = 'audio_' + Math.random().toString(16).slice(2) + '.mp3';\n            defaultOptions.itemid = getEditorUtils(this.uniqid).getDraftItemId();\n        } else if (this.getCurrentTool() === 'imggen') {\n            defaultOptions.filename = 'img_' + Math.random().toString(16).slice(2) + '.png';\n            defaultOptions.itemid = getEditorUtils(this.uniqid).getDraftItemId();\n        }\n        return defaultOptions;\n    }\n\n    setCurrentTool(currentTool) {\n        this.currentTool = currentTool;\n    }\n\n    getCurrentTool() {\n        return this.currentTool;\n    }\n\n    setCurrentText(text) {\n        this.text = text;\n        const textUpdatedEvent = new CustomEvent('textUpdated', {\n            detail: {\n                newText: text\n            }\n        });\n        this.eventEmitterElement.dispatchEvent(textUpdatedEvent);\n    }\n\n    getCurrentText() {\n        return this.text;\n    }\n\n    setCurrentPrompt(prompt) {\n        this.prompt = prompt;\n        const promptUpdatedEvent = new CustomEvent('promptUpdated', {\n            detail: {\n                newPrompt: prompt\n            }\n        });\n        this.eventEmitterElement.dispatchEvent(promptUpdatedEvent);\n    }\n\n    getCurrentPrompt() {\n        return this.prompt;\n    }\n\n    setCurrentFile(file) {\n        this.file = file;\n    }\n\n    getCurrentFile() {\n        return this.file;\n\n    }\n\n    getSelection() {\n        return this.selection;\n    }\n\n    getSelectionText() {\n        const span = document.createElement('span');\n        span.innerHTML = this.selection;\n        return span.textContent;\n    }\n\n    setSelection(selection) {\n        this.selection = selection;\n    }\n\n    getSelectionImg() {\n        return this.selectionImg;\n    }\n\n    setSelectionImg(image) {\n        this.selectionImg = image;\n    }\n\n    getEventEmitterElement() {\n        return this.eventEmitterElement;\n    }\n\n    setCurrentAiResult(aiResult) {\n        this.currentAiResult = aiResult;\n    }\n\n    getCurrentAiResult() {\n        return this.currentAiResult;\n    }\n\n    setCurrentOptions(options) {\n        this.options = options;\n    }\n\n    getCurrentOptions() {\n        const optionsToReturn = this.options === null ? {} : this.options;\n        Object.assign(optionsToReturn, this.getDefaultOptions());\n        return optionsToReturn;\n    }\n\n    reset() {\n        this.setCurrentPrompt('');\n        this.setCurrentOptions(null);\n        this.setCurrentTool(null);\n        this.setCurrentAiResult(null);\n        this.setCurrentFile(null);\n    }\n}\n"], "names": ["constructor", "uniqid", "eventEmitterElement", "document", "createElement", "getDefaultOptions", "defaultOptions", "component", "this", "getComponent", "contextid", "getContextId", "getCurrentTool", "filename", "Math", "random", "toString", "slice", "itemid", "getDraftItemId", "setCurrentTool", "currentTool", "setCurrentText", "text", "textUpdatedEvent", "CustomEvent", "detail", "newText", "dispatchEvent", "getCurrentText", "setCurrentPrompt", "prompt", "promptUpdatedEvent", "newPrompt", "getCurrentPrompt", "setCurrentFile", "file", "getCurrentFile", "getSelection", "selection", "getSelectionText", "span", "innerHTML", "textContent", "setSelection", "getSelectionImg", "selectionImg", "setSelectionImg", "image", "getEventEmitterElement", "setCurrentAiResult", "aiResult", "currentAiResult", "getCurrentAiResult", "setCurrentOptions", "options", "getCurrentOptions", "optionsToReturn", "Object", "assign", "reset"], "mappings": ";;;;;;;;mHA8BIA,YAAYC,sCAFH,yCAOK,6CACI,oCACT,kCACF,qCACG,uCACE,0CACG,WAVNA,OAASA,YACTC,oBAAsBC,SAASC,cAAc,OAWtDC,0BACUC,eAAiB,CACnBC,WAAW,yBAAeC,KAAKP,QAAQQ,eACvCC,WAAW,yBAAeF,KAAKP,QAAQU,sBAEb,QAA1BH,KAAKI,kBACLN,eAAeO,SAAW,SAAWC,KAAKC,SAASC,SAAS,IAAIC,MAAM,GAAK,OAC3EX,eAAeY,QAAS,yBAAeV,KAAKP,QAAQkB,kBACnB,WAA1BX,KAAKI,mBACZN,eAAeO,SAAW,OAASC,KAAKC,SAASC,SAAS,IAAIC,MAAM,GAAK,OACzEX,eAAeY,QAAS,yBAAeV,KAAKP,QAAQkB,kBAEjDb,eAGXc,eAAeC,kBACNA,YAAcA,YAGvBT,wBACWJ,KAAKa,YAGhBC,eAAeC,WACNA,KAAOA,WACNC,iBAAmB,IAAIC,YAAY,cAAe,CACpDC,OAAQ,CACJC,QAASJ,aAGZrB,oBAAoB0B,cAAcJ,kBAG3CK,wBACWrB,KAAKe,KAGhBO,iBAAiBC,aACRA,OAASA,aACRC,mBAAqB,IAAIP,YAAY,gBAAiB,CACxDC,OAAQ,CACJO,UAAWF,eAGd7B,oBAAoB0B,cAAcI,oBAG3CE,0BACW1B,KAAKuB,OAGhBI,eAAeC,WACNA,KAAOA,KAGhBC,wBACW7B,KAAK4B,KAIhBE,sBACW9B,KAAK+B,UAGhBC,yBACUC,KAAOtC,SAASC,cAAc,eACpCqC,KAAKC,UAAYlC,KAAK+B,UACfE,KAAKE,YAGhBC,aAAaL,gBACJA,UAAYA,UAGrBM,yBACWrC,KAAKsC,aAGhBC,gBAAgBC,YACPF,aAAeE,MAGxBC,gCACWzC,KAAKN,oBAGhBgD,mBAAmBC,eACVC,gBAAkBD,SAG3BE,4BACW7C,KAAK4C,gBAGhBE,kBAAkBC,cACTA,QAAUA,QAGnBC,0BACUC,gBAAmC,OAAjBjD,KAAK+C,QAAmB,GAAK/C,KAAK+C,eAC1DG,OAAOC,OAAOF,gBAAiBjD,KAAKH,qBAC7BoD,gBAGXG,aACS9B,iBAAiB,SACjBwB,kBAAkB,WAClBlC,eAAe,WACf8B,mBAAmB,WACnBf,eAAe"}