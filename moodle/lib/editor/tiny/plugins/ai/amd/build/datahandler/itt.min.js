define("tiny_ai/datahandler/itt",["exports","local_ai_manager/config","tiny_ai/datahandler/basedata","tiny_ai/datahandler/base","tiny_ai/utils"],(function(_exports,AiConfig,BasedataHandler,_base,_utils){var obj;function _getRequireWildcardCache(nodeInterop){if("function"!=typeof WeakMap)return null;var cacheBabelInterop=new WeakMap,cacheNodeInterop=new WeakMap;return(_getRequireWildcardCache=function(nodeInterop){return nodeInterop?cacheNodeInterop:cacheBabelInterop})(nodeInterop)}function _interopRequireWildcard(obj,nodeInterop){if(!nodeInterop&&obj&&obj.__esModule)return obj;if(null===obj||"object"!=typeof obj&&"function"!=typeof obj)return{default:obj};var cache=_getRequireWildcardCache(nodeInterop);if(cache&&cache.has(obj))return cache.get(obj);var newObj={},hasPropertyDescriptor=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var key in obj)if("default"!==key&&Object.prototype.hasOwnProperty.call(obj,key)){var desc=hasPropertyDescriptor?Object.getOwnPropertyDescriptor(obj,key):null;desc&&(desc.get||desc.set)?Object.defineProperty(newObj,key,desc):newObj[key]=obj[key]}return newObj.default=obj,cache&&cache.set(obj,newObj),newObj}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0,AiConfig=_interopRequireWildcard(AiConfig),BasedataHandler=_interopRequireWildcard(BasedataHandler),_base=(obj=_base)&&obj.__esModule?obj:{default:obj};
/**
   * Tiny AI data handler for image to text.
   *
   * @module      tiny_ai/datahandler/itt
   * @copyright   2024, ISB Bayern
   * <AUTHOR> Memmel
   * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
   */
class _default extends _base.default{constructor(){super(...arguments),function(obj,key,value){key in obj?Object.defineProperty(obj,key,{value:value,enumerable:!0,configurable:!0,writable:!0}):obj[key]=value}(this,"ittOptions",null)}async loadIttOptions(){if(null===this.ittOptions){const fetchedOptions=await AiConfig.getPurposeOptions("itt");this.ittOptions=JSON.parse(fetchedOptions.options)}}async getAllowedMimetypes(){return await this.loadIttOptions(),this.ittOptions.allowedmimetypes}getOptions(){const options={},datamanager=(0,_utils.getDatamanager)(this.uniqid);return options.image=datamanager.getCurrentFile(),options}getPrompt(tool){return BasedataHandler.getTinyAiString(tool+"_baseprompt")}async getTemplateContext(tool){const context={modalHeadline:BasedataHandler.getTinyAiString(tool+"_headline"),showIcon:!0,tool:tool,textareatype:"prompt",placeholder:BasedataHandler.getTinyAiString(tool+"_placeholder"),insertimagedescription:BasedataHandler.getTinyAiString("imagetotext_insertimage")};return Object.assign(context,BasedataHandler.getShowPromptButtonContext()),Object.assign(context,BasedataHandler.getBackAndGenerateButtonContext()),context}}return _exports.default=_default,_exports.default}));

//# sourceMappingURL=itt.min.js.map