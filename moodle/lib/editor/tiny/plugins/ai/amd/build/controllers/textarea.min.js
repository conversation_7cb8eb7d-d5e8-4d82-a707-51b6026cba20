define("tiny_ai/controllers/textarea",["exports"],(function(_exports){Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.init=void 0;_exports.init=textareaSelector=>{const textarea=document.querySelector(textareaSelector),adjustHeight=()=>{textarea.style.height="40px",textarea.style.height=Math.min(textarea.scrollHeight,342)+"px"};textarea&&(adjustHeight(),textarea.addEventListener("input",adjustHeight),window.addEventListener("resize",adjustHeight))}}));

//# sourceMappingURL=textarea.min.js.map