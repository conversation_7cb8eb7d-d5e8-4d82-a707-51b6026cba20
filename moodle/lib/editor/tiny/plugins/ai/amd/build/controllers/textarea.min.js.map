{"version": 3, "file": "textarea.min.js", "sources": ["../../src/controllers/textarea.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Moodle is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Textarea handler.\n *\n * @module      tiny_ai/controllers/textarea\n * @copyright   2024, ISB Bayern\n * <AUTHOR>\n * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\n\nexport const init = (textareaSelector) => {\n    const textarea = document.querySelector(textareaSelector);\n    const minHeight = 40;\n    const maxHeight = 342;\n\n    const adjustHeight = () => {\n        textarea.style.height = minHeight + 'px';\n        textarea.style.height = Math.min(textarea.scrollHeight, maxHeight) + 'px';\n    };\n\n    if (textarea) {\n        adjustHeight();\n        textarea.addEventListener('input', adjustHeight);\n        window.addEventListener('resize', adjustHeight);\n    }\n};\n"], "names": ["textareaSelector", "textarea", "document", "querySelector", "adjustHeight", "style", "height", "minHeight", "Math", "min", "scrollHeight", "addEventListener", "window"], "mappings": "iKAyBqBA,yBACXC,SAAWC,SAASC,cAAcH,kBAIlCI,aAAe,KACjBH,SAASI,MAAMC,OAASC,OACxBN,SAASI,MAAMC,OAASE,KAAKC,IAAIR,SAASS,aAJ5B,KAIuD,MAGrET,WACAG,eACAH,SAASU,iBAAiB,QAASP,cACnCQ,OAAOD,iBAAiB,SAAUP"}