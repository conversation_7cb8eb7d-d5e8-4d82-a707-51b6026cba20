{"version": 3, "file": "suggestion.min.js", "sources": ["../../src/controllers/suggestion.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Mo<PERSON>le is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Controller for the main selection.\n *\n * @module      tiny_ai/controllers/suggestion\n * @copyright   2024, ISB Bayern\n * <AUTHOR>\n * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport BaseController from 'tiny_ai/controllers/base';\nimport * as BasedataHandler from 'tiny_ai/datahandler/basedata';\nimport {copyTextToClipboard, copyFileToClipboard, downloadFile, downloadTextAsFile, errorAlert} from 'tiny_ai/utils';\nimport {renderWarningBox} from 'local_ai_manager/warningbox';\n\nexport default class extends BaseController {\n\n    async init() {\n        const trashButton = this.footer.querySelector('[data-action=\"delete\"]');\n        const regenerateButton = this.footer.querySelector('[data-action=\"regenerate\"]');\n        const insertBelowButton = this.footer.querySelector('[data-action=\"insertbelow\"]');\n        const replaceButton = this.footer.querySelector('[data-action=\"replace\"]');\n        const insertAtCaretButton = this.footer.querySelector('[data-action=\"insertatcaret\"]');\n        const copyButton = this.footer.querySelector('[data-action=\"copy\"]');\n        const downloadButton = this.footer.querySelector('[data-action=\"download\"]');\n\n        if (trashButton) {\n            trashButton.addEventListener('click', async() => {\n                await this.renderer.renderDismiss();\n            });\n        }\n\n        if (regenerateButton) {\n            regenerateButton.addEventListener('click', async() => {\n                await this.renderer.renderOptimizePrompt();\n            });\n        }\n\n        if (insertBelowButton) {\n            insertBelowButton.addEventListener('click', () => {\n                this.editorUtils.insertAfterContent(this.renderer.renderAiResultForEditor());\n                this.editorUtils.getModal().destroy();\n            });\n        }\n\n        if (replaceButton) {\n            replaceButton.addEventListener('click', () => {\n                this.editorUtils.replaceSelection(this.renderer.renderAiResultForEditor());\n                this.editorUtils.getModal().destroy();\n            });\n        }\n\n        if (insertAtCaretButton) {\n            insertAtCaretButton.addEventListener('click', () => {\n                this.editorUtils.replaceSelection(this.renderer.renderAiResultForEditor());\n                this.editorUtils.getModal().destroy();\n            });\n        }\n\n        if (copyButton) {\n            copyButton.addEventListener('click', async() => {\n                if (this.datamanager.getCurrentTool() === 'tts' || this.datamanager.getCurrentTool() === 'imggen') {\n                    const fileSupported = await copyFileToClipboard(this.datamanager.getCurrentAiResult());\n                    if (!fileSupported) {\n                        await errorAlert(BasedataHandler.getTinyAiString('error_filetypeclipboardnotsupported_text'),\n                            BasedataHandler.getTinyAiString('error_filetypeclipboardnotsupported_title'));\n                        return;\n                    }\n                } else {\n                    copyTextToClipboard(this.datamanager.getCurrentAiResult());\n                }\n            });\n        }\n\n        if (downloadButton) {\n            downloadButton.addEventListener('click', async() => {\n                if (this.datamanager.getCurrentTool() === 'tts' || this.datamanager.getCurrentTool() === 'imggen') {\n                    downloadFile(this.datamanager.getCurrentAiResult());\n                } else {\n                    downloadTextAsFile(this.datamanager.getCurrentAiResult());\n                }\n            });\n        }\n\n        const warningBoxSelector = '[data-rendertarget=\"warningbox\"]';\n        if (document.querySelector(warningBoxSelector)) {\n            await renderWarningBox(warningBoxSelector);\n        }\n    }\n}\n"], "names": ["BaseController", "trashButton", "this", "footer", "querySelector", "regenerateButton", "insert<PERSON>elowButton", "replaceButton", "insertAtCaretButton", "copyButton", "downloadButton", "addEventListener", "async", "renderer", "<PERSON><PERSON><PERSON><PERSON>", "renderOptimizePrompt", "editor<PERSON><PERSON><PERSON>", "insertAfterContent", "renderAiResultForEditor", "getModal", "destroy", "replaceSelection", "datamanager", "getCurrentTool", "getCurrentAiResult", "BasedataHandler", "getTinyAiString", "document"], "mappings": ";;;;;;;;imCA6B6BA,iCAGfC,YAAcC,KAAKC,OAAOC,cAAc,0BACxCC,iBAAmBH,KAAKC,OAAOC,cAAc,8BAC7CE,kBAAoBJ,KAAKC,OAAOC,cAAc,+BAC9CG,cAAgBL,KAAKC,OAAOC,cAAc,2BAC1CI,oBAAsBN,KAAKC,OAAOC,cAAc,iCAChDK,WAAaP,KAAKC,OAAOC,cAAc,wBACvCM,eAAiBR,KAAKC,OAAOC,cAAc,4BAE7CH,aACAA,YAAYU,iBAAiB,SAASC,gBAC5BV,KAAKW,SAASC,mBAIxBT,kBACAA,iBAAiBM,iBAAiB,SAASC,gBACjCV,KAAKW,SAASE,0BAIxBT,mBACAA,kBAAkBK,iBAAiB,SAAS,UACnCK,YAAYC,mBAAmBf,KAAKW,SAASK,gCAC7CF,YAAYG,WAAWC,aAIhCb,eACAA,cAAcI,iBAAiB,SAAS,UAC/BK,YAAYK,iBAAiBnB,KAAKW,SAASK,gCAC3CF,YAAYG,WAAWC,aAIhCZ,qBACAA,oBAAoBG,iBAAiB,SAAS,UACrCK,YAAYK,iBAAiBnB,KAAKW,SAASK,gCAC3CF,YAAYG,WAAWC,aAIhCX,YACAA,WAAWE,iBAAiB,SAASC,aACS,QAAtCV,KAAKoB,YAAYC,kBAAoE,WAAtCrB,KAAKoB,YAAYC,iBAA+B,WACnE,8BAAoBrB,KAAKoB,YAAYE,wCAEvD,qBAAWC,gBAAgBC,gBAAgB,4CAC7CD,gBAAgBC,gBAAgB,iFAIpBxB,KAAKoB,YAAYE,yBAK7Cd,gBACAA,eAAeC,iBAAiB,SAASC,UACK,QAAtCV,KAAKoB,YAAYC,kBAAoE,WAAtCrB,KAAKoB,YAAYC,yCACnDrB,KAAKoB,YAAYE,oDAEXtB,KAAKoB,YAAYE,yBAM5CG,SAASvB,cADc,2CAEjB,gCAFiB"}