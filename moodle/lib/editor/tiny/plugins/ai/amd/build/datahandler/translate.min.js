define("tiny_ai/datahandler/translate",["exports","tiny_ai/datahandler/basedata","tiny_ai/datahandler/base","tiny_ai/utils","core/config","core/str"],(function(_exports,BasedataHandler,_base,_utils,_config,_str){function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}function _getRequireWildcardCache(nodeInterop){if("function"!=typeof WeakMap)return null;var cacheBabelInterop=new WeakMap,cacheNodeInterop=new WeakMap;return(_getRequireWildcardCache=function(nodeInterop){return nodeInterop?cacheNodeInterop:cacheBabelInterop})(nodeInterop)}function _defineProperty(obj,key,value){return key in obj?Object.defineProperty(obj,key,{value:value,enumerable:!0,configurable:!0,writable:!0}):obj[key]=value,obj}
/**
   * Tiny AI data manager.
   *
   * @module      tiny_ai/datahandler/translate
   * @copyright   2024, ISB Bayern
   * <AUTHOR>
   * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
   */Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0,BasedataHandler=function(obj,nodeInterop){if(!nodeInterop&&obj&&obj.__esModule)return obj;if(null===obj||"object"!=typeof obj&&"function"!=typeof obj)return{default:obj};var cache=_getRequireWildcardCache(nodeInterop);if(cache&&cache.has(obj))return cache.get(obj);var newObj={},hasPropertyDescriptor=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var key in obj)if("default"!==key&&Object.prototype.hasOwnProperty.call(obj,key)){var desc=hasPropertyDescriptor?Object.getOwnPropertyDescriptor(obj,key):null;desc&&(desc.get||desc.set)?Object.defineProperty(newObj,key,desc):newObj[key]=obj[key]}newObj.default=obj,cache&&cache.set(obj,newObj);return newObj}(BasedataHandler),_base=_interopRequireDefault(_base),_config=_interopRequireDefault(_config);class _default extends _base.default{constructor(uniqid){super(uniqid),_defineProperty(this,"targetLanguageCodes",["de","fr","it","es","cs","zh","ru","uk","el","la","tr","ro","pl","bg","ar","sq","bs","sr","hr","ku","fa","ps","sk","hu"]),_defineProperty(this,"targetLanguageOptions",[]),_defineProperty(this,"targetLanguage",null),this.initTargetLanguages()}setTargetLanguage(targetLanguage){this.targetLanguage=targetLanguage}async getPrompt(selectionText){const selectedLanguageEntry=this.targetLanguageOptions.filter((languageEntry=>languageEntry.key===this.targetLanguage))[0];let prompt=await(0,_str.getString)("translate_baseprompt","tiny_ai",selectedLanguageEntry.value);return prompt+=": "+selectionText,prompt}getTemplateContext(){const translateHandler=(0,_utils.getTranslateHandler)(this.uniqid),context={modalHeadline:BasedataHandler.getTinyAiString("translate_headline"),showIcon:!0,tool:"translate"},targetLanguageDropdownContext={preference:"targetLanguage"};targetLanguageDropdownContext.dropdownDefault=translateHandler.targetLanguageOptions[0].value,targetLanguageDropdownContext.dropdownDefaultValue=translateHandler.targetLanguageOptions[0].key,targetLanguageDropdownContext.dropdownDescription=BasedataHandler.getTinyAiString("targetlanguage");const targetLanguageDropdownOptions=[];return translateHandler.targetLanguageOptions.forEach((languageEntry=>{targetLanguageDropdownOptions.push({optionValue:languageEntry.key,optionLabel:languageEntry.value})})),targetLanguageDropdownContext.dropdownOptions=targetLanguageDropdownOptions,Object.assign(context,{modalDropdowns:[targetLanguageDropdownContext]}),Object.assign(context,BasedataHandler.getShowPromptButtonContext()),Object.assign(context,BasedataHandler.getBackAndGenerateButtonContext()),context}initTargetLanguages(){const currentUserLanguage=_config.default.language.substring(0,2),languageNameInCurrentUserLanguage=new Intl.DisplayNames([currentUserLanguage],{type:"language"}),firstLanguages=[{key:"en",value:languageNameInCurrentUserLanguage.of("en")}];if("en"!==currentUserLanguage&&this.targetLanguageCodes.includes(currentUserLanguage)){firstLanguages.push({key:currentUserLanguage,value:languageNameInCurrentUserLanguage.of(currentUserLanguage)});const index=this.targetLanguageCodes.indexOf(currentUserLanguage);this.targetLanguageCodes.splice(index,1)}this.targetLanguageCodes.forEach((languageCode=>{this.targetLanguageOptions[languageCode]=languageNameInCurrentUserLanguage.of(languageCode)}));const sortedLanguages=Object.entries(this.targetLanguageOptions).sort(((a,b)=>a[1].localeCompare(b[1]))).map((_ref=>{let[key,value]=_ref;return{key:key,value:value}}));this.targetLanguageOptions=[...firstLanguages,...sortedLanguages]}}return _exports.default=_default,_exports.default}));

//# sourceMappingURL=translate.min.js.map