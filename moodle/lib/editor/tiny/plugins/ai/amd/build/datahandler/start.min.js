define("tiny_ai/datahandler/start",["exports","core/config","core/str","tiny_ai/constants","tiny_ai/datahandler/basedata","tiny_ai/datahandler/base","local_ai_manager/config","tiny_ai/utils"],(function(_exports,config,_str,_constants,BasedataHandler,_base,_config2,_utils){var obj;function _getRequireWildcardCache(nodeInterop){if("function"!=typeof WeakMap)return null;var cacheBabelInterop=new WeakMap,cacheNodeInterop=new WeakMap;return(_getRequireWildcardCache=function(nodeInterop){return nodeInterop?cacheNodeInterop:cacheBabelInterop})(nodeInterop)}function _interopRequireWildcard(obj,nodeInterop){if(!nodeInterop&&obj&&obj.__esModule)return obj;if(null===obj||"object"!=typeof obj&&"function"!=typeof obj)return{default:obj};var cache=_getRequireWildcardCache(nodeInterop);if(cache&&cache.has(obj))return cache.get(obj);var newObj={},hasPropertyDescriptor=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var key in obj)if("default"!==key&&Object.prototype.hasOwnProperty.call(obj,key)){var desc=hasPropertyDescriptor?Object.getOwnPropertyDescriptor(obj,key):null;desc&&(desc.get||desc.set)?Object.defineProperty(newObj,key,desc):newObj[key]=obj[key]}return newObj.default=obj,cache&&cache.set(obj,newObj),newObj}function _defineProperty(obj,key,value){return key in obj?Object.defineProperty(obj,key,{value:value,enumerable:!0,configurable:!0,writable:!0}):obj[key]=value,obj}
/**
   * Tiny AI data handler for start page.
   *
   * @module      tiny_ai/datahandler/start
   * @copyright   2024, ISB Bayern
   * <AUTHOR> Memmel
   * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
   */Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0,config=_interopRequireWildcard(config),BasedataHandler=_interopRequireWildcard(BasedataHandler),_base=(obj=_base)&&obj.__esModule?obj:{default:obj};class _default extends _base.default{constructor(){super(...arguments),_defineProperty(this,"stringKeys",["error_limitreached","error_pleaseconfirm","error_purposenotconfigured","error_tenantdisabled","error_unavailable_noselection","error_unavailable_selection","error_userlocked","error_usernotconfirmed"]),_defineProperty(this,"aiConfig",null),_defineProperty(this,"strings",new Map)}async init(){this.aiConfig=await(0,_config2.getAiConfig)();const stringRequest=this.stringKeys.map((key=>({key:key,component:"local_ai_manager"}))),fetchedStrings=await(0,_str.getStrings)(stringRequest);for(let i=0;i<this.stringKeys.length;i++)this.strings.set(this.stringKeys[i],fetchedStrings[i]);const tinyNotAvailableString=await(0,_str.getString)("error_tiny_ai_notavailable","tiny_ai");this.strings.set("error_editor_notavailable",tinyNotAvailableString);const confirmLink=document.createElement("a");confirmLink.href="".concat(config.wwwroot,"/local/ai_manager/confirm_ai_usage.php"),confirmLink.innerText=this.strings.get("error_pleaseconfirm"),confirmLink.target="_blank",this.strings.set("combinedusernotconfirmederror",this.strings.get("error_usernotconfirmed")+" "+confirmLink.outerHTML)}getPurposeConfig(tool){if(null===this.aiConfig)throw new Error("Coding error: init function was not called before accessing this.getPurposeConfig!");const toolPurpose=_constants.constants.toolPurposeMapping[tool];return this.aiConfig.purposes.filter((purpose=>purpose.purpose===toolPurpose))[0]}isTinyAiDisabled(){return this.aiConfig.tenantenabled?this.aiConfig.userconfirmed?this.aiConfig.userlocked?this.strings.get("error_userlocked"):"":this.strings.get("combinedusernotconfirmederror"):this.strings.get("error_tenantdisabled")}isToolDisabled(tool){if(this.isTinyAiDisabled())return this.isTinyAiDisabled();const purposeInfo=this.getPurposeConfig(tool);return purposeInfo.isconfigured?purposeInfo.limitreached?this.strings.get("error_limitreached"):"":this.strings.get("error_purposenotconfigured")}isToolHidden(tool){const purposeInfo=this.getPurposeConfig(tool);if("role_basic"===this.aiConfig.role){if(!this.aiConfig.tenantenabled)return!0;if(!purposeInfo.isconfigured)return!0}return!1}async getTemplateContext(editorUtils){let toolButtons=[];"role_basic"===this.aiConfig.role&&this.isTinyAiDisabled()&&(await(0,_utils.errorAlert)(await(0,_str.getString)("error_tiny_ai_notavailable","tiny_ai")+"<br/>"+this.isTinyAiDisabled()),editorUtils.getModal().destroy()),this.isToolHidden("summarize")||toolButtons.push({toolname:"summarize",tool:BasedataHandler.getTinyAiString("toolname_summarize"),customicon:!0,iconname:"shorten",disabled:this.isToolDisabled("summarize").length>0,tooltip:(0,_utils.stripHtmlTags)(this.isToolDisabled("summarize")),action:"loadsummarize"}),this.isToolHidden("translate")||toolButtons.push({toolname:"translate",tool:BasedataHandler.getTinyAiString("toolname_translate"),iconname:"language",iconstyle:"solid",disabled:this.isToolDisabled("translate").length>0,tooltip:(0,_utils.stripHtmlTags)(this.isToolDisabled("translate")),action:"loadtranslate"}),this.isToolHidden("describe")||toolButtons.push({toolname:"describe",tool:BasedataHandler.getTinyAiString("toolname_describe"),customicon:!0,iconname:"extend",disabled:this.isToolDisabled("describe").length>0,tooltip:(0,_utils.stripHtmlTags)(this.isToolDisabled("describe")),action:"loaddescribe"}),this.isToolHidden("tts")||toolButtons.push({toolname:"tts",tool:BasedataHandler.getTinyAiString("toolname_tts"),iconstyle:"solid",iconname:"microphone",disabled:this.isToolDisabled("tts").length>0,tooltip:(0,_utils.stripHtmlTags)(this.isToolDisabled("tts")),action:"loadtts"}),this.isToolHidden("imggen")||toolButtons.push({toolname:"imggen",tool:BasedataHandler.getTinyAiString("toolname_imggen"),iconstyle:"solid",iconname:"image",disabled:this.isToolDisabled("imggen").length>0,tooltip:(0,_utils.stripHtmlTags)(this.isToolDisabled("imggen")),action:"loadimggen"}),this.isToolHidden("describeimg")||toolButtons.push({toolname:"describeimg",tool:BasedataHandler.getTinyAiString("toolname_describeimg"),iconstyle:"solid",iconname:"file-image",disabled:this.isToolDisabled("describeimg").length>0,tooltip:(0,_utils.stripHtmlTags)(this.isToolDisabled("describeimg")),action:"loaddescribeimg"}),this.isToolHidden("imagetotext")||toolButtons.push({toolname:"imagetotext",tool:BasedataHandler.getTinyAiString("toolname_imagetotext"),iconstyle:"solid",iconname:"signature",disabled:this.isToolDisabled("imagetotext").length>0,tooltip:(0,_utils.stripHtmlTags)(this.isToolDisabled("imagetotext")),action:"loadimagetotext"}),toolButtons.sort(((a,b)=>a.disabled&&!b.disabled?1:b.disabled&&!a.disabled?-1:0));const templateContext={showIcon:!0,modalHeadline:BasedataHandler.getTinyAiString("mainselection_heading"),action:"loadfreeprompt",modalButtons:toolButtons,freeprompthidden:!0};return Object.assign(templateContext,BasedataHandler.getInputContext()),this.isTinyAiDisabled()&&(templateContext.input[0].disabled=!0,templateContext.input[0].hasError=!0,templateContext.input[0].errorMessage=this.isTinyAiDisabled()),this.isToolDisabled("freeprompt")&&(templateContext.input[0].disabled=!0),templateContext}}return _exports.default=_default,_exports.default}));

//# sourceMappingURL=start.min.js.map