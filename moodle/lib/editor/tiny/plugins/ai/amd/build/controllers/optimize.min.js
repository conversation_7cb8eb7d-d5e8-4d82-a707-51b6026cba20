define("tiny_ai/controllers/optimize",["exports","tiny_ai/controllers/base"],(function(_exports,_base){var obj;
/**
   * Controller for the main selection.
   *
   * @module      tiny_ai/controllers/optimize
   * @copyright   2024, ISB Bayern
   * <AUTHOR>
   * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
   */Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0,_base=(obj=_base)&&obj.__esModule?obj:{default:obj};class _default extends _base.default{async init(){const backButton=this.footer.querySelector('[data-action="back"]'),generateButton=this.footer.querySelector('[data-action="generate"]');backButton&&backButton.addEventListener("click",(async()=>{await this.renderer.renderSuggestion()})),generateButton&&generateButton.addEventListener("click",(async()=>{null!==await this.generateAiAnswer()&&await this.renderer.renderSuggestion()}))}}return _exports.default=_default,_exports.default}));

//# sourceMappingURL=optimize.min.js.map