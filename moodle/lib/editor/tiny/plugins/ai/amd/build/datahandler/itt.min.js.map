{"version": 3, "file": "itt.min.js", "sources": ["../../src/datahandler/itt.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// <PERSON><PERSON>le is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\nimport * as AiConfig from 'local_ai_manager/config';\nimport * as BasedataHandler from 'tiny_ai/datahandler/basedata';\nimport BaseHandler from 'tiny_ai/datahandler/base';\nimport {getDatamanager} from 'tiny_ai/utils';\n\n/**\n * Tiny AI data handler for image to text.\n *\n * @module      tiny_ai/datahandler/itt\n * @copyright   2024, ISB Bayern\n * <AUTHOR>\n * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nexport default class extends BaseHandler {\n\n    ittOptions = null;\n\n    async loadIttOptions() {\n        if (this.ittOptions === null) {\n            const fetchedOptions = await AiConfig.getPurposeOptions('itt');\n            this.ittOptions = JSON.parse(fetchedOptions.options);\n        }\n    }\n\n    async getAllowedMimetypes() {\n        await this.loadIttOptions();\n        return this.ittOptions.allowedmimetypes;\n    }\n\n    getOptions() {\n        const options = {};\n        const datamanager = getDatamanager(this.uniqid);\n        options.image = datamanager.getCurrentFile();\n        return options;\n    }\n\n    /**\n     * Get the prompt.\n     *\n     * @param {string} tool the tool to generate the prompt for, can be 'describeimage' and 'imagetotext'\n     */\n    getPrompt(tool) {\n        return BasedataHandler.getTinyAiString(tool + '_baseprompt');\n    }\n\n    /**\n     * Get the rendering context.\n     *\n     * @param {string} tool the tool to generate the context for, can be 'describeimage' and 'imagetotext'\n     */\n    async getTemplateContext(tool) {\n        const context = {\n            modalHeadline: BasedataHandler.getTinyAiString(tool + '_headline'),\n            showIcon: true,\n            tool: tool,\n            textareatype: 'prompt',\n            placeholder: BasedataHandler.getTinyAiString(tool + '_placeholder'),\n            insertimagedescription: BasedataHandler.getTinyAiString('imagetotext_insertimage')\n        };\n\n        Object.assign(context, BasedataHandler.getShowPromptButtonContext());\n\n        Object.assign(context, BasedataHandler.getBackAndGenerateButtonContext());\n        return context;\n    }\n}\n"], "names": ["BaseHandler", "this", "ittOptions", "fetchedOptions", "AiConfig", "getPurposeOptions", "JSON", "parse", "options", "loadIttOptions", "allowedmimetypes", "getOptions", "datamanager", "uniqid", "image", "getCurrentFile", "getPrompt", "tool", "BasedataHandler", "getTinyAiString", "context", "modalHeadline", "showIcon", "textareatype", "placeholder", "insertimagedescription", "Object", "assign", "getShowPromptButtonContext", "getBackAndGenerateButtonContext"], "mappings": ";;;;;;;;;uBA6B6BA,4MAEZ,gCAGe,OAApBC,KAAKC,WAAqB,OACpBC,qBAAuBC,SAASC,kBAAkB,YACnDH,WAAaI,KAAKC,MAAMJ,eAAeK,mDAK1CP,KAAKQ,iBACJR,KAAKC,WAAWQ,iBAG3BC,mBACUH,QAAU,GACVI,aAAc,yBAAeX,KAAKY,eACxCL,QAAQM,MAAQF,YAAYG,iBACrBP,QAQXQ,UAAUC,aACCC,gBAAgBC,gBAAgBF,KAAO,wCAQzBA,YACfG,QAAU,CACZC,cAAeH,gBAAgBC,gBAAgBF,KAAO,aACtDK,UAAU,EACVL,KAAMA,KACNM,aAAc,SACdC,YAAaN,gBAAgBC,gBAAgBF,KAAO,gBACpDQ,uBAAwBP,gBAAgBC,gBAAgB,mCAG5DO,OAAOC,OAAOP,QAASF,gBAAgBU,8BAEvCF,OAAOC,OAAOP,QAASF,gBAAgBW,mCAChCT"}