{"version": 3, "file": "renderer.min.js", "sources": ["../src/renderer.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Moodle is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Tiny AI utils library.\n *\n * @module      tiny_ai/renderer\n * @copyright   2024, ISB Bayern\n * <AUTHOR>\n * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport {renderInfoBox} from 'local_ai_manager/infobox';\nimport {renderUserQuota} from 'local_ai_manager/userquota';\nimport * as BasedataHandler from 'tiny_ai/datahandler/basedata';\nimport Templates from 'core/templates';\nimport $ from 'jquery';\nimport {\n    getEditorUtils,\n    getDatamanager,\n    getImggenHandler,\n    getOptimizeHandler,\n    getStartHandler,\n    getSummarizeHandler,\n    getTranslateHandler,\n    getTtsHandler,\n    getIttHandler\n} from 'tiny_ai/utils';\nimport {constants} from 'tiny_ai/constants';\n\nexport default class {\n\n    uniqid = null;\n    mode = null;\n    datamanager = null;\n\n    constructor(uniqid, mode) {\n        this.uniqid = uniqid;\n        this.mode = mode;\n        this.datamanager = getDatamanager(uniqid);\n    }\n\n    async renderStart() {\n        this.datamanager.reset();\n        const templateContext = await getStartHandler(this.uniqid).getTemplateContext(getEditorUtils(this.uniqid));\n        await this.renderModalContent('moodle-modal-body-start', 'moodle-modal-footer-info', templateContext);\n    }\n\n\n    async renderSummarize() {\n        const templateContext = getSummarizeHandler(this.uniqid).getTemplateContext('summarize');\n        await this.renderModalContent('moodle-modal-body-preferences', 'moodle-modal-footer-generate', templateContext);\n    }\n\n\n    async renderTranslate() {\n        const templateContext = getTranslateHandler(this.uniqid).getTemplateContext();\n        await this.renderModalContent('moodle-modal-body-preferences', 'moodle-modal-footer-generate', templateContext);\n    }\n\n    async renderDescribe() {\n        const templateContext = getSummarizeHandler(this.uniqid).getTemplateContext('describe');\n        await this.renderModalContent('moodle-modal-body-preferences', 'moodle-modal-footer-generate', templateContext);\n    }\n\n    async renderTts() {\n        const templateContext = await getTtsHandler(this.uniqid).getTemplateContext();\n        await this.renderModalContent('moodle-modal-body-preferences', 'moodle-modal-footer-generate', templateContext);\n    }\n\n    async renderImggen() {\n        const templateContext = await getImggenHandler(this.uniqid).getTemplateContext();\n        await this.renderModalContent('moodle-modal-body-mediageneration', 'moodle-modal-footer-generate', templateContext);\n    }\n\n    async renderDescribeimg() {\n        const templateContext = await getIttHandler(this.uniqid).getTemplateContext('describeimg');\n        await this.renderModalContent('moodle-modal-body-itt', 'moodle-modal-footer-generate', templateContext);\n    }\n\n    async renderImagetotext() {\n        const templateContext = await getIttHandler(this.uniqid).getTemplateContext('imagetotext');\n        await this.renderModalContent('moodle-modal-body-itt', 'moodle-modal-footer-generate', templateContext);\n    }\n\n    async renderLoading() {\n        const templateContext = {};\n        templateContext.modalHeadline = BasedataHandler.getTinyAiString('aigenerating');\n        await this.renderModalContent('moodle-modal-body-loading', 'moodle-modal-footer-empty', templateContext);\n    }\n\n\n    async renderSuggestion() {\n        const templateContext = {};\n        templateContext.modalHeadline = BasedataHandler.getTinyAiString('aisuggestion');\n        // TODO Eventually do not use the same rendering in the suggestion like in the course, or just leave it because we\n        //  consider it beautiful\n        templateContext.resultText = this.renderAiResultForEditor();\n\n        if (this.mode === constants.modalModes.editor) {\n            Object.assign(templateContext, BasedataHandler.getReplaceButtonsContext(\n                this.datamanager.getSelectionText().length > 0));\n        } else {\n            Object.assign(templateContext, BasedataHandler.getCopyAndDownloadButtonsContext());\n        }\n        await this.renderModalContent('moodle-modal-body-suggestion', 'moodle-modal-footer-replace', templateContext);\n    }\n\n    async renderOptimizePrompt() {\n        const templateContext = getOptimizeHandler(this.uniqid).getTemplateContext();\n        await this.renderModalContent('moodle-modal-body-optimize', 'moodle-modal-footer-generate', templateContext);\n    }\n\n\n    async renderDismiss() {\n        const templateContext = {\n            modalHeadline: '',\n            centeredHeadline: BasedataHandler.getTinyAiString('dismisssuggestion'),\n            showIcon: false,\n            buttons: [\n                {\n                    hasText: true,\n                    buttonText: BasedataHandler.getTinyAiString('cancel'),\n                    iconLeft: false,\n                    iconRight: false,\n                    primary: false,\n                    secondary: true,\n                    tertiary: false,\n                    action: 'canceldismiss'\n                },\n                {\n                    hasText: true,\n                    buttonText: BasedataHandler.getTinyAiString('dismiss'),\n                    iconLeft: false,\n                    iconRight: false,\n                    primary: true,\n                    secondary: false,\n                    tertiary: false,\n                    action: 'dismiss'\n                }\n            ]\n        };\n        await this.renderModalContent('moodle-modal-body-dismiss', 'moodle-modal-footer-empty', templateContext);\n    }\n\n\n    renderAiResultForEditor() {\n        let html;\n        switch (this.datamanager.getCurrentTool()) {\n            case 'tts':\n            case 'audiogen': {\n                const audioPlayer = document.createElement('audio');\n                audioPlayer.controls = 'controls';\n                audioPlayer.src = this.datamanager.getCurrentAiResult();\n                audioPlayer.type = 'audio/mpeg';\n                html = audioPlayer.outerHTML;\n                break;\n            }\n            case 'imggen': {\n                const img = document.createElement('img');\n                img.src = this.datamanager.getCurrentAiResult();\n                img.classList.add('mw-100');\n                html = img.outerHTML;\n                break;\n            }\n            default: {\n                html = this.datamanager.getCurrentAiResult();\n            }\n        }\n        return html;\n    }\n\n    /**\n     * Re-renders the content auf the modal once it has been created.\n     *\n     * @param {string} bodyComponentTemplate the name of the body template to use (without the prefix 'tiny_ai/components/')\n     * @param {string} footerComponentTemplate the name of the footer template to use (without the prefix 'tiny_ai/components/')\n     * @param {object} templateContext the template context being used for all partial templates\n     * @returns {Promise<void>} the async promise\n     */\n    async renderModalContent(bodyComponentTemplate, footerComponentTemplate, templateContext) {\n        templateContext.tinyinstanceuniqid = this.uniqid;\n        const modal = getEditorUtils(this.uniqid).getModal();\n        // Remove all eventually remaining tooltips before rendering a new view.\n        document.querySelectorAll('button[data-action]').forEach(button => {\n            $(button).tooltip('hide');\n        });\n        const result = await Promise.all([\n            Templates.renderForPromise('tiny_ai/components/moodle-modal-header-title', templateContext),\n            Templates.renderForPromise('tiny_ai/components/' + bodyComponentTemplate, templateContext),\n            Templates.renderForPromise('tiny_ai/components/' + footerComponentTemplate, templateContext)\n        ]);\n        if (templateContext.hasOwnProperty('modalHeadline')) {\n            // If there is no headline specified, we keep the old one.\n            modal.setTitle(result[0].html);\n        }\n        // Hide all eventually still existing tooltips first, because they show on 'hover' and\n        // 'focus'. So we need to remove them before removing the corresponding buttons from the DOM.\n        // Boostrap 4 still using jQuery for tooltips, so we need jQuery here.\n        document.querySelectorAll('button[data-action]').forEach(button => {\n            $(button).tooltip('hide');\n        });\n        modal.setBody(result[1].html);\n        modal.setFooter(result[2].html);\n        result.forEach((item) => {\n            Templates.runTemplateJS(item.js);\n        });\n        modal.getRoot().attr('data-tiny_ai_uniqid', this.uniqid);\n        await this.insertInfoBox();\n        await this.insertUserQuotaBox();\n        document.querySelectorAll('button[data-action]').forEach(button => {\n            button.addEventListener('click', event => {\n                $(event.target).closest('button[data-action]').tooltip('hide');\n            });\n        });\n    }\n\n    async insertInfoBox() {\n        const infoBoxSelector = '[data-rendertarget=\"infobox\"]';\n        if (document.querySelector(infoBoxSelector)) {\n            await renderInfoBox('tiny_ai', getEditorUtils(this.uniqid).getUserId(), infoBoxSelector,\n                ['singleprompt', 'translate', 'tts', 'imggen', 'itt']);\n        }\n    }\n\n    async insertUserQuotaBox() {\n        const usageBoxSelector = '[data-rendertarget=\"usageinfo\"]';\n        if (document.querySelector(usageBoxSelector)) {\n            await renderUserQuota(usageBoxSelector, ['singleprompt', 'translate', 'tts', 'imggen', 'itt']);\n        }\n    }\n}\n"], "names": ["constructor", "uniqid", "mode", "datamanager", "reset", "templateContext", "this", "getTemplateContext", "renderModalContent", "modalHeadline", "BasedataHandler", "getTinyAiString", "resultText", "renderAiResultForEditor", "constants", "modalModes", "editor", "Object", "assign", "getReplaceButtonsContext", "getSelectionText", "length", "getCopyAndDownloadButtonsContext", "centeredHeadline", "showIcon", "buttons", "hasText", "buttonText", "iconLeft", "iconRight", "primary", "secondary", "tertiary", "action", "html", "getCurrentTool", "audioPlayer", "document", "createElement", "controls", "src", "getCurrentAiResult", "type", "outerHTML", "img", "classList", "add", "bodyComponentTemplate", "footerComponentTemplate", "tinyinstanceuniqid", "modal", "getModal", "querySelectorAll", "for<PERSON>ach", "button", "tooltip", "result", "Promise", "all", "Templates", "renderForPromise", "hasOwnProperty", "setTitle", "setBody", "setFooter", "item", "runTemplateJS", "js", "getRoot", "attr", "insertInfoBox", "insertUserQuotaBox", "addEventListener", "event", "target", "closest", "querySelector", "getUserId"], "mappings": "mpDAgDIA,YAAYC,OAAQC,oCAJX,kCACF,yCACO,WAGLD,OAASA,YACTC,KAAOA,UACPC,aAAc,yBAAeF,iCAI7BE,YAAYC,cACXC,sBAAwB,0BAAgBC,KAAKL,QAAQM,oBAAmB,yBAAeD,KAAKL,eAC5FK,KAAKE,mBAAmB,0BAA2B,2BAA4BH,+CAK/EA,iBAAkB,8BAAoBC,KAAKL,QAAQM,mBAAmB,mBACtED,KAAKE,mBAAmB,gCAAiC,+BAAgCH,+CAKzFA,iBAAkB,8BAAoBC,KAAKL,QAAQM,2BACnDD,KAAKE,mBAAmB,gCAAiC,+BAAgCH,8CAIzFA,iBAAkB,8BAAoBC,KAAKL,QAAQM,mBAAmB,kBACtED,KAAKE,mBAAmB,gCAAiC,+BAAgCH,yCAIzFA,sBAAwB,wBAAcC,KAAKL,QAAQM,2BACnDD,KAAKE,mBAAmB,gCAAiC,+BAAgCH,4CAIzFA,sBAAwB,2BAAiBC,KAAKL,QAAQM,2BACtDD,KAAKE,mBAAmB,oCAAqC,+BAAgCH,iDAI7FA,sBAAwB,wBAAcC,KAAKL,QAAQM,mBAAmB,qBACtED,KAAKE,mBAAmB,wBAAyB,+BAAgCH,iDAIjFA,sBAAwB,wBAAcC,KAAKL,QAAQM,mBAAmB,qBACtED,KAAKE,mBAAmB,wBAAyB,+BAAgCH,6CAIjFA,gBAAkB,GACxBA,gBAAgBI,cAAgBC,gBAAgBC,gBAAgB,sBAC1DL,KAAKE,mBAAmB,4BAA6B,4BAA6BH,gDAKlFA,gBAAkB,GACxBA,gBAAgBI,cAAgBC,gBAAgBC,gBAAgB,gBAGhEN,gBAAgBO,WAAaN,KAAKO,0BAE9BP,KAAKJ,OAASY,qBAAUC,WAAWC,OACnCC,OAAOC,OAAOb,gBAAiBK,gBAAgBS,yBAC3Cb,KAAKH,YAAYiB,mBAAmBC,OAAS,IAEjDJ,OAAOC,OAAOb,gBAAiBK,gBAAgBY,0CAE7ChB,KAAKE,mBAAmB,+BAAgC,8BAA+BH,oDAIvFA,iBAAkB,6BAAmBC,KAAKL,QAAQM,2BAClDD,KAAKE,mBAAmB,6BAA8B,+BAAgCH,6CAKtFA,gBAAkB,CACpBI,cAAe,GACfc,iBAAkBb,gBAAgBC,gBAAgB,qBAClDa,UAAU,EACVC,QAAS,CACL,CACIC,SAAS,EACTC,WAAYjB,gBAAgBC,gBAAgB,UAC5CiB,UAAU,EACVC,WAAW,EACXC,SAAS,EACTC,WAAW,EACXC,UAAU,EACVC,OAAQ,iBAEZ,CACIP,SAAS,EACTC,WAAYjB,gBAAgBC,gBAAgB,WAC5CiB,UAAU,EACVC,WAAW,EACXC,SAAS,EACTC,WAAW,EACXC,UAAU,EACVC,OAAQ,mBAId3B,KAAKE,mBAAmB,4BAA6B,4BAA6BH,iBAI5FQ,8BACQqB,YACI5B,KAAKH,YAAYgC,sBAChB,UACA,kBACKC,YAAcC,SAASC,cAAc,SAC3CF,YAAYG,SAAW,WACvBH,YAAYI,IAAMlC,KAAKH,YAAYsC,qBACnCL,YAAYM,KAAO,aACnBR,KAAOE,YAAYO,oBAGlB,gBACKC,IAAMP,SAASC,cAAc,OACnCM,IAAIJ,IAAMlC,KAAKH,YAAYsC,qBAC3BG,IAAIC,UAAUC,IAAI,UAClBZ,KAAOU,IAAID,wBAIXT,KAAO5B,KAAKH,YAAYsC,4BAGzBP,8BAWca,sBAAuBC,wBAAyB3C,iBACrEA,gBAAgB4C,mBAAqB3C,KAAKL,aACpCiD,OAAQ,yBAAe5C,KAAKL,QAAQkD,WAE1Cd,SAASe,iBAAiB,uBAAuBC,SAAQC,6BACnDA,QAAQC,QAAQ,iBAEhBC,aAAeC,QAAQC,IAAI,CAC7BC,mBAAUC,iBAAiB,+CAAgDvD,iBAC3EsD,mBAAUC,iBAAiB,sBAAwBb,sBAAuB1C,iBAC1EsD,mBAAUC,iBAAiB,sBAAwBZ,wBAAyB3C,mBAE5EA,gBAAgBwD,eAAe,kBAE/BX,MAAMY,SAASN,OAAO,GAAGtB,MAK7BG,SAASe,iBAAiB,uBAAuBC,SAAQC,6BACnDA,QAAQC,QAAQ,WAEtBL,MAAMa,QAAQP,OAAO,GAAGtB,MACxBgB,MAAMc,UAAUR,OAAO,GAAGtB,MAC1BsB,OAAOH,SAASY,0BACFC,cAAcD,KAAKE,OAEjCjB,MAAMkB,UAAUC,KAAK,sBAAuB/D,KAAKL,cAC3CK,KAAKgE,sBACLhE,KAAKiE,qBACXlC,SAASe,iBAAiB,uBAAuBC,SAAQC,SACrDA,OAAOkB,iBAAiB,SAASC,4BAC3BA,MAAMC,QAAQC,QAAQ,uBAAuBpB,QAAQ,oCAO3DlB,SAASuC,cADW,wCAEd,0BAAc,WAAW,yBAAetE,KAAKL,QAAQ4E,YAFvC,gCAGhB,CAAC,eAAgB,YAAa,MAAO,SAAU,mCAMnDxC,SAASuC,cADY,0CAEf,8BAFe,kCAEmB,CAAC,eAAgB,YAAa,MAAO,SAAU"}