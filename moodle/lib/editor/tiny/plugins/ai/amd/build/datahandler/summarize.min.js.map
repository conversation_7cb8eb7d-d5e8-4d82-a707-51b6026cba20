{"version": 3, "file": "summarize.min.js", "sources": ["../../src/datahandler/summarize.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Moodle is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\nimport * as BasedataHandler from 'tiny_ai/datahandler/basedata';\nimport BaseHandler from 'tiny_ai/datahandler/base';\nimport {getString} from 'core/str';\n\n/**\n * Tiny AI data manager.\n *\n * @module      tiny_ai/datahandler/summarize\n * @copyright   2024, ISB Bayern\n * <AUTHOR>\n * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nexport default class extends BaseHandler {\n\n    currentTool = null;\n\n    getLanguageTypeOptions() {\n        return {\n            nospeciallanguage: BasedataHandler.getTinyAiString('keeplanguagetype'),\n            simplelanguage: BasedataHandler.getTinyAiString('simplelanguage'),\n            technicallanguage: BasedataHandler.getTinyAiString('technicallanguage'),\n        };\n    }\n\n    getMaxWordCountOptions() {\n        return {\n            '0': BasedataHandler.getTinyAiString('nomaxwordcount'),\n            '10': '10',\n            '20': '20',\n            '50': '50',\n            '100': '100',\n            '200': '200',\n            '300': '300'\n        };\n    }\n\n    languageType = null;\n    maxWordCount = 0;\n\n    setMaxWordCount(maxWordCount) {\n        this.maxWordCount = maxWordCount;\n    }\n\n    setLanguageType(languageType) {\n        this.languageType = languageType;\n    }\n\n    async getPrompt(selectionText) {\n        let prompt = '';\n        if (this.currentTool === 'summarize') {\n            prompt += BasedataHandler.getTinyAiString('summarize_baseprompt');\n        } else if (this.currentTool === 'describe') {\n            prompt += BasedataHandler.getTinyAiString('describe_baseprompt');\n        }\n        if (parseInt(this.maxWordCount) === 0 && this.languageType === 'nospeciallanguage') {\n            return prompt + ': ' + selectionText;\n        } else {\n            prompt += '. ';\n            if (parseInt(this.maxWordCount) !== 0) {\n                prompt += ' ';\n                prompt += await getString('maxwordcount_prompt', 'tiny_ai', this.maxWordCount);\n                prompt += '.';\n            }\n            if (this.languageType !== 'nospeciallanguage') {\n                prompt += ' ';\n                prompt += await getString('languagetype_prompt', 'tiny_ai', this.getLanguageTypeOptions()[this.languageType]);\n                prompt += '.';\n            }\n            prompt += '\\n';\n            prompt += BasedataHandler.getTinyAiString('texttouse') + ': ' + selectionText;\n            return prompt;\n        }\n    }\n\n    setTool(currentTool) {\n        this.currentTool = currentTool;\n    }\n\n    /**\n     * Return the template context.\n     *\n     * @param {string} tool the tool to generate the context for, can be 'summarize' or 'describe'\n     */\n    getTemplateContext(tool) {\n        const\n            context = {\n                modalHeadline: BasedataHandler.getTinyAiString(tool + '_headline'),\n                showIcon: true,\n                tool: tool,\n            };\n        Object.assign(context, BasedataHandler.getShowPromptButtonContext());\n        Object.assign(context, BasedataHandler.getBackAndGenerateButtonContext());\n\n        const maxWordCountDropdownContext = {};\n        maxWordCountDropdownContext.preference = 'maxWordCount';\n        maxWordCountDropdownContext.dropdownDefault = Object.values(this.getMaxWordCountOptions())[0];\n        maxWordCountDropdownContext.dropdownDefaultValue = Object.keys(this.getMaxWordCountOptions())[0];\n        maxWordCountDropdownContext.dropdownDescription = BasedataHandler.getTinyAiString('maxwordcount');\n        const maxWordCountDropdownOptions = [];\n\n        for (const [key, value] of Object.entries(this.getMaxWordCountOptions())) {\n            maxWordCountDropdownOptions.push({\n                optionValue: key,\n                optionLabel: value,\n            });\n        }\n\n        delete maxWordCountDropdownOptions[Object.keys(this.getLanguageTypeOptions())[0]];\n        maxWordCountDropdownContext.dropdownOptions = maxWordCountDropdownOptions;\n\n        const languageTypeDropdownContext = {};\n        languageTypeDropdownContext.preference = 'languageType';\n        languageTypeDropdownContext.dropdownDefault = Object.values(this.getLanguageTypeOptions())[0];\n        languageTypeDropdownContext.dropdownDefaultValue = Object.keys(this.getLanguageTypeOptions())[0];\n        languageTypeDropdownContext.dropdownDescription = BasedataHandler.getTinyAiString('languagetype');\n        const languageTypeDropdownOptions = [];\n        for (const [key, value] of Object.entries(this.getLanguageTypeOptions())) {\n            languageTypeDropdownOptions.push({\n                optionValue: key,\n                optionLabel: value,\n            });\n        }\n        delete languageTypeDropdownOptions[Object.keys(this.getLanguageTypeOptions)[0]];\n        languageTypeDropdownContext.dropdownOptions = languageTypeDropdownOptions;\n\n\n        Object.assign(context, {\n            modalDropdowns: [\n                maxWordCountDropdownContext,\n                languageTypeDropdownContext,\n            ]\n        });\n\n        return context;\n    }\n}\n"], "names": ["BaseHandler", "getLanguageTypeOptions", "nospeciallanguage", "BasedataHandler", "getTinyAiString", "simplelanguage", "technicallanguage", "getMaxWordCountOptions", "setMaxWordCount", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "setLanguageType", "languageType", "selectionText", "prompt", "this", "currentTool", "parseInt", "setTool", "getTemplateContext", "tool", "context", "modalHeadline", "showIcon", "Object", "assign", "getShowPromptButtonContext", "getBackAndGenerateButtonContext", "maxWordCountDropdownContext", "dropdownDefault", "values", "dropdownDefaultValue", "keys", "dropdownDescription", "maxWordCountDropdownOptions", "key", "value", "entries", "push", "optionValue", "optionLabel", "dropdownOptions", "languageTypeDropdownContext", "languageTypeDropdownOptions", "modalDropdowns"], "mappings": ";;;;;;;;80BA4B6BA,mFAEX,0CAsBC,0CACA,GArBfC,+BACW,CACHC,kBAAmBC,gBAAgBC,gBAAgB,oBACnDC,eAAgBF,gBAAgBC,gBAAgB,kBAChDE,kBAAmBH,gBAAgBC,gBAAgB,sBAI3DG,+BACW,GACEJ,gBAAgBC,gBAAgB,qBAC/B,QACA,QACA,SACC,UACA,UACA,OAOfI,gBAAgBC,mBACPA,aAAeA,aAGxBC,gBAAgBC,mBACPA,aAAeA,6BAGRC,mBACRC,OAAS,SACY,cAArBC,KAAKC,YACLF,QAAUV,gBAAgBC,gBAAgB,wBACd,aAArBU,KAAKC,cACZF,QAAUV,gBAAgBC,gBAAgB,wBAEV,IAAhCY,SAASF,KAAKL,eAA6C,sBAAtBK,KAAKH,aACnCE,OAAS,KAAOD,eAEvBC,QAAU,KAC0B,IAAhCG,SAASF,KAAKL,gBACdI,QAAU,IACVA,cAAgB,kBAAU,sBAAuB,UAAWC,KAAKL,cACjEI,QAAU,KAEY,sBAAtBC,KAAKH,eACLE,QAAU,IACVA,cAAgB,kBAAU,sBAAuB,UAAWC,KAAKb,yBAAyBa,KAAKH,eAC/FE,QAAU,KAEdA,QAAU,KACVA,QAAUV,gBAAgBC,gBAAgB,aAAe,KAAOQ,cACzDC,QAIfI,QAAQF,kBACCA,YAAcA,YAQvBG,mBAAmBC,YAEXC,QAAU,CACNC,cAAelB,gBAAgBC,gBAAgBe,KAAO,aACtDG,UAAU,EACVH,KAAMA,MAEdI,OAAOC,OAAOJ,QAASjB,gBAAgBsB,8BACvCF,OAAOC,OAAOJ,QAASjB,gBAAgBuB,yCAEjCC,4BAA8B,CACpCA,WAAyC,gBACzCA,4BAA4BC,gBAAkBL,OAAOM,OAAOf,KAAKP,0BAA0B,GAC3FoB,4BAA4BG,qBAAuBP,OAAOQ,KAAKjB,KAAKP,0BAA0B,GAC9FoB,4BAA4BK,oBAAsB7B,gBAAgBC,gBAAgB,sBAC5E6B,4BAA8B,OAE/B,MAAOC,IAAKC,SAAUZ,OAAOa,QAAQtB,KAAKP,0BAC3C0B,4BAA4BI,KAAK,CAC7BC,YAAaJ,IACbK,YAAaJ,eAIdF,4BAA4BV,OAAOQ,KAAKjB,KAAKb,0BAA0B,IAC9E0B,4BAA4Ba,gBAAkBP,kCAExCQ,4BAA8B,CACpCA,WAAyC,gBACzCA,4BAA4Bb,gBAAkBL,OAAOM,OAAOf,KAAKb,0BAA0B,GAC3FwC,4BAA4BX,qBAAuBP,OAAOQ,KAAKjB,KAAKb,0BAA0B,GAC9FwC,4BAA4BT,oBAAsB7B,gBAAgBC,gBAAgB,sBAC5EsC,4BAA8B,OAC/B,MAAOR,IAAKC,SAAUZ,OAAOa,QAAQtB,KAAKb,0BAC3CyC,4BAA4BL,KAAK,CAC7BC,YAAaJ,IACbK,YAAaJ,sBAGdO,4BAA4BnB,OAAOQ,KAAKjB,KAAKb,wBAAwB,IAC5EwC,4BAA4BD,gBAAkBE,4BAG9CnB,OAAOC,OAAOJ,QAAS,CACnBuB,eAAgB,CACZhB,4BACAc,+BAIDrB"}