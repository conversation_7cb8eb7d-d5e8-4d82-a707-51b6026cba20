define("tiny_ai/controllers/base",["exports","core/notification","tiny_ai/datahandler/basedata","tiny_ai/utils","tiny_ai/constants"],(function(_exports,_notification,BasedataHandler,_utils,_constants){function _getRequireWildcardCache(nodeInterop){if("function"!=typeof WeakMap)return null;var cacheBabelInterop=new WeakMap,cacheNodeInterop=new WeakMap;return(_getRequireWildcardCache=function(nodeInterop){return nodeInterop?cacheNodeInterop:cacheBabelInterop})(nodeInterop)}function _defineProperty(obj,key,value){return key in obj?Object.defineProperty(obj,key,{value:value,enumerable:!0,configurable:!0,writable:!0}):obj[key]=value,obj}
/**
   * Base controller class providing some basic functionalities.
   *
   * All tiny_ai controllers should inherit from this class.
   *
   * @module      tiny_ai/controllers/base
   * @copyright   2024, ISB Bayern
   * <AUTHOR>
   * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
   */Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0,BasedataHandler=function(obj,nodeInterop){if(!nodeInterop&&obj&&obj.__esModule)return obj;if(null===obj||"object"!=typeof obj&&"function"!=typeof obj)return{default:obj};var cache=_getRequireWildcardCache(nodeInterop);if(cache&&cache.has(obj))return cache.get(obj);var newObj={},hasPropertyDescriptor=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var key in obj)if("default"!==key&&Object.prototype.hasOwnProperty.call(obj,key)){var desc=hasPropertyDescriptor?Object.getOwnPropertyDescriptor(obj,key):null;desc&&(desc.get||desc.set)?Object.defineProperty(newObj,key,desc):newObj[key]=obj[key]}newObj.default=obj,cache&&cache.set(obj,newObj);return newObj}(BasedataHandler);return _exports.default=class{constructor(baseSelector){_defineProperty(this,"uniqid",null),_defineProperty(this,"baseElement",null),_defineProperty(this,"renderer",null),_defineProperty(this,"editorUtils",null),_defineProperty(this,"footer",null),this.baseElement=document.querySelector(baseSelector),this.uniqid=(0,_utils.getCurrentModalUniqId)(this.baseElement),this.renderer=(0,_utils.getRenderer)(this.uniqid),this.editorUtils=(0,_utils.getEditorUtils)(this.uniqid),this.datamanager=(0,_utils.getDatamanager)(this.uniqid),null!==this.baseElement&&(this.footer=this.baseElement.parentElement.parentElement.querySelector('[data-region="footer"]'))}async generateAiAnswer(){if(null===this.datamanager.getCurrentPrompt()||0===this.datamanager.getCurrentPrompt().length)return await(0,_utils.errorAlert)(BasedataHandler.getTinyAiString("error_nopromptgiven")),null;if(["describeimg","imagetotext"].includes(this.datamanager.getCurrentTool())&&null===this.datamanager.getCurrentFile())return await(0,_utils.errorAlert)(BasedataHandler.getTinyAiString("error_nofile")),null;await this.renderer.renderLoading();let result=null;try{result=await(0,_utils.getAiAnswer)(this.datamanager.getCurrentPrompt(),_constants.constants.toolPurposeMapping[this.datamanager.getCurrentTool()],this.datamanager.getCurrentOptions())}catch(exception){await(0,_notification.exception)(exception)}return null===result?(await this.callRendererFunction(),null):(this.datamanager.setCurrentAiResult(result),!0)}async callRendererFunction(){if("freeprompt"===this.datamanager.getCurrentTool())return void await this.renderer.renderStart();const toolNameWithUppercaseLetter=this.datamanager.getCurrentTool().charAt(0).toUpperCase()+this.datamanager.getCurrentTool().slice(1);this.renderer["render"+toolNameWithUppercaseLetter]()}},_exports.default}));

//# sourceMappingURL=base.min.js.map