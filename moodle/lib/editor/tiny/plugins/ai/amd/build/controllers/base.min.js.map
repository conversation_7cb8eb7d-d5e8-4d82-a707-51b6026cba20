{"version": 3, "file": "base.min.js", "sources": ["../../src/controllers/base.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// <PERSON><PERSON>le is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\nimport {exception as displayException} from 'core/notification';\nimport * as BasedataHandler from 'tiny_ai/datahandler/basedata';\nimport {getRenderer, getDatamanager, getAiAnswer, errorAlert, getCurrentModalUniqId, getEditorUtils} from 'tiny_ai/utils';\nimport {constants} from 'tiny_ai/constants';\n\n/**\n * Base controller class providing some basic functionalities.\n *\n * All tiny_ai controllers should inherit from this class.\n *\n * @module      tiny_ai/controllers/base\n * @copyright   2024, ISB Bayern\n * <AUTHOR>\n * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\nexport default class {\n\n    uniqid = null;\n    baseElement = null;\n    renderer = null;\n    editorUtils = null;\n    footer = null;\n\n    constructor(baseSelector) {\n        this.baseElement = document.querySelector(baseSelector);\n        this.uniqid = getCurrentModalUniqId(this.baseElement);\n        this.renderer = getRenderer(this.uniqid);\n        this.editorUtils = getEditorUtils(this.uniqid);\n        this.datamanager = getDatamanager(this.uniqid);\n\n        if (this.baseElement === null) {\n            // Sometimes (for example we display an error message before we even finish rendering the modal) we do not have\n            // a base element. In this case there is nothing to do, so we avoid console errors by early exiting.\n            return;\n        }\n        this.footer = this.baseElement.parentElement.parentElement.querySelector('[data-region=\"footer\"]');\n    }\n\n    async generateAiAnswer() {\n        if (this.datamanager.getCurrentPrompt() === null || this.datamanager.getCurrentPrompt().length === 0) {\n            await errorAlert(BasedataHandler.getTinyAiString('error_nopromptgiven'));\n            return null;\n        }\n        if (['describeimg', 'imagetotext'].includes(this.datamanager.getCurrentTool())\n                && this.datamanager.getCurrentFile() === null) {\n            await errorAlert(BasedataHandler.getTinyAiString('error_nofile'));\n            return null;\n        }\n        await this.renderer.renderLoading();\n        let result = null;\n        try {\n            result = await getAiAnswer(this.datamanager.getCurrentPrompt(),\n                constants.toolPurposeMapping[this.datamanager.getCurrentTool()], this.datamanager.getCurrentOptions());\n        } catch (exception) {\n            await displayException(exception);\n        }\n\n        if (result === null) {\n            await this.callRendererFunction();\n            return null;\n        }\n        this.datamanager.setCurrentAiResult(result);\n        return true;\n    }\n\n    async callRendererFunction() {\n        if (this.datamanager.getCurrentTool() === 'freeprompt') {\n            await this.renderer.renderStart();\n            return;\n        }\n        const toolNameWithUppercaseLetter =\n            this.datamanager.getCurrentTool().charAt(0).toUpperCase() + this.datamanager.getCurrentTool().slice(1);\n        this.renderer['render' + toolNameWithUppercaseLetter]();\n    }\n}\n"], "names": ["constructor", "baseSelector", "baseElement", "document", "querySelector", "uniqid", "this", "renderer", "editor<PERSON><PERSON><PERSON>", "datamanager", "footer", "parentElement", "getCurrentPrompt", "length", "BasedataHandler", "getTinyAiString", "includes", "getCurrentTool", "getCurrentFile", "renderLoading", "result", "constants", "toolPurposeMapping", "getCurrentOptions", "exception", "callRendererFunction", "setCurrentAiResult", "renderStart", "toolNameWithUppercaseLetter", "char<PERSON>t", "toUpperCase", "slice"], "mappings": ";;;;;;;;;;iyBAsCIA,YAAYC,4CANH,yCACK,sCACH,yCACG,oCACL,WAGAC,YAAcC,SAASC,cAAcH,mBACrCI,QAAS,gCAAsBC,KAAKJ,kBACpCK,UAAW,sBAAYD,KAAKD,aAC5BG,aAAc,yBAAeF,KAAKD,aAClCI,aAAc,yBAAeH,KAAKD,QAEd,OAArBC,KAAKJ,mBAKJQ,OAASJ,KAAKJ,YAAYS,cAAcA,cAAcP,cAAc,uDAI7B,OAAxCE,KAAKG,YAAYG,oBAA8E,IAA/CN,KAAKG,YAAYG,mBAAmBC,oBAC9E,qBAAWC,gBAAgBC,gBAAgB,wBAC1C,QAEP,CAAC,cAAe,eAAeC,SAASV,KAAKG,YAAYQ,mBACZ,OAAtCX,KAAKG,YAAYS,8BAClB,qBAAWJ,gBAAgBC,gBAAgB,iBAC1C,WAELT,KAAKC,SAASY,oBAChBC,OAAS,SAETA,aAAe,sBAAYd,KAAKG,YAAYG,mBACxCS,qBAAUC,mBAAmBhB,KAAKG,YAAYQ,kBAAmBX,KAAKG,YAAYc,qBACxF,MAAOC,iBACC,2BAAiBA,kBAGZ,OAAXJ,cACMd,KAAKmB,uBACJ,YAENhB,YAAYiB,mBAAmBN,SAC7B,mCAImC,eAAtCd,KAAKG,YAAYQ,mCACXX,KAAKC,SAASoB,oBAGlBC,4BACFtB,KAAKG,YAAYQ,iBAAiBY,OAAO,GAAGC,cAAgBxB,KAAKG,YAAYQ,iBAAiBc,MAAM,QACnGxB,SAAS,SAAWqB"}