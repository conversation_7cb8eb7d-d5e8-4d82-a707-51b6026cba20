define("tiny_ai/modal",["exports","core/modal"],(function(_exports,_modal){var obj;function _defineProperty(obj,key,value){return key in obj?Object.defineProperty(obj,key,{value:value,enumerable:!0,configurable:!0,writable:!0}):obj[key]=value,obj}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0,_modal=(obj=_modal)&&obj.__esModule?obj:{default:obj};class AiModal extends _modal.default{registerEventListeners(){super.registerEventListeners()}configure(modalConfig){modalConfig.large=!0,modalConfig.removeOnClose=!0,super.configure(modalConfig)}}return _exports.default=AiModal,_defineProperty(AiModal,"TYPE","ai-modal"),_defineProperty(AiModal,"TEMPLATE","tiny_ai/components/moodle-modal"),_exports.default}));

//# sourceMappingURL=modal.min.js.map