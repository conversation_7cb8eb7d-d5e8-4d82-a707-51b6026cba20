define("tiny_ai/options",["exports","editor_tiny/options","./common"],(function(_exports,_options,_common){Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.register=_exports.getUserId=void 0;
/**
   * Options helper for the Moodle tiny_ai plugin.
   *
   * @module      tiny_ai/options
   * @copyright   2024, ISB Bayern
   * <AUTHOR> <PERSON>
   * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
   */
const userId=(0,_options.getPluginOptionName)(_common.pluginName,"userId");_exports.register=editor=>{(0,editor.options.register)(userId,{processor:"number"})};_exports.getUserId=editor=>editor.options.get(userId)}));

//# sourceMappingURL=options.min.js.map