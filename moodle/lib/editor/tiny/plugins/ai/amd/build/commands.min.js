define("tiny_ai/commands",["exports","editor_tiny/utils","tiny_ai/common","core/prefetch","core/str","editor_tiny/options","tiny_ai/constants","tiny_ai/options","tiny_ai/editor_utils","tiny_ai/utils"],(function(_exports,_utils,_common,_prefetch,_str,_options,_constants,_options2,_editor_utils,Utils){var obj;
/**
   * Commands helper for the Moodle tiny_ai plugin.
   *
   * @module      tiny_ai/commands
   * @copyright   2024, ISB Bayern
   * <AUTHOR> <PERSON>
   * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
   */function _getRequireWildcardCache(nodeInterop){if("function"!=typeof WeakMap)return null;var cacheBabelInterop=new WeakMap,cacheNodeInterop=new WeakMap;return(_getRequireWildcardCache=function(nodeInterop){return nodeInterop?cacheNodeInterop:cacheBabelInterop})(nodeInterop)}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.injectSelectedElements=_exports.getSetup=void 0,_editor_utils=(obj=_editor_utils)&&obj.__esModule?obj:{default:obj},Utils=function(obj,nodeInterop){if(!nodeInterop&&obj&&obj.__esModule)return obj;if(null===obj||"object"!=typeof obj&&"function"!=typeof obj)return{default:obj};var cache=_getRequireWildcardCache(nodeInterop);if(cache&&cache.has(obj))return cache.get(obj);var newObj={},hasPropertyDescriptor=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var key in obj)if("default"!==key&&Object.prototype.hasOwnProperty.call(obj,key)){var desc=hasPropertyDescriptor?Object.getOwnPropertyDescriptor(obj,key):null;desc&&(desc.get||desc.set)?Object.defineProperty(newObj,key,desc):newObj[key]=obj[key]}newObj.default=obj,cache&&cache.set(obj,newObj);return newObj}(Utils);_exports.getSetup=async()=>{(0,_prefetch.prefetchStrings)("tiny_ai",["toolbarbuttontitle","selectionbarbuttontitle"]);const[buttonImage,toolbarButtonTitle,selectionbarButtonTitle]=await Promise.all([(0,_utils.getButtonImage)("icon",_common.component),(0,_str.getString)("toolbarbuttontitle","tiny_ai"),(0,_str.getString)("selectionbarbuttontitle","tiny_ai")]),uniqid=Math.random().toString(16).slice(2);return await Utils.init(uniqid,_constants.constants.modalModes.editor),editor=>{editor.ui.registry.addIcon(_common.icon,buttonImage.html);const contextId=(0,_options.getContextId)(editor),editorUtils=new _editor_utils.default(uniqid,"tiny_ai",contextId,(0,_options2.getUserId)(editor),editor);Utils.setEditorUtils(uniqid,editorUtils),editor.ui.registry.addButton(_common.toolbarButtonName,{icon:_common.icon,tooltip:toolbarButtonTitle,onAction:async()=>{await injectSelectedElements(editor,Utils.getDatamanager(uniqid)),Utils.getEditorUtils(uniqid).displayDialogue()}}),editor.ui.registry.addMenuItem(_common.toolbarButtonName,{icon:_common.icon,text:toolbarButtonTitle,onAction:async()=>{await injectSelectedElements(editor,Utils.getDatamanager(uniqid)),Utils.getEditorUtils(uniqid).displayDialogue()}}),editor.ui.registry.addButton(_common.selectionbarButtonName,{icon:_common.icon,tooltip:selectionbarButtonTitle,onAction:async()=>{await injectSelectedElements(editor,Utils.getDatamanager(uniqid)),Utils.getEditorUtils(uniqid).displayDialogue()}})}};const injectSelectedElements=async(editor,datamanager)=>{const selectedEditorContentHtml=editor.selection.getContent({format:"html"}),images=(new DOMParser).parseFromString(selectedEditorContentHtml,"text/html").querySelectorAll("img");if(images.length>0&&images[0].src){const image=images[0],fetchResult=await fetch(image.src),data=await fetchResult.blob();datamanager.setSelectionImg(data)}datamanager.setSelection(editor.selection.getContent())};_exports.injectSelectedElements=injectSelectedElements}));

//# sourceMappingURL=commands.min.js.map