{"version": 3, "file": "optimize.min.js", "sources": ["../../src/controllers/optimize.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Moodle is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Controller for the main selection.\n *\n * @module      tiny_ai/controllers/optimize\n * @copyright   2024, ISB Bayern\n * <AUTHOR>\n * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport BaseController from 'tiny_ai/controllers/base';\n\nexport default class extends BaseController {\n\n    async init() {\n        const backButton = this.footer.querySelector('[data-action=\"back\"]');\n        const generateButton = this.footer.querySelector('[data-action=\"generate\"]');\n\n        if (backButton) {\n            backButton.addEventListener('click', async() => {\n                await this.renderer.renderSuggestion();\n            });\n        }\n\n        if (generateButton) {\n            generateButton.addEventListener('click', async() => {\n                const result = await this.generateAiAnswer();\n                if (result === null) {\n                    return;\n                }\n                await this.renderer.renderSuggestion();\n            });\n        }\n    }\n}\n"], "names": ["BaseController", "backButton", "this", "footer", "querySelector", "generateButton", "addEventListener", "async", "renderer", "renderSuggestion", "generateAiAnswer"], "mappings": ";;;;;;;;gKA0B6BA,iCAGfC,WAAaC,KAAKC,OAAOC,cAAc,wBACvCC,eAAiBH,KAAKC,OAAOC,cAAc,4BAE7CH,YACAA,WAAWK,iBAAiB,SAASC,gBAC3BL,KAAKM,SAASC,sBAIxBJ,gBACAA,eAAeC,iBAAiB,SAASC,UAEtB,aADML,KAAKQ,0BAIpBR,KAAKM,SAASC"}