define("tiny_ai/controllers/dropdown",["exports"],(function(_exports){Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.init=void 0;_exports.init=dropDownSelector=>{const dropdown=document.querySelector(dropDownSelector);dropdown.querySelectorAll('[data-dropdown="option"]').forEach((item=>{item.addEventListener("click",(()=>{const dropdownSelect=dropdown.querySelector('[data-dropdown="select"]');dropdown.querySelector('[data-dropdown="selecttext"]').innerText=item.innerText,dropdownSelect.dataset.value=item.dataset.value;const event=new CustomEvent("dropdownSelectionUpdated",{detail:{dropdownPreference:dropdown.dataset.preference,newValue:dropdownSelect.dataset.value}});dropdown.dispatchEvent(event)}))}))}}));

//# sourceMappingURL=dropdown.min.js.map