define("tiny_ai/controllers/preferences",["exports","tiny_ai/constants","tiny_ai/selectors","tiny_ai/controllers/base","tiny_ai/utils"],(function(_exports,_constants,_selectors,_base,_utils){function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}
/**
   * Controller for the main selection.
   *
   * @module      tiny_ai/controllers/preferences
   * @copyright   2024, ISB Bayern
   * <AUTHOR>
   * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
   */Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0,_selectors=_interopRequireDefault(_selectors),_base=_interopRequireDefault(_base);class _default extends _base.default{async init(){const modalFooter=document.querySelector(_selectors.default.modalFooter),backButton=modalFooter.querySelector('[data-action="back"]'),generateButton=modalFooter.querySelector('[data-action="generate"]'),[summarizeHandler,translateHandler,ttsHandler,imggenHandler,ittHandler]=[(0,_utils.getSummarizeHandler)(this.uniqid),(0,_utils.getTranslateHandler)(this.uniqid),(0,_utils.getTtsHandler)(this.uniqid),(0,_utils.getImggenHandler)(this.uniqid),(0,_utils.getIttHandler)(this.uniqid)];switch(this.datamanager.getCurrentTool()){case"summarize":case"describe":{summarizeHandler.setTool(this.datamanager.getCurrentTool());const maxWordCountElement=this.baseElement.querySelector('[data-preference="maxWordCount"]'),languageTypeElement=this.baseElement.querySelector('[data-preference="languageType"]');summarizeHandler.setMaxWordCount(maxWordCountElement.querySelector('[data-dropdown="select"]').dataset.value),summarizeHandler.setLanguageType(languageTypeElement.querySelector('[data-dropdown="select"]').dataset.value),this.datamanager.getEventEmitterElement().addEventListener("textUpdated",(async()=>{if(!["summarize","describe"].includes(this.datamanager.currentTool))return;const currentPromptSummarize=await summarizeHandler.getPrompt(this.datamanager.getCurrentText());this.datamanager.setCurrentPrompt(currentPromptSummarize)})),this.datamanager.setCurrentText(this.datamanager.getSelectionText()),maxWordCountElement.addEventListener("dropdownSelectionUpdated",(async event=>{summarizeHandler.setMaxWordCount(event.detail.newValue);const currentPrompt=await summarizeHandler.getPrompt(this.datamanager.getCurrentText());this.datamanager.setCurrentPrompt(currentPrompt)})),languageTypeElement.addEventListener("dropdownSelectionUpdated",(async event=>{summarizeHandler.setLanguageType(event.detail.newValue);const currentPrompt=await summarizeHandler.getPrompt(this.datamanager.getCurrentText());this.datamanager.setCurrentPrompt(currentPrompt)}));break}case"translate":{const targetLanguageElement=this.baseElement.querySelector('[data-preference="targetLanguage"]');translateHandler.setTargetLanguage(targetLanguageElement.querySelector('[data-dropdown="select"]').dataset.value),this.datamanager.getEventEmitterElement().addEventListener("textUpdated",(async()=>{if("translate"!==this.datamanager.currentTool)return;const currentPromptTranslate=await translateHandler.getPrompt(this.datamanager.getCurrentText());this.datamanager.setCurrentPrompt(currentPromptTranslate)})),this.datamanager.setCurrentText(this.datamanager.getSelectionText()),targetLanguageElement.addEventListener("dropdownSelectionUpdated",(async event=>{translateHandler.setTargetLanguage(event.detail.newValue);const currentPromptTranslate=await translateHandler.getPrompt(this.datamanager.getCurrentText());this.datamanager.setCurrentPrompt(currentPromptTranslate)}));break}case"tts":{const ttsTargetLanguageElement=this.baseElement.querySelector('[data-preference="targetLanguage"]'),voiceElement=this.baseElement.querySelector('[data-preference="voice"]'),genderElement=this.baseElement.querySelector('[data-preference="gender"]');ttsTargetLanguageElement&&(ttsHandler.setTargetLanguage(ttsTargetLanguageElement.querySelector('[data-dropdown="select"]').dataset.value),ttsTargetLanguageElement.addEventListener("dropdownSelectionUpdated",(event=>{ttsHandler.setTargetLanguage(event.detail.newValue),this.datamanager.setCurrentOptions(ttsHandler.getOptions())}))),voiceElement&&(ttsHandler.setVoice(voiceElement.querySelector('[data-dropdown="select"]').dataset.value),voiceElement.addEventListener("dropdownSelectionUpdated",(event=>{ttsHandler.setVoice(event.detail.newValue),this.datamanager.setCurrentOptions(ttsHandler.getOptions())}))),genderElement&&(ttsHandler.setGender(genderElement.querySelector('[data-dropdown="select"]').dataset.value),genderElement.addEventListener("dropdownSelectionUpdated",(event=>{ttsHandler.setGender(event.detail.newValue),this.datamanager.setCurrentOptions(ttsHandler.getOptions())}))),this.datamanager.getEventEmitterElement().addEventListener("textUpdated",(async()=>{if("tts"!==this.datamanager.currentTool)return;const currentPromptTts=await ttsHandler.getPrompt(this.datamanager.getCurrentText());this.datamanager.setCurrentPrompt(currentPromptTts)})),this.datamanager.setCurrentText(ttsHandler.getPrompt(this.datamanager.getSelectionText())),this.datamanager.setCurrentOptions(ttsHandler.getOptions());break}case"imggen":{const sizesElement=this.baseElement.querySelector('[data-preference="sizes"]');sizesElement&&(imggenHandler.setSize(sizesElement.querySelector('[data-dropdown="select"]').dataset.value),sizesElement.addEventListener("dropdownSelectionUpdated",(event=>{imggenHandler.setSize(event.detail.newValue),this.datamanager.setCurrentOptions(imggenHandler.getOptions())}))),this.datamanager.setCurrentPrompt(this.datamanager.getSelectionText()),this.datamanager.setCurrentOptions(imggenHandler.getOptions());break}case"describeimg":case"imagetotext":this.baseElement.querySelector('[data-preference="fileupload"]')&&this.datamanager.getEventEmitterElement().addEventListener("fileUploaded",(async event=>{this.datamanager.setCurrentFile(event.detail.newFile),this.datamanager.setCurrentOptions(ittHandler.getOptions())})),this.datamanager.setCurrentPrompt(ittHandler.getPrompt(this.datamanager.getCurrentTool())),this.datamanager.setCurrentFile(null);break}backButton&&backButton.addEventListener("click",(async()=>{await this.renderer.renderStart(_constants.constants.modalModes.selection)})),generateButton&&generateButton.addEventListener("click",(async()=>{null!==await this.generateAiAnswer()&&await this.renderer.renderSuggestion()}))}}return _exports.default=_default,_exports.default}));

//# sourceMappingURL=preferences.min.js.map