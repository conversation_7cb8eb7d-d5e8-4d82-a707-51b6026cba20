define("tiny_ai/controllers/promptedit",["exports","core/str","tiny_ai/utils"],(function(_exports,_str,_utils){Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0;return _exports.default=
/**
   * Controller for handling the show/hide prompt button and the associated textarea.
   *
   * @module      tiny_ai/controllers/promtedit_controller
   * @copyright   2024, ISB Bayern
   * <AUTHOR>
   * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
   */
class{constructor(baseSelector){this.baseElement=document.querySelector(baseSelector)}async init(){const showPromptButton=this.baseElement.querySelector('[data-action="showprompt"]'),textTextarea=this.baseElement.querySelector('textarea[data-type="text"]'),promptTextarea=this.baseElement.querySelector('textarea[data-type="prompt"]'),datamanager=(0,_utils.getDatamanager)((0,_utils.getCurrentModalUniqId)(this.baseElement));if(promptTextarea.innerHTML=datamanager.getCurrentPrompt(),datamanager.getEventEmitterElement().addEventListener("promptUpdated",(event=>{promptTextarea.value=event.detail.newPrompt})),textTextarea&&(textTextarea.innerHTML=datamanager.getCurrentText(),textTextarea.addEventListener("input",(()=>{datamanager.setCurrentText(textTextarea.value)}))),promptTextarea.addEventListener("input",(()=>{datamanager.setCurrentPrompt(promptTextarea.value)})),showPromptButton){const[showPromptString,hidePromptString]=await(0,_str.getStrings)([{key:"prompteditmode",component:"tiny_ai"},{key:"prompteditmodedisable",component:"tiny_ai"}]);showPromptButton.addEventListener("click",(()=>{const currentText=showPromptButton.querySelector("[data-text]").innerText;showPromptButton.querySelector("[data-text]").innerText=currentText===showPromptString?hidePromptString:showPromptString;const buttonIcon=showPromptButton.querySelector("i");buttonIcon.classList.contains("fa-edit")?(buttonIcon.classList.remove("fa-edit"),buttonIcon.classList.add("fa-arrow-left")):(buttonIcon.classList.remove("fa-arrow-left"),buttonIcon.classList.add("fa-edit")),promptTextarea.classList.toggle("d-none"),textTextarea&&textTextarea.classList.toggle("d-none")}))}}},_exports.default}));

//# sourceMappingURL=promptedit.min.js.map