<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Language file for block_ai_chat
 *
 * @package    block_ai_chat
 * @copyright  2024 ISB Bayern
 * <AUTHOR>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

$string['addblockinstance'] = 'Add an AI Chat to this course';
$string['addblockinstance_help'] = 'Adds an AI Chat to this course. The AI chat will be removed including all conversations if the checkbox is unchecked.';
$string['addpersonatitle'] = 'Add new persona';
$string['ai_chat'] = 'AI Chat';
$string['ai_chat:addinstance'] = 'Add an AI Chat block';
$string['ai_chat:myaddinstance'] = 'Add an AI Chat block to my moodle';
$string['ai_chat:view'] = 'Access the AI Chat block';
$string['areyousuredelete'] = 'Are you sure you want to delete this persona?';
$string['awaitanswer'] = 'AI generating...';
$string['badgeprivate'] = 'Your chat is visible only to you.';
$string['badgepublic'] = 'Your chat is visible to others.';
$string['chatwindow'] = 'Open as chat window';
$string['chosenpersona'] = 'Chosen persona:';
$string['confirm_ai_usage'] = 'Click <a target="_self" href="{$a}">confirm</a> to confirm the terms of use.';
$string['copied'] = 'Copied';
$string['copypersonatitle'] = 'Copy current persona';
$string['definepersona'] = 'Define persona';
$string['delete'] = 'Delete current dialog';
$string['deletetemplate'] = 'Delete template';
$string['deletewarning'] = 'Are you sure you want to delete this conversation? The conversation will be permanently hidden from you, but will remain stored in the system.';
$string['dockright'] = 'Dock on the right';
$string['enableaichat'] = 'Add an AI Chat instance';
$string['error'] = 'A general error occurred.';
$string['errorhistorycontextmax'] = 'History must be a number and bigger than 0';
$string['errorname'] = 'Name can\'t be empty';
$string['errorprompt'] = 'Prompt can\'t be empty';
$string['erroruserinfo'] = 'Userinfo can\'t be empty';
$string['errorwithcode'] = 'An error occurred with code {$a}';
$string['history'] = 'History';
$string['historycontextmax'] = 'Amount of last messages sent along for context';
$string['input'] = 'Send a message to the AI';
$string['inputsystemprompt'] = 'Add AI system prompt';
$string['inputsystempromptplaceholder'] = 'Act as if you are...';
$string['maxhistory'] = 'Conversation history restriction';
$string['maxhistoryreached'] = 'Please be aware that dialog history is restricted to {$a} messages. Older messages wont be included';
$string['name'] = 'Name';
$string['newdialog'] = 'New AI Chat';
$string['newpersona'] = 'Create new persona';
$string['nohistory'] = 'Chat history not found';
$string['nopersona'] = 'No persona';
$string['notice'] = 'Notice';
$string['noticenewconversation'] = 'Currently you can\'t open a new conversation';
$string['noticenewquestion'] = 'Currently you can\'t submit a new question';
$string['openfull'] = 'Use full width';
$string['personalink'] = 'Infolink to personas';
$string['personaltemplate'] = 'Personal';
$string['personasystemtemplateedit'] = '<h3>You are editing as admin!</h3>
That means:<ul><li>You will only be able to edit system templates</li><li>Editing a system templates will <span class="text-danger">change it for every chatbot in the system</span></li><li>Deleting a system persona will <span class="text-danger">delete it from every chatbot on the system</span></li></ul>';
$string['pluginname'] = 'AI Chat';
$string['privacy:metadata'] = 'Conversations are saved by local_ai_manager.';
$string['private'] = 'Private';
$string['prompt'] = 'Prompt';
$string['public'] = 'Public';
$string['replacehelp'] = 'Replace help button with block_ai_chat button';
$string['showhistory'] = 'Show history';
$string['showonpagetypes'] = 'Pagetypes on which the chat bot floating button should be shown';
$string['showonpagetypesdesc'] = 'Insert a list of page types (one string per line) on which the floating button should be shown. Insert "*" to always show the block.';
$string['submit'] = 'Submit';
$string['systemorpersonal_question'] = 'Do you want to save this persona as system wide template or personal persona?';
$string['systemorpersonal_title'] = 'System or personal persona';
$string['systemtemplate'] = 'System';
$string['systemtemplates'] = 'System templates';
$string['template'] = 'Template';
$string['userinfo'] = 'Info shown to users';
$string['usertemplates'] = 'My templates';
$string['yesterday'] = 'Yesterday';
