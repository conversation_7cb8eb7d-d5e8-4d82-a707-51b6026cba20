define("block_ai_chat/dialog",["exports","core/modal","block_ai_chat/webservices","core/templates","core/notification","core/modal_save_cancel","core/modal_events","core_form/modalform","block_ai_chat/helper","block_ai_chat/ai_manager","core/str","local_ai_manager/infobox","local_ai_manager/userquota","local_ai_manager/warningbox","local_ai_manager/config","core/localstorage","./helper","tiny_ai/utils","tiny_ai/editor_utils","tiny_ai/constants"],(function(_exports,_modal,externalServices,_templates,_notification,_modal_save_cancel,_modal_events,_modalform,helper,manager,_str,_infobox,_userquota,_warningbox,_config,_localstorage,_helper2,TinyAiUtils,_editor_utils,_constants){function _getRequireWildcardCache(nodeInterop){if("function"!=typeof WeakMap)return null;var cacheBabelInterop=new WeakMap,cacheNodeInterop=new WeakMap;return(_getRequireWildcardCache=function(nodeInterop){return nodeInterop?cacheNodeInterop:cacheBabelInterop})(nodeInterop)}function _interopRequireWildcard(obj,nodeInterop){if(!nodeInterop&&obj&&obj.__esModule)return obj;if(null===obj||"object"!=typeof obj&&"function"!=typeof obj)return{default:obj};var cache=_getRequireWildcardCache(nodeInterop);if(cache&&cache.has(obj))return cache.get(obj);var newObj={},hasPropertyDescriptor=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var key in obj)if("default"!==key&&Object.prototype.hasOwnProperty.call(obj,key)){var desc=hasPropertyDescriptor?Object.getOwnPropertyDescriptor(obj,key):null;desc&&(desc.get||desc.set)?Object.defineProperty(newObj,key,desc):newObj[key]=obj[key]}return newObj.default=obj,cache&&cache.set(obj,newObj),newObj}function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}function _defineProperty(obj,key,value){return key in obj?Object.defineProperty(obj,key,{value:value,enumerable:!0,configurable:!0,writable:!0}):obj[key]=value,obj}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.init=void 0,_modal=_interopRequireDefault(_modal),externalServices=_interopRequireWildcard(externalServices),_templates=_interopRequireDefault(_templates),_modal_save_cancel=_interopRequireDefault(_modal_save_cancel),_modal_events=_interopRequireDefault(_modal_events),_modalform=_interopRequireDefault(_modalform),helper=_interopRequireWildcard(helper),manager=_interopRequireWildcard(manager),_localstorage=_interopRequireDefault(_localstorage),TinyAiUtils=_interopRequireWildcard(TinyAiUtils),_editor_utils=_interopRequireDefault(_editor_utils);const VIEW_CHATWINDOW="block_ai_chat_chatwindow",VIEW_OPENFULL="block_ai_chat_openfull",VIEW_DOCKRIGHT="block_ai_chat_dockright";let strHistory,strNewDialog,strToday,strYesterday,strDefinePersona,strNewPersona,strUserTemplates,strSystemTemplates,badge,viewmode,modal={},personaForm={},personaPrompt="",personaInfo="",personaLink="",personaNewname={},personaButtondelete={},personaUserinfo={},personaInputprompt={},systemTemplateHiddenInput={},showPersona=!1,optionsForm={},showOptions=!1,isAdmin=!1,modalopen=!1,conversation={id:0,messages:[]},allConversations=[],userid=0,contextid=0,firstLoad=!0,aiAtWork=!1,maxHistory=5,maxHistoryWarnings=new Set,tenantConfig={},chatConfig={};class DialogModal extends _modal.default{configure(modalConfig){modalConfig.show=!1,modalConfig.removeOnClose=!1,modalConfig.isVerticallyCentered=!1,super.configure(modalConfig),modalConfig.titletest&&this.setTitletest(modalConfig.titletest)}setTitletest(value){this.titletest=value}hide(){super.hide(),modalopen=!1;document.querySelector("body").classList.remove("block_ai_chat_open")}}_defineProperty(DialogModal,"TYPE","block_ai_chat/dialog_modal"),_defineProperty(DialogModal,"TEMPLATE","block_ai_chat/dialog_modal");_exports.init=async params=>{userid=params.userid,contextid=params.contextid,strNewDialog=params.new,strHistory=params.history,strDefinePersona=params.persona,strNewPersona=params.newpersona,strUserTemplates=params.usertemplates,strSystemTemplates=params.systemtemplates,personaPrompt=params.personaprompt,personaInfo=params.personainfo,showPersona=params.showpersona,showOptions=params.showoptions,personaLink=params.personalink,isAdmin=params.isadmin,badge=params.badge,badge=!1;const aiConfig=await(0,_config.getAiConfig)();tenantConfig=aiConfig,chatConfig=aiConfig.purposes.find((p=>"chat"===p.purpose)),modal=await DialogModal.create({templateContext:{title:strNewDialog,badge:false,showPersona:showPersona,showOptions:showOptions}}),modal.getRoot().on("modal:shown",(function(e){e.target.classList.add("block_ai_chat_modal")})),modal.getRoot().on(_modal_events.default.outsideClick,(event=>{checkOutsideClick(event)})),setView(),document.getElementById("ai_chat_button").addEventListener("mousedown",(async()=>{!async function(){if(modalopen)return void modal.hide();await modal.show(),modalopen=!0;document.querySelector("body").classList.add("block_ai_chat_open");const textarea=document.getElementById("block_ai_chat-input-id");addTextareaListener(textarea);if(document.getElementById("block_ai_chat-submit-id").addEventListener("click",(event=>{clickSubmitButton(event)})),firstLoad){await getConversations(),await showConversation();let reply=await externalServices.getConversationcontextLimit(contextid);maxHistory=reply.limit;document.getElementById("block_ai_chat_new_dialog").addEventListener("click",(()=>{newDialog()}));document.getElementById("block_ai_chat_delete_dialog").addEventListener("click",(()=>{deleteCurrentDialog()}));document.getElementById("block_ai_chat_show_history").addEventListener("click",(()=>{showHistory()}));const btnDefinePersona=document.getElementById("block_ai_chat_define_persona");btnDefinePersona&&btnDefinePersona.addEventListener("click",(async()=>{isAdmin?await(0,_notification.confirm)((0,_str.getString)("notice","block_ai_chat"),(0,_str.getString)("personasystemtemplateedit","block_ai_chat"),(0,_str.getString)("confirm","core"),null,showPersonasModal,null):await showPersonasModal()}));const btnOptions=document.getElementById("block_ai_chat_options");btnOptions&&btnOptions.addEventListener("click",(()=>{showOptionsModal()}));document.getElementById(VIEW_CHATWINDOW).addEventListener("click",(()=>{setView(VIEW_CHATWINDOW)}));document.getElementById(VIEW_OPENFULL).addEventListener("click",(()=>{setView(VIEW_OPENFULL)}));document.getElementById(VIEW_DOCKRIGHT).addEventListener("click",(()=>{setView(VIEW_DOCKRIGHT)})),await(0,_userquota.renderUserQuota)("#block_ai_chat_userquota",["chat"]),await(0,_infobox.renderInfoBox)("block_ai_chat",userid,'.block_ai_chat_modal_body [data-content="local_ai_manager_infobox"]',["chat"]);const warningBoxSelector=".local_ai_manager-ai-warning";document.querySelector(warningBoxSelector)&&await(0,_warningbox.renderWarningBox)(warningBoxSelector),""!==personaPrompt&&showUserinfo(!0);const message=await userAllowed();if(""!==message){const notice=await(0,_str.getString)("notice","block_ai_chat");await(0,_notification.alert)(notice,message)}const aiUtilsButton=document.querySelector('[data-action="openaiutils"]'),uniqid=Math.random().toString(16).slice(2);await TinyAiUtils.init(uniqid,_constants.constants.modalModes.standalone),aiUtilsButton.addEventListener("click",(async()=>{const selectionObject=window.getSelection(),range=selectionObject.getRangeAt(0),container=document.createElement("div");container.appendChild(range.cloneContents());const images=container.querySelectorAll("img");if(images.length>0&&images[0].src){const image=images[0],fetchResult=await fetch(image.src),data=await fetchResult.blob();TinyAiUtils.getDatamanager(uniqid).setSelectionImg(data)}selectionObject.toString()&&selectionObject.toString().length>0&&TinyAiUtils.getDatamanager(uniqid).setSelection(selectionObject.toString());const editorUtils=new _editor_utils.default(uniqid,"block_ai_chat",contextid,userid,null);TinyAiUtils.setEditorUtils(uniqid,editorUtils),await editorUtils.displayDialogue()})),firstLoad=!1}helper.focustextarea()}()})),strToday=await(0,_str.getString)("today","core"),strYesterday=await(0,_str.getString)("yesterday","block_ai_chat");window.matchMedia("(max-width: 576px)").addEventListener("change",handleScreenWidthChange),window.innerWidth<=576&&setView(VIEW_OPENFULL)};const getConversations=async()=>{try{allConversations=await externalServices.getAllConversations(userid,contextid)}catch(error){(0,_notification.exception)(error)}},showConversation=async function(){let id=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;aiAtWork||(0!==id?conversation=allConversations.find((x=>x.id===id)):void 0!==allConversations[0]?conversation=allConversations.at(allConversations.length-1):0===allConversations.length&&newDialog(!0),clearMessages(),setModalHeader(),await showMessages(),helper.renderMathjax())};document.showConversation=showConversation;const enterQuestion=async question=>{if(""==question)return void(aiAtWork=!1);const message=await userAllowed();if(""!==message){const notice=await(0,_str.getString)("noticenewquestion","block_ai_chat");return await(0,_notification.alert)(notice,message),void(aiAtWork=!1)}showMessage(question,"self",!1),conversation.messages[0]={message:personaPrompt,sender:"system"};let convHistory=await checkMessageHistoryLengthLimit(conversation.messages);""===personaPrompt.trim()&&convHistory.shift();const options={component:"block_ai_chat",conversationcontext:convHistory};if(0===conversation.id){try{let idresult=await externalServices.getNewConversationId(contextid);conversation.id=idresult.id,conversation.timecreated=Math.floor(Date.now()/1e3),setModalHeader((0,_helper2.escapeHTML)(question))}catch(error){(0,_notification.exception)(error)}options.forcenewitemid=!0}options.itemid=conversation.id;let requestresult=await manager.askLocalAiManager("chat",question,contextid,options);200!=requestresult.code&&(requestresult=await errorHandling(requestresult,question,contextid,options));let copy=document.querySelector(".block_ai_chat_modal .awaitanswer .copy");copy.addEventListener("mousedown",(()=>{helper.copyToClipboard(copy)})),await showReply(requestresult.result),helper.renderMathjax(),aiAtWork=!1,200==requestresult.code&&saveConversationLocally(question,requestresult.result);document.getElementById("block_ai_chat_userquota").innerHTML="",(0,_userquota.renderUserQuota)("#block_ai_chat_userquota",["chat"])},showReply=async text=>{let fields=document.querySelectorAll(".block_ai_chat_modal .awaitanswer .text");const field=fields[fields.length-1];field.innerHTML=text,field.classList.remove("small");let awaitdivs=document.querySelectorAll(".block_ai_chat_modal .awaitanswer");awaitdivs[awaitdivs.length-1].classList.remove("awaitanswer");const container=document.querySelector(".block_ai_chat-output-wrapper");field.scrollHeight<container.clientHeight&&(0,_helper2.scrollToBottom)()},showMessages=async()=>{for(const item of conversation.messages)await showMessage(item.message,item.sender)},showMessage=async function(text){let sender=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",answer=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];if("system"===sender)return;"ai"===sender&&(sender=""),answer||(text=(0,_helper2.escapeHTML)(text));const templateData={sender:sender,content:text,answer:answer},{html:html,js:js}=await _templates.default.renderForPromise("block_ai_chat/message",templateData);_templates.default.appendNodeContents(".block_ai_chat-output",html,js),helper.attachCopyListenerLast(),helper.scrollToBottom()},newDialog=async function(){let deleted=arguments.length>0&&void 0!==arguments[0]&&arguments[0];aiAtWork||(void 0!==allConversations.find((x=>x.id===conversation.id))||deleted||allConversations.push(conversation),conversation={id:0,messages:[]},clearMessages(),setModalHeader(strNewDialog),helper.focustextarea())},deleteCurrentDialog=()=>{(0,_notification.deleteCancelPromise)((0,_str.getString)("delete","block_ai_chat"),(0,_str.getString)("deletewarning","block_ai_chat")).then((async()=>{if(0!==conversation.id)try{await externalServices.deleteConversation(contextid,userid,conversation.id)&&(removeFromHistory(),await showConversation())}catch(error){(0,_notification.exception)(error)}})).catch((()=>{}))},showHistory=async()=>{void 0===allConversations.find((x=>x.id===conversation.id))&&allConversations.push(conversation);let title='<a href="#" id="block_ai_chat_backlink"><i class="icon fa fa-arrow-left"></i>'+strHistory+"</a>";clearMessages(!0),setModalHeader(title);document.getElementById("block_ai_chat_backlink").addEventListener("click",(async()=>{0!==conversation.id?await showConversation(conversation.id):newDialog(),clearMessages(),setModalHeader()})),document.querySelector(".block_ai_chat_modal").classList.add("onhistorypage");let groupedByDate={};allConversations.forEach((convo=>{if(void 0!==convo.messages[1]){let title=convo.messages[1].message;const now=new Date,date=new Date(1e3*convo.timecreated),today=new Date(now.getFullYear(),now.getMonth(),now.getDate()),yesterday=new Date(now.getFullYear(),now.getMonth(),now.getDate()-1),twoWeeksAgo=new Date(now);twoWeeksAgo.setDate(now.getDate()-14);const options={weekday:"long",day:"2-digit",month:"2-digit"},monthOptions={month:"long",year:"2-digit"};let dateString="";dateString=date>=today?strToday:date>=yesterday?strYesterday:date>=twoWeeksAgo?date.toLocaleDateString(void 0,options):date.toLocaleDateString(void 0,monthOptions);const hours=date.getHours(),minutes=date.getMinutes().toString().padStart(2,"0");let convItem={title:title,conversationid:convo.id,time:hours+":"+minutes};groupedByDate[dateString]||(groupedByDate[dateString]=[]),groupedByDate[dateString].push(convItem)}}));const templateData={dates:{groups:Object.keys(groupedByDate).map((key=>({key:key,objects:groupedByDate[key]})))}.groups},{html:html,js:js}=await _templates.default.renderForPromise("block_ai_chat/history",templateData);_templates.default.appendNodeContents(".block_ai_chat_modal .block_ai_chat-output",html,js);document.getElementById("ai_chat_history_new_dialog").addEventListener("mousedown",(()=>{newDialog()}))},removeFromHistory=()=>{0!==conversation.id&&void 0!==allConversations.find((x=>x.id===conversation.id))&&(allConversations=allConversations.filter((obj=>obj.id!==conversation.id)))},saveConversationLocally=(question,reply)=>{let message={message:question,sender:"user"};conversation.messages.push(message),message={message:reply,sender:"ai"},conversation.messages.push(message)},clearMessages=function(){let hideinput=arguments.length>0&&void 0!==arguments[0]&&arguments[0];const output=document.querySelector(".block_ai_chat-output");output.innerHTML="";let input=document.querySelector(".block_ai_chat-input");input.style.display=hideinput?"none":"flex"},setModalHeader=function(){let setTitle=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",modalheader=document.querySelector(".block_ai_chat_modal .modal-title div"),title="";null!==modalheader&&(conversation.messages.length>0||setTitle.length)&&(title=setTitle.length?setTitle:conversation.messages[1].message,modalheader.innerHTML=title);let modal=document.querySelector(".block_ai_chat_modal");modal.classList.remove("onhistorypage")},addTextareaListener=textarea=>{textarea.addEventListener("keydown",(event=>{textareaOnKeydown(event),textarea.style.height="auto";const computedStyles=window.getComputedStyle(textarea),lineHeight=parseFloat(computedStyles.lineHeight),paddingTop=parseFloat(computedStyles.paddingTop),paddingBottom=parseFloat(computedStyles.paddingBottom),borderTop=parseFloat(computedStyles.borderTopWidth),borderBottom=parseFloat(computedStyles.borderBottomWidth),maxHeight=4*lineHeight+paddingTop+paddingBottom+borderTop+borderBottom,newHeight=Math.min(textarea.scrollHeight+borderTop+borderBottom,maxHeight);textarea.style.height=newHeight+"px"}))},textareaOnKeydown=event=>{"Enter"!==event.key||aiAtWork||event.shiftKey||(aiAtWork=!0,enterQuestion(event.target.value),event.preventDefault(),event.target.value="")},clickSubmitButton=()=>{if(!aiAtWork){aiAtWork=!0;const textarea=document.getElementById("block_ai_chat-input-id");enterQuestion(textarea.value),textarea.value=""}},errorHandling=async(requestresult,question,contextid,options)=>{if(409==requestresult.code)for(;409==requestresult.code;){try{let idresult=await externalServices.getNewConversationId(contextid);conversation.id=idresult.id,options.itemid=conversation.id}catch(error){(0,_notification.exception)(error)}return requestresult=await manager.askLocalAiManager("chat",question,contextid,options)}const errorString=await(0,_str.getString)("errorwithcode","block_ai_chat",requestresult.code),result=JSON.parse(requestresult.result);await(0,_notification.alert)(errorString,result.message);const answerdivs=document.querySelectorAll(".awaitanswer");return answerdivs[answerdivs.length-1].closest(".message").classList.add("text-danger"),requestresult.result=await(0,_str.getString)("error","block_ai_chat"),requestresult},checkMessageHistoryLengthLimit=async messages=>{if(messages.length>maxHistory){let shortenedMessages=[messages[0],...messages.slice(-maxHistory)];if(!maxHistoryWarnings.has(conversation.id)){const maxHistoryString=await(0,_str.getString)("maxhistory","block_ai_chat",maxHistory),warningErrorString=await(0,_str.getString)("maxhistoryreached","block_ai_chat",maxHistory);await(0,_notification.alert)(maxHistoryString,warningErrorString),maxHistoryWarnings.add(conversation.id)}return shortenedMessages}return messages},checkOutsideClick=event=>{viewmode!=VIEW_OPENFULL&&event.preventDefault()},setView=async function(){let mode=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";const key=await(0,_helper2.hash)("chatmode"+userid);let savedmode=_localstorage.default.get(key);""==mode&&(mode=savedmode||VIEW_CHATWINDOW),_localstorage.default.set(key,mode),viewmode=mode;const body=document.querySelector("body");body.classList.remove(VIEW_CHATWINDOW,VIEW_OPENFULL,VIEW_DOCKRIGHT),body.classList.add(mode)},userAllowed=async()=>{let message;if(!1===tenantConfig.tenantenabled)return message=await(0,_str.getString)("error_http403disabled","local_ai_manager"),message;if(!1===tenantConfig.userconfirmed){message=await(0,_str.getString)("error_http403notconfirmed","local_ai_manager"),message+=". ";const link=window.location.origin+"/local/ai_manager/confirm_ai_usage.php";return message+=await(0,_str.getString)("confirm_ai_usage","block_ai_chat",link),message}return!0===tenantConfig.userlocked?(message=await(0,_str.getString)("error_http403blocked","local_ai_manager"),message):!1===chatConfig.isconfigured?(message=await(0,_str.getString)("error_purposenotconfigured","local_ai_manager"),message):!0===chatConfig.lockedforrole?(message=await(0,_str.getString)("error_http403blocked","local_ai_manager"),message):!0===chatConfig.limitreached?(message=await(0,_str.getString)("error_limitreached","local_ai_manager"),message):""},handleScreenWidthChange=e=>{const body=document.querySelector("body");e.matches?(body.classList.remove(VIEW_CHATWINDOW,VIEW_OPENFULL,VIEW_DOCKRIGHT),body.classList.add(VIEW_OPENFULL)):(body.classList.remove(VIEW_CHATWINDOW,VIEW_OPENFULL,VIEW_DOCKRIGHT),body.classList.add(viewmode))},showPersonasModal=()=>{personaForm=new _modalform.default({formClass:"block_ai_chat\\form\\persona_form",moduleName:"block_ai_chat/modal_save_delete_cancel",args:{contextid:contextid},modalConfig:{title:strDefinePersona}}),personaForm.show(),personaForm.addEventListener(personaForm.events.LOADED,(()=>{personaForm.modal.getRoot().on(_modal_events.default.bodyRendered,(()=>{const inputprompts=document.querySelector('input[name="prompts"]'),prompts=JSON.parse(inputprompts.value),select=document.querySelector('select[name="template"]'),addpersona=document.querySelector("#add_persona"),copypersona=document.querySelector("#copy_persona");personaNewname=document.querySelector('input[name="name"]'),personaInputprompt=document.querySelector('textarea[name="prompt"]'),personaUserinfo=document.querySelector('textarea[name="userinfo"]');const inputtemplateids=document.querySelector('input[name="templateids"]'),templateids=JSON.parse(inputtemplateids.value),inputuserinfos=document.querySelector('input[name="userinfos"]'),userinfos=JSON.parse(inputuserinfos.value);personaButtondelete=document.querySelector('[data-custom="delete"]'),systemTemplateHiddenInput=document.querySelector('[data-type="systemtemplate"]'),personaNewname.value=personaNewname.value.trim(),select.options.forEach((option=>{templateids.map((id=>parseInt(id))).includes(parseInt(option.value))||0===parseInt(option.value)||select.options[select.options.length-1].after(option)})),manageInputs(!1,templateids,select.value),select.addEventListener("change",(event=>{let selectValue=event.target.value,selectText=event.target.options[select.selectedIndex].text.trim();manageInputs(!0),void 0!==prompts[selectValue]?(personaInputprompt.value=prompts[selectValue],personaNewname.value=selectText,personaNewname.setAttribute("placeholder",""),personaNewname.setAttribute("value",selectText),personaUserinfo.value=userinfos[selectValue],personaUserinfo.disabled=!1,personaInputprompt.disabled=!1):(personaNewname.setAttribute("value",""),personaInputprompt.value="",personaInputprompt.disabled=!0,personaUserinfo.value="",personaUserinfo.disabled=!0),manageInputs(!1,templateids,selectValue)})),select.addEventListener("click",(()=>{let option=document.querySelector(".new-persona-placeholder");option&&select.removeChild(option)}));const useroptions=select.options.length>templateids.length,spacer=new Option("","",!1,!1);spacer.disabled=!0,spacer.classList.add("select-spacer"),select.insertBefore(spacer,select.options[1]);const systemtemplates=new Option(strSystemTemplates,"",!1,!1);if(systemtemplates.disabled=!0,select.insertBefore(systemtemplates,select.options[2]),useroptions){const maxValue=Math.max(...templateids.map((id=>parseInt(id)))),lastSystemOption=Array.from(select.options).find((opt=>parseInt(opt.value)===maxValue)),usertemplates=new Option(strUserTemplates,"",!1,!1);usertemplates.disabled=!0,select.insertBefore(usertemplates,lastSystemOption.nextSibling)}addpersona.addEventListener("click",(()=>{addPersona(!1,select)})),copypersona.addEventListener("click",(()=>{addPersona(!0,select)}));document.querySelectorAll('[data-action="save"]').forEach((button=>{button.addEventListener("click",(async e=>{const deleteinput=document.querySelector('input[name="delete"]');if("delete"==e.target.dataset.custom)deleteinput.value="1","1"!==e.target.dataset.confirmed&&(e.stopPropagation(),await(0,_notification.confirm)((0,_str.getString)("delete","core"),(0,_str.getString)("areyousuredelete","block_ai_chat"),(0,_str.getString)("delete","core"),null,(()=>{e.target.dataset.confirmed="1",e.target.click()}),null));else if(deleteinput.value="0",""==select.value&&isAdmin&&"1"!==e.target.dataset.confirmed){e.stopPropagation();const modal=await _modal_save_cancel.default.create({title:(0,_str.getString)("systemorpersonal_title","block_ai_chat"),body:(0,_str.getString)("systemorpersonal_question","block_ai_chat"),buttons:{save:(0,_str.getString)("systemtemplate","block_ai_chat"),cancel:(0,_str.getString)("personaltemplate","block_ai_chat")},removeOnClose:!0,show:!0});modal.getRoot().on(_modal_events.default.save,(()=>{systemTemplateHiddenInput.value=1,e.target.dataset.confirmed="1",e.target.click()})),modal.getRoot().on(_modal_events.default.cancel,(()=>{systemTemplateHiddenInput.value=0,e.target.dataset.confirmed="1",e.target.click()}))}}))}))}))})),personaForm.addEventListener(personaForm.events.SUBMIT_BUTTON_PRESSED,(()=>{manageInputs(!0)})),personaForm.addEventListener(personaForm.events.SERVER_VALIDATION_ERROR,(()=>{manageInputs(!0)})),personaForm.addEventListener(personaForm.events.FORM_SUBMITTED,(async()=>{let reply=await externalServices.reloadPersona(contextid);personaPrompt=reply.prompt,personaInfo=reply.info,showUserinfo(!1)}))},showOptionsModal=()=>{optionsForm=new _modalform.default({formClass:"block_ai_chat\\form\\options_form",moduleName:"core/modal_save_cancel",args:{contextid:contextid},modalConfig:{title:(0,_str.getString)("options")}}),optionsForm.show()},addPersona=(copy,select)=>{if(personaNewname.disabled=!1,personaNewname.placeholder=strNewPersona,personaNewname.value="",personaInputprompt.disabled=!1,personaUserinfo.disabled=!1,copy||(personaInputprompt.value="",personaUserinfo.value=""),!document.querySelector(".new-persona-placeholder")){let signifierOption=new Option(strNewPersona,"",!0,!0);signifierOption.classList.add("new-persona-placeholder"),select.add(signifierOption)}},manageInputs=function(switchon){let templateids=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],selectValue=arguments.length>2&&void 0!==arguments[2]?arguments[2]:42;if((switchon||isAdmin)&&0!=selectValue)return personaNewname.disabled=!1,personaButtondelete.disabled=!1,personaInputprompt.disabled=!1,void(personaUserinfo.disabled=!1);null===document.querySelector(".is-invalid")&&(templateids.includes(selectValue)||0==selectValue?(personaNewname.disabled=!0,personaButtondelete.disabled=!0,personaInputprompt.disabled=!0,personaUserinfo.disabled=!0):(personaNewname.disabled=!1,personaButtondelete.disabled=!1,personaInputprompt.disabled=!1,personaUserinfo.disabled=!1))},showUserinfo=async first=>{if(!first){const toDelete=document.querySelector(".local_ai_manager-infobox.alert.alert-info");toDelete&&toDelete.remove()}if(""!=personaInfo.trim()){const targetElement=document.querySelector(".block_ai_chat_modal_body .infobox"),templateContext={persona:personaInfo,personainfourl:personaLink},{html:html,js:js}=await _templates.default.renderForPromise("block_ai_chat/persona_infobox",templateContext);_templates.default.appendNodeContents(targetElement,html,js)}}}));

//# sourceMappingURL=dialog.min.js.map