define("block_ai_chat/webservices",["exports","core/ajax"],(function(_exports,_ajax){Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.reloadPersona=_exports.getNewConversationId=_exports.getConversationcontextLimit=_exports.getAllConversations=_exports.deleteConversation=void 0;_exports.getAllConversations=(userid,contextid)=>(0,_ajax.call)([{methodname:"block_ai_chat_get_all_conversations",args:{userid:userid,contextid:contextid}}])[0];_exports.getNewConversationId=contextid=>(0,_ajax.call)([{methodname:"block_ai_chat_get_new_conversation_id",args:{contextid:contextid}}])[0];_exports.deleteConversation=(contextid,userid,conversationid)=>(0,_ajax.call)([{methodname:"block_ai_chat_delete_conversation",args:{contextid:contextid,userid:userid,conversationid:conversationid}}])[0];_exports.getConversationcontextLimit=contextid=>(0,_ajax.call)([{methodname:"block_ai_chat_get_conversationcontext_limit",args:{contextid:contextid}}])[0];_exports.reloadPersona=contextid=>(0,_ajax.call)([{methodname:"block_ai_chat_reload_persona",args:{contextid:contextid}}])[0]}));

//# sourceMappingURL=webservices.min.js.map