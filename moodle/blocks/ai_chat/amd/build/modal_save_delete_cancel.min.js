define("block_ai_chat/modal_save_delete_cancel",["exports","core/modal","core/notification"],(function(_exports,_modal,_notification){function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}function _defineProperty(obj,key,value){return key in obj?Object.defineProperty(obj,key,{value:value,enumerable:!0,configurable:!0,writable:!0}):obj[key]=value,obj}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0,_modal=_interopRequireDefault(_modal),_notification=_interopRequireDefault(_notification);class ModalSaveDeleteCancel extends _modal.default{constructor(root){super(root),this.getFooter().find(this.getActionSelector("save")).length||_notification.default.exception({message:"No save button found"}),this.getFooter().find('[data-custom="delete"]').length||_notification.default.exception({message:"No delete button found"}),this.getFooter().find(this.getActionSelector("cancel")).length||_notification.default.exception({message:"No cancel button found"})}registerEventListeners(){super.registerEventListeners(),this.registerCloseOnSave(),this.registerCloseOnDelete(),this.registerCloseOnCancel()}setFooter(){_notification.default.exception({message:"Can not change the footer of a save delete cancel modal"})}setDeleteButtonText(value){return this.setButtonText("delete",value)}setSaveButtonText(value){return this.setButtonText("save",value)}}return _exports.default=ModalSaveDeleteCancel,_defineProperty(ModalSaveDeleteCancel,"TYPE","SAVE_DELETE_CANCEL"),_defineProperty(ModalSaveDeleteCancel,"TEMPLATE","block_ai_chat/modal_save_delete_cancel"),ModalSaveDeleteCancel.registerModalType(),_exports.default}));

//# sourceMappingURL=modal_save_delete_cancel.min.js.map