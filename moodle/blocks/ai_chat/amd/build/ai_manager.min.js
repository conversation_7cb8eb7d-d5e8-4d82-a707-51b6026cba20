define("block_ai_chat/ai_manager",["exports","local_ai_manager/make_request"],(function(_exports,_make_request){Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.askLocalAiManager=void 0;_exports.askLocalAiManager=async function(purpose,prompt,contextid){let options=arguments.length>3&&void 0!==arguments[3]?arguments[3]:[],result={};try{result=await(0,_make_request.makeRequest)(purpose,prompt,"block_ai_chat",contextid,options)}catch(error){result.code="aiconnector",result.result=error.error+" "+error.message,result.result+=error.backtrace}return result}}));

//# sourceMappingURL=ai_manager.min.js.map