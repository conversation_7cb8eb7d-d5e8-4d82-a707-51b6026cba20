{"version": 3, "file": "modal_save_delete_cancel.min.js", "sources": ["../src/modal_save_delete_cancel.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Moodle is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Contain the logic for the save/delete/cancel modal.\n *\n * @module     core/modal_save_delete_cancel\n * @copyright  2022 Laurent <PERSON> <<EMAIL>>\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\nimport Modal from 'core/modal';\nimport Notification from 'core/notification';\n\n/**\n * The Delete/Cancel Modal.\n *\n * @class\n * @extends module:core/modal\n */\nexport default class ModalSaveDeleteCancel extends Modal {\n    static TYPE = 'SAVE_DELETE_CANCEL';\n    static TEMPLATE = 'block_ai_chat/modal_save_delete_cancel';\n\n    constructor(root) {\n        super(root);\n\n        if (!this.getFooter().find(this.getActionSelector('save')).length) {\n            Notification.exception({message: 'No save button found'});\n        }\n\n        if (!this.getFooter().find('[data-custom=\"delete\"]').length) {\n            Notification.exception({message: 'No delete button found'});\n        }\n\n        if (!this.getFooter().find(this.getActionSelector('cancel')).length) {\n            Notification.exception({message: 'No cancel button found'});\n        }\n    }\n\n    /**\n     * Register all event listeners.\n     */\n    registerEventListeners() {\n        // Call the parent registration.\n        super.registerEventListeners();\n\n        // Register to close on delete/cancel.\n        this.registerCloseOnSave();\n        this.registerCloseOnDelete();\n        this.registerCloseOnCancel();\n    }\n\n    /**\n     * Override parent implementation to prevent changing the footer content.\n     */\n    setFooter() {\n        Notification.exception({message: 'Can not change the footer of a save delete cancel modal'});\n    }\n\n    /**\n     * Set the title of the delete button.\n     *\n     * @param {String|Promise} value The button text, or a Promise which will resolve it\n     * @returns{Promise}\n     */\n    setDeleteButtonText(value) {\n        return this.setButtonText('delete', value);\n    }\n\n\n    /**\n     * Set the title of the save button.\n     *\n     * @param {String|Promise} value The button text, or a Promise which will resolve it\n     * @returns{Promise}\n     */\n    setSaveButtonText(value) {\n        return this.setButtonText('save', value);\n    }\n}\n\nModalSaveDeleteCancel.registerModalType();\n"], "names": ["ModalSaveDeleteCancel", "Modal", "constructor", "root", "this", "getFooter", "find", "getActionSelector", "length", "exception", "message", "registerEventListeners", "registerCloseOnSave", "registerCloseOnDelete", "registerCloseOnCancel", "setFooter", "setDeleteButtonText", "value", "setButtonText", "setSaveButtonText", "registerModalType"], "mappings": "4iBA+BqBA,8BAA8BC,eAI/CC,YAAYC,YACFA,MAEDC,KAAKC,YAAYC,KAAKF,KAAKG,kBAAkB,SAASC,8BAC1CC,UAAU,CAACC,QAAS,yBAGhCN,KAAKC,YAAYC,KAAK,0BAA0BE,8BACpCC,UAAU,CAACC,QAAS,2BAGhCN,KAAKC,YAAYC,KAAKF,KAAKG,kBAAkB,WAAWC,8BAC5CC,UAAU,CAACC,QAAS,2BAOzCC,+BAEUA,8BAGDC,2BACAC,6BACAC,wBAMTC,kCACiBN,UAAU,CAACC,QAAS,4DASrCM,oBAAoBC,cACTb,KAAKc,cAAc,SAAUD,OAUxCE,kBAAkBF,cACPb,KAAKc,cAAc,OAAQD,sEA1DrBjB,6BACH,sCADGA,iCAEC,0CA4DtBA,sBAAsBoB"}