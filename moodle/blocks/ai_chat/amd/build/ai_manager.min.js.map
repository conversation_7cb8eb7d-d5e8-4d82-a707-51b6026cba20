{"version": 3, "file": "ai_manager.min.js", "sources": ["../src/ai_manager.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// <PERSON><PERSON>le is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\nimport {makeRequest} from 'local_ai_manager/make_request';\n\n/**\n * Get the async answer from the local_ai_manager.\n *\n * @param {string} purpose\n * @param {string} prompt\n * @param {number} contextid\n * @param {array} options\n * @returns {string}\n */\nexport const askLocalAiManager = async(purpose, prompt, contextid, options = []) => {\n    let result = {};\n    try {\n        result = await makeRequest(purpose, prompt, 'block_ai_chat', contextid, options);\n    } catch (error) {\n        result.code = 'aiconnector';\n        result.result = error.error + \" \" + error.message;\n        // For devs.\n        result.result += error.backtrace;\n    }\n    return result;\n};\n"], "names": ["async", "purpose", "prompt", "contextid", "options", "result", "error", "code", "message", "backtrace"], "mappings": "qOA0BiCA,eAAMC,QAASC,OAAQC,eAAWC,+DAAU,GACrEC,OAAS,OAETA,aAAe,6BAAYJ,QAASC,OAAQ,gBAAiBC,UAAWC,SAC1E,MAAOE,OACLD,OAAOE,KAAO,cACdF,OAAOA,OAASC,MAAMA,MAAQ,IAAMA,MAAME,QAE1CH,OAAOA,QAAUC,MAAMG,iBAEpBJ"}