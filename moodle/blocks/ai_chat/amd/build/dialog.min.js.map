{"version": 3, "file": "dialog.min.js", "sources": ["../src/dialog.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Moodle is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\nimport Modal from 'core/modal';\nimport * as externalServices from 'block_ai_chat/webservices';\nimport Templates from 'core/templates';\nimport {alert as displayAlert, exception as displayException, deleteCancelPromise,\n    confirm as confirmModal} from 'core/notification';\nimport SaveCancelModal from 'core/modal_save_cancel';\nimport ModalEvents from 'core/modal_events';\nimport ModalForm from 'core_form/modalform';\nimport * as helper from 'block_ai_chat/helper';\nimport * as manager from 'block_ai_chat/ai_manager';\nimport {getString} from 'core/str';\nimport {renderInfoBox} from 'local_ai_manager/infobox';\nimport {renderUserQuota} from 'local_ai_manager/userquota';\nimport {renderWarningBox} from 'local_ai_manager/warningbox';\nimport {getAiConfig} from 'local_ai_manager/config';\nimport LocalStorage from 'core/localstorage';\nimport {escapeHTML, hash, scrollToBottom} from './helper';\nimport * as TinyAiUtils from 'tiny_ai/utils';\nimport TinyAiEditorUtils from 'tiny_ai/editor_utils';\nimport {constants as TinyAiConstants} from 'tiny_ai/constants';\n\n// Declare variables.\nconst VIEW_CHATWINDOW = 'block_ai_chat_chatwindow';\nconst VIEW_OPENFULL = 'block_ai_chat_openfull';\nconst VIEW_DOCKRIGHT = 'block_ai_chat_dockright';\nconst MODAL_OPEN = 'block_ai_chat_open';\n\n// Modal.\nlet modal = {};\nlet strHistory;\nlet strNewDialog;\nlet strToday;\nlet strYesterday;\nlet strDefinePersona;\nlet strNewPersona;\nlet strUserTemplates;\nlet strSystemTemplates;\nlet personaForm = {};\nlet personaPrompt = '';\nlet personaInfo = '';\nlet personaLink = '';\nlet personaNewname = {};\nlet personaButtondelete = {};\nlet personaUserinfo = {};\nlet personaInputprompt = {};\nlet systemTemplateHiddenInput = {};\nlet showPersona = false;\nlet optionsForm = {};\nlet showOptions = false;\nlet isAdmin = false;\nlet badge;\nlet viewmode;\nlet modalopen = false;\n\n// Current conversation.\nlet conversation = {\n    id: 0,\n    messages: [],\n};\n// All conversations.\nlet allConversations = [];\n// Userid.\nlet userid = 0;\n// Block context id.\nlet contextid = 0;\n// First load.\nlet firstLoad = true;\n// AI in process of answering.\nlet aiAtWork = false;\n// Maximum history included in query, should be reset via webservice.\nlet maxHistory = 5;\n// Remember warnings for maximum history in this session.\nlet maxHistoryWarnings = new Set();\n// Tenantconfig.\nlet tenantConfig = {};\nlet chatConfig = {};\n\nclass DialogModal extends Modal {\n    static TYPE = \"block_ai_chat/dialog_modal\";\n    static TEMPLATE = \"block_ai_chat/dialog_modal\";\n\n    configure(modalConfig) {\n        // Show this modal on instantiation.\n        modalConfig.show = false;\n\n        // Remove from the DOM on close.\n        modalConfig.removeOnClose = false;\n\n        modalConfig.isVerticallyCentered = false;\n\n        super.configure(modalConfig);\n\n        // Accept our own custom arguments too.\n        if (modalConfig.titletest) {\n            this.setTitletest(modalConfig.titletest);\n        }\n    }\n\n    setTitletest(value) {\n        this.titletest = value;\n    }\n\n    hide() {\n        super.hide();\n        // Keep track of state, to restrict changes to block_ai_chat modal.\n        modalopen = false;\n        const body = document.querySelector('body');\n        body.classList.remove(MODAL_OPEN);\n    }\n}\n\nexport const init = async(params) => {\n    // Read params.\n    userid = params.userid;\n    contextid = params.contextid;\n    strNewDialog = params.new;\n    strHistory = params.history;\n    strDefinePersona = params.persona;\n    strNewPersona = params.newpersona;\n    strUserTemplates = params.usertemplates;\n    strSystemTemplates = params.systemtemplates;\n    personaPrompt = params.personaprompt;\n    personaInfo = params.personainfo;\n    showPersona = params.showpersona;\n    showOptions = params.showoptions;\n    personaLink = params.personalink;\n    isAdmin = params.isadmin;\n    badge = params.badge;\n    // Disable badge.\n    badge = false;\n\n    // Get configuration.\n    const aiConfig = await getAiConfig();\n    tenantConfig = aiConfig;\n    chatConfig = aiConfig.purposes.find(p => p.purpose === \"chat\");\n\n    // Build chat dialog modal.\n    modal = await DialogModal.create({\n        templateContext: {\n            title: strNewDialog,\n            badge: badge,\n            showPersona: showPersona,\n            showOptions: showOptions,\n        },\n    });\n\n    // Add class for styling when modal is displayed.\n    modal.getRoot().on('modal:shown', function(e) {\n        e.target.classList.add(\"block_ai_chat_modal\");\n    });\n\n    // Conditionally prevent outside click event.\n    modal.getRoot().on(ModalEvents.outsideClick, event => {\n        checkOutsideClick(event);\n    });\n\n    // Check and set viewmode.\n    setView();\n\n    // Attach listener to the ai button to call modal.\n    let button = document.getElementById('ai_chat_button');\n    button.addEventListener('mousedown', async() => {\n        showModal(params);\n    });\n\n    // Get strings.\n    strToday = await getString('today', 'core');\n    strYesterday = await getString('yesterday', 'block_ai_chat');\n\n    // Create a MediaQueryList object to check for small screens.\n    const mediaQuery = window.matchMedia(\"(max-width: 576px)\");\n\n    // Attach the event listener to handle changes.\n    mediaQuery.addEventListener('change', handleScreenWidthChange);\n\n    // Initial check for screenwidth.\n    if (window.innerWidth <= 576) {\n        setView(VIEW_OPENFULL);\n    }\n};\n\n/**\n * Show ai_chat modal.\n */\nasync function showModal() {\n    // Switch for repeated clicking.\n    if (modalopen) {\n        modal.hide();\n        return;\n    }\n\n    // Show modal.\n    await modal.show();\n    modalopen = true;\n    const body = document.querySelector('body');\n    body.classList.add(MODAL_OPEN);\n\n    // Add listener for input submission.\n    const textarea = document.getElementById('block_ai_chat-input-id');\n    addTextareaListener(textarea);\n    const button = document.getElementById('block_ai_chat-submit-id');\n    button.addEventListener(\"click\", (event) => {\n        clickSubmitButton(event);\n    });\n\n    if (firstLoad) {\n        // Load conversations.\n        await getConversations();\n\n        // Show conversation.\n        await showConversation();\n\n        // Get conversationcontext message limit.\n        let reply = await externalServices.getConversationcontextLimit(contextid);\n        maxHistory = reply.limit;\n\n        // Add listeners for dropdownmenus.\n        // Actions.\n        const btnNewDialog = document.getElementById('block_ai_chat_new_dialog');\n        btnNewDialog.addEventListener('click', () => {\n            newDialog();\n        });\n        const btnDeleteDialog = document.getElementById('block_ai_chat_delete_dialog');\n        btnDeleteDialog.addEventListener('click', () => {\n            deleteCurrentDialog();\n        });\n        const btnShowHistory = document.getElementById('block_ai_chat_show_history');\n        btnShowHistory.addEventListener('click', () => {\n            showHistory();\n        });\n        const btnDefinePersona = document.getElementById('block_ai_chat_define_persona');\n        if (btnDefinePersona) {\n            btnDefinePersona.addEventListener('click', async() => {\n                if (isAdmin) {\n                    await confirmModal(\n                        getString('notice', 'block_ai_chat'),\n                        getString('personasystemtemplateedit', 'block_ai_chat'),\n                        getString('confirm', 'core'),\n                        null,\n                        showPersonasModal,\n                        null\n                    );\n                } else {\n                    await showPersonasModal();\n                }\n            });\n        }\n        const btnOptions = document.getElementById('block_ai_chat_options');\n        if (btnOptions) {\n            btnOptions.addEventListener('click', () => {\n                showOptionsModal();\n            });\n        }\n        // Views.\n        const btnChatwindow = document.getElementById(VIEW_CHATWINDOW);\n        btnChatwindow.addEventListener('click', () => {\n            setView(VIEW_CHATWINDOW);\n        });\n        const btnFullWidth = document.getElementById(VIEW_OPENFULL);\n        btnFullWidth.addEventListener('click', () => {\n            setView(VIEW_OPENFULL);\n        });\n        const btnDockRight = document.getElementById(VIEW_DOCKRIGHT);\n        btnDockRight.addEventListener('click', () => {\n            setView(VIEW_DOCKRIGHT);\n        });\n\n        // Show userquota.\n        await renderUserQuota('#block_ai_chat_userquota', ['chat']);\n        // Show infobox.\n        await renderInfoBox(\n            'block_ai_chat', userid, '.block_ai_chat_modal_body [data-content=\"local_ai_manager_infobox\"]', ['chat']\n        );\n        // Show ai info warning.\n        const warningBoxSelector = '.local_ai_manager-ai-warning';\n        if (document.querySelector(warningBoxSelector)) {\n            await renderWarningBox(warningBoxSelector);\n        }\n        // Show persona info.\n        if (personaPrompt !== '') {\n            showUserinfo(true);\n        }\n\n        // Check if all permissions and settings are correct.\n        const message = await userAllowed();\n        if (message !== '') {\n            const notice = await getString('notice', 'block_ai_chat');\n            await displayAlert(notice, message);\n        }\n\n        const aiUtilsButton = document.querySelector('[data-action=\"openaiutils\"]');\n        const uniqid = Math.random().toString(16).slice(2);\n\n        await TinyAiUtils.init(uniqid, TinyAiConstants.modalModes.standalone);\n        aiUtilsButton.addEventListener('click', async() => {\n            // We try to find selected text or images and inject it into the AI tools.\n            const selectionObject = window.getSelection();\n            const range = selectionObject.getRangeAt(0);\n            const container = document.createElement('div');\n            container.appendChild(range.cloneContents());\n            const images = container.querySelectorAll('img');\n            if (images.length > 0 && images[0].src) {\n                // If there are more than one we just use the first one.\n                const image = images[0];\n                // This should work for both external and data urls.\n                const fetchResult = await fetch(image.src);\n                const data = await fetchResult.blob();\n                TinyAiUtils.getDatamanager(uniqid).setSelectionImg(data);\n            }\n\n            // If currently there is text selected we inject it.\n            if (selectionObject.toString() && selectionObject.toString().length > 0) {\n                TinyAiUtils.getDatamanager(uniqid).setSelection(selectionObject.toString());\n            }\n\n            const editorUtils = new TinyAiEditorUtils(uniqid, 'block_ai_chat', contextid, userid, null);\n            TinyAiUtils.setEditorUtils(uniqid, editorUtils);\n            await editorUtils.displayDialogue();\n        });\n\n        firstLoad = false;\n    }\n\n    helper.focustextarea();\n}\n\n\n/**\n * Webservice Get all conversations.\n */\nconst getConversations = async() => {\n    try {\n        allConversations = await externalServices.getAllConversations(userid, contextid);\n    } catch (error) {\n        displayException(error);\n    }\n};\n\n/**\n * Function to set conversation.\n * @param {*} id\n */\nconst showConversation = async(id = 0) => {\n    // Dissallow changing conversations when question running.\n    if (aiAtWork) {\n        return;\n    }\n    // Change conversation or get last conversation.\n    if (id !== 0) {\n        // Set selected conversation.\n        conversation = allConversations.find(x => x.id === id);\n    } else if (typeof allConversations[0] !== 'undefined') {\n        // Set last conversation.\n        conversation = allConversations.at(allConversations.length - 1);\n    } else if (allConversations.length === 0) {\n        // Last conversation has been deleted.\n        newDialog(true);\n    }\n    clearMessages();\n    setModalHeader();\n    await showMessages();\n    helper.renderMathjax();\n};\n// Make globally accessible since it is used to show history in dropdownmenuitem.mustache.\ndocument.showConversation = showConversation;\n\n\n/**\n * Send input to ai connector.\n * @param {*} question\n */\nconst enterQuestion = async(question) => {\n\n    // Deny changing dialogs until answer present?\n    if (question == '') {\n        aiAtWork = false;\n        return;\n    }\n    const message = await userAllowed();\n    if (message !== '') {\n        const notice = await getString('noticenewquestion', 'block_ai_chat');\n        await displayAlert(notice, message);\n        aiAtWork = false;\n        return;\n    }\n\n    // Add to conversation, answer not yet available.\n    showMessage(question, 'self', false);\n\n    // For first message, add the personaprompt, even if empty.\n    // Since we dont know if the personaPrompt was changed, always replace it.\n    conversation.messages[0] = {\n        'message': personaPrompt,\n        'sender': 'system'\n    };\n\n    // Check history for length limit.\n    let convHistory = await checkMessageHistoryLengthLimit(conversation.messages);\n\n    // Since some models cant handle an empty system message, remove from convHistory.\n    if (personaPrompt.trim() === '') {\n        convHistory.shift();\n    }\n\n    // Options, with conversation history.\n    const options = {\n        'component': 'block_ai_chat',\n        'conversationcontext': convHistory\n    };\n\n    // For a new conversation, get an id.\n    if (conversation.id === 0) {\n        try {\n            let idresult = await externalServices.getNewConversationId(contextid);\n            conversation.id = idresult.id;\n            conversation.timecreated = Math.floor(Date.now() / 1000);\n            setModalHeader(escapeHTML(question));\n        } catch (error) {\n            displayException(error);\n        }\n        options.forcenewitemid = true;\n    }\n\n    // Pass itemid / conversationid.\n    options.itemid = conversation.id;\n\n    // Send to local_ai_manager.\n    let requestresult = await manager.askLocalAiManager('chat', question, contextid, options);\n\n    // Handle errors.\n    if (requestresult.code != 200) {\n        requestresult = await errorHandling(requestresult, question, contextid, options);\n    }\n\n    // Attach copy listener.\n    let copy = document.querySelector('.block_ai_chat_modal .awaitanswer .copy');\n    copy.addEventListener('mousedown', () => {\n        helper.copyToClipboard(copy);\n    });\n\n    // Write back answer.\n    await showReply(requestresult.result);\n\n    // Render mathjax.\n    helper.renderMathjax();\n\n    // Ai is done.\n    aiAtWork = false;\n\n    // Save new question and answer.\n    if (requestresult.code == 200) {\n        saveConversationLocally(question, requestresult.result);\n    }\n\n    // Update userquota.\n    const userquota = document.getElementById('block_ai_chat_userquota');\n    userquota.innerHTML = '';\n    renderUserQuota('#block_ai_chat_userquota', ['chat']);\n};\n\n/**\n * Render reply.\n * @param {string} text\n */\nconst showReply = async(text) => {\n    // Get textblock.\n    let fields = document.querySelectorAll('.block_ai_chat_modal .awaitanswer .text');\n    const field = fields[fields.length - 1];\n    // Render the reply.\n    field.innerHTML = text;\n    field.classList.remove('small');\n\n    // Remove awaitanswer class.\n    let awaitdivs = document.querySelectorAll('.block_ai_chat_modal .awaitanswer');\n    const awaitdiv = awaitdivs[awaitdivs.length - 1];\n    awaitdiv.classList.remove('awaitanswer');\n\n    // Check if answer is smaller than the viewport, if so scroll to bottom.\n    const container = document.querySelector('.block_ai_chat-output-wrapper');\n    if (field.scrollHeight < container.clientHeight) {\n        scrollToBottom();\n    }\n};\n\nconst showMessages = async() => {\n    for (const item of conversation.messages) {\n        await showMessage(item.message, item.sender);\n    }\n};\n\n/**\n * Show answer from local_ai_manager.\n * @param {*} text\n * @param {*} sender User or Ai\n * @param {*} answer Is answer in history\n */\nconst showMessage = async(text, sender = '', answer = true) => {\n    // Skip if sender is system.\n    if (sender === 'system') {\n        return;\n    }\n    // Imitate bool for message.mustache logic {{#sender}}.\n    if (sender === 'ai') {\n        sender = '';\n    }\n    // Escape chars for immediate rendering.\n    if (!answer) {\n        text = escapeHTML(text);\n    }\n\n    const templateData = {\n        \"sender\": sender,\n        \"content\": text,\n        \"answer\": answer,\n    };\n    // Call the function to load and render our template.\n    const {html, js} = await Templates.renderForPromise('block_ai_chat/message', templateData);\n    Templates.appendNodeContents('.block_ai_chat-output', html, js);\n\n    // Add copy listener for question and reply.\n    helper.attachCopyListenerLast();\n\n    // Scroll the modal content to the bottom.\n    helper.scrollToBottom();\n};\n\n/**\n * Create new / Reset dialog.\n * @param {bool} deleted\n */\nconst newDialog = async(deleted = false) => {\n    if (aiAtWork) {\n        return;\n    }\n    // Add current convo local representation, if not already there.\n    if (allConversations.find(x => x.id === conversation.id) === undefined && !deleted) {\n        allConversations.push(conversation);\n    }\n    // Reset local conservation.\n    conversation = {\n        id: 0,\n        messages: [],\n    };\n    clearMessages();\n    setModalHeader(strNewDialog);\n    helper.focustextarea();\n};\n\n/**\n * Delete /hide current dialog.\n */\nconst deleteCurrentDialog = () => {\n    deleteCancelPromise(\n        getString('delete', 'block_ai_chat'),\n        getString('deletewarning', 'block_ai_chat'),\n    ).then(async() => {\n        if (conversation.id !== 0) {\n            try {\n                const deleted = await externalServices.deleteConversation(contextid, userid, conversation.id);\n                if (deleted) {\n                    removeFromHistory();\n                    await showConversation();\n                }\n            } catch (error) {\n                displayException(error);\n            }\n        }\n        return;\n    }).catch(() => {\n        return;\n    });\n};\n\n/**\n * Show conversation history.\n */\nconst showHistory = async() => {\n    // Add current convo local representation, if not already there.\n    if (allConversations.find(x => x.id === conversation.id) === undefined) {\n        allConversations.push(conversation);\n    }\n    // Change title and add backlink.\n    let title = '<a href=\"#\" id=\"block_ai_chat_backlink\"><i class=\"icon fa fa-arrow-left\"></i>' + strHistory + '</a>';\n    clearMessages(true);\n    setModalHeader(title);\n    const btnBacklink = document.getElementById('block_ai_chat_backlink');\n    btnBacklink.addEventListener('click', async() => {\n        if (conversation.id !== 0) {\n            await showConversation(conversation.id);\n        } else {\n            newDialog();\n        }\n        clearMessages();\n        setModalHeader();\n    });\n\n    // Set modal class to hide info about ratelimits and infobox.\n    let modal = document.querySelector('.block_ai_chat_modal');\n    modal.classList.add('onhistorypage');\n\n    // Iterate over conversations and group by date.\n    let groupedByDate = {};\n    allConversations.forEach((convo) => {\n        if (typeof convo.messages[1] !== 'undefined') {\n            // Get first prompt.\n            let title = convo.messages[1].message;\n\n            // Get date and sort convos into a date array.\n            const now = new Date();\n            const date = new Date(convo.timecreated * 1000);\n            const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());\n            const yesterday = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1);\n            const twoWeeksAgo = new Date(now);\n            twoWeeksAgo.setDate(now.getDate() - 14);\n\n            const options = {weekday: 'long', day: '2-digit', month: '2-digit'};\n            const monthOptions = {month: 'long', year: '2-digit'};\n\n            // Create a date string.\n            let dateString = '';\n            if (date >= today) {\n                dateString = strToday;\n            } else if (date >= yesterday) {\n                dateString = strYesterday;\n            } else if (date >= twoWeeksAgo) {\n                dateString = date.toLocaleDateString(undefined, options);\n            } else {\n                dateString = date.toLocaleDateString(undefined, monthOptions);\n            }\n\n            // Create a time string.\n            const hours = date.getHours();\n            const minutes = date.getMinutes().toString().padStart(2, '0');\n\n            let convItem = {\n                \"title\": title,\n                \"conversationid\": convo.id,\n                \"time\": hours + ':' + minutes,\n            };\n\n            // Save entry under the date.\n            if (!groupedByDate[dateString]) {\n                groupedByDate[dateString] = [];\n            }\n            groupedByDate[dateString].push(convItem);\n        }\n    });\n\n    // Convert the grouped objects into an array format that Mustache can iterate over.\n    let convert = {\n        groups: Object.keys(groupedByDate).map(key => ({\n            key: key,\n            objects: groupedByDate[key]\n        }))\n    };\n\n    // Render history.\n    const templateData = {\n        \"dates\": convert.groups,\n    };\n    const {html, js} = await Templates.renderForPromise('block_ai_chat/history', templateData);\n    Templates.appendNodeContents('.block_ai_chat_modal .block_ai_chat-output', html, js);\n\n    // Add a listener for the new dialog button.\n    const btnNewDialog = document.getElementById('ai_chat_history_new_dialog');\n    btnNewDialog.addEventListener('mousedown', () => {\n        newDialog();\n    });\n};\n\n/**\n * Remove currrent conversation from history.\n */\nconst removeFromHistory = () => {\n    // Cant remove if new or not yet in history.\n    if (conversation.id !== 0 && allConversations.find(x => x.id === conversation.id) !== undefined) {\n        // Build new allConversations array without deleted one.\n        allConversations = allConversations.filter(obj => obj.id !== conversation.id);\n    }\n};\n\n/**\n * Webservice Save conversation.\n * @param {*} question\n * @param {*} reply\n */\nconst saveConversationLocally = (question, reply) => {\n    // Add to local representation.\n    let message = {'message': question, 'sender': 'user'};\n    conversation.messages.push(message);\n    message = {'message': reply, 'sender': 'ai'};\n    conversation.messages.push(message);\n};\n\n/**\n * Clear output div.\n * @param {*} hideinput\n */\nconst clearMessages = (hideinput = false) => {\n    const output = document.querySelector('.block_ai_chat-output');\n    output.innerHTML = '';\n    // For showing history.\n    let input = document.querySelector('.block_ai_chat-input');\n    if (hideinput) {\n        input.style.display = 'none';\n    } else {\n        input.style.display = 'flex';\n    }\n};\n\n/**\n * Set modal header title.\n * @param {*} setTitle\n */\nconst setModalHeader = (setTitle = '') => {\n    let modalheader = document.querySelector('.block_ai_chat_modal .modal-title div');\n    let title = '';\n    if (modalheader !== null && (conversation.messages.length > 0 || setTitle.length)) {\n        if (!setTitle.length) {\n            title = conversation.messages[1].message;\n        } else {\n            title = setTitle;\n        }\n        modalheader.innerHTML = title;\n    }\n    // Remove onhistorypage, since history page is setting it.\n    let modal = document.querySelector('.block_ai_chat_modal');\n    modal.classList.remove('onhistorypage');\n};\n\n/**\n * Attach event listener.\n * @param {*} textarea\n */\nconst addTextareaListener = (textarea) => {\n    textarea.addEventListener('keydown', (event) => {\n        // Handle submission.\n        textareaOnKeydown(event);\n\n        // Handle autgrow.\n        // Reset the height to auto to get the correct scrollHeight.\n        textarea.style.height = 'auto';\n\n        // Fetch the computed styles.\n        const computedStyles = window.getComputedStyle(textarea);\n        const lineHeight = parseFloat(computedStyles.lineHeight);\n        const paddingTop = parseFloat(computedStyles.paddingTop);\n        const paddingBottom = parseFloat(computedStyles.paddingBottom);\n        const borderTop = parseFloat(computedStyles.borderTopWidth);\n        const borderBottom = parseFloat(computedStyles.borderBottomWidth);\n\n        // Calculate the maximum height for four rows plus padding and borders.\n        const maxHeight = (lineHeight * 4) + paddingTop + paddingBottom + borderTop + borderBottom;\n\n        // Calculate the new height based on the scrollHeight.\n        const newHeight = Math.min(textarea.scrollHeight + borderTop + borderBottom, maxHeight);\n\n        // Set the new height.\n        textarea.style.height = newHeight + 'px';\n    });\n};\n\n/**\n * Action for textarea submission.\n * @param {*} event\n */\nconst textareaOnKeydown = (event) => {\n    if (event.key === 'Enter' && !aiAtWork && !event.shiftKey) {\n        aiAtWork = true;\n        enterQuestion(event.target.value);\n        event.preventDefault();\n        event.target.value = '';\n    }\n};\n\n/**\n * Submit form.\n */\nconst clickSubmitButton = () => {\n    // Var aiAtWork to make it impossible to submit multiple questions at once.\n    if (!aiAtWork) {\n        aiAtWork = true;\n        const textarea = document.getElementById('block_ai_chat-input-id');\n        enterQuestion(textarea.value);\n        textarea.value = '';\n    }\n};\n\n/**\n * Handle error from local_ai_manager.\n * @param {*} requestresult\n * @param {*} question\n * @param {*} contextid\n * @param {*} options\n * @returns {object}\n */\nconst errorHandling = async(requestresult, question, contextid, options) => {\n\n    // If code 409, conversationid is already taken, try get new a one.\n    if (requestresult.code == 409) {\n        while (requestresult.code == 409) {\n            try {\n                let idresult = await externalServices.getNewConversationId(contextid);\n                conversation.id = idresult.id;\n                options.itemid = conversation.id;\n            } catch (error) {\n                displayException(error);\n            }\n            // Retry with new id.\n            requestresult = await manager.askLocalAiManager('chat', question, contextid, options);\n            return requestresult;\n        }\n    }\n\n    // If any other errorcode, alert with errormessage.\n    const errorString = await getString('errorwithcode', 'block_ai_chat', requestresult.code);\n    const result = JSON.parse(requestresult.result);\n    await displayAlert(errorString, result.message);\n\n    // Change answer styling to differentiate from ai.\n    const answerdivs = document.querySelectorAll('.awaitanswer');\n    const answerdiv = answerdivs[answerdivs.length - 1];\n    const messagediv = answerdiv.closest('.message');\n    messagediv.classList.add('text-danger');\n\n    // And write generic error message in chatbot.\n    requestresult.result = await getString('error', 'block_ai_chat');\n    return requestresult;\n};\n\n/**\n * Check historic messages for max length.\n * @param {array} messages\n * @returns {array}\n */\nconst checkMessageHistoryLengthLimit = async(messages) => {\n    const length = messages.length;\n    if (length > maxHistory) {\n        // Cut history.\n        let shortenedMessages = [messages[0], ...messages.slice(-maxHistory)];\n\n        // Show warning once per session.\n        if (!maxHistoryWarnings.has(conversation.id)) {\n            const maxHistoryString = await getString('maxhistory', 'block_ai_chat', maxHistory);\n            const warningErrorString = await getString('maxhistoryreached', 'block_ai_chat', maxHistory);\n            await displayAlert(maxHistoryString, warningErrorString);\n            // Remember warning.\n            maxHistoryWarnings.add(conversation.id);\n        }\n        return shortenedMessages;\n    }\n    // Limit not reached, return messages.\n    return messages;\n};\n\n/**\n * Check if modal should close on outside click.\n * @param {*} event\n */\nconst checkOutsideClick = (event) => {\n    // View openfull acts like a normal modal.\n    if (viewmode != VIEW_OPENFULL) {\n        event.preventDefault();\n    }\n};\n\n/**\n * Set different viewmodes and save in local storage.\n * @param {string} mode\n */\nconst setView = async(mode = '') => {\n    const key = await hash('chatmode' + userid);\n    // Check for saved viewmode.\n    let savedmode = LocalStorage.get(key);\n    if (mode == '') {\n        if (!savedmode) {\n            // Set default.\n            mode = VIEW_CHATWINDOW;\n        } else {\n            mode = savedmode;\n        }\n    }\n    // Save viewmode and set global var.\n    LocalStorage.set(key, mode);\n    viewmode = mode;\n\n    // Set viewmode as bodyclass.\n    const body = document.querySelector('body');\n    body.classList.remove(VIEW_CHATWINDOW, VIEW_OPENFULL, VIEW_DOCKRIGHT);\n    body.classList.add(mode);\n};\n\n/**\n * Is user allowed new queries.\n * @returns {message}\n */\nconst userAllowed = async() => {\n    let message;\n    if (tenantConfig.tenantenabled === false) {\n        message = await getString('error_http403disabled', 'local_ai_manager');\n        return message;\n    }\n    if (tenantConfig.userconfirmed === false) {\n        message = await getString('error_http403notconfirmed', 'local_ai_manager');\n        message += \". \";\n        const link = window.location.origin + '/local/ai_manager/confirm_ai_usage.php';\n        message += await getString('confirm_ai_usage', 'block_ai_chat', link);\n        return message;\n    }\n    if (tenantConfig.userlocked === true) {\n        message = await getString('error_http403blocked', 'local_ai_manager');\n        return message;\n    }\n    if (chatConfig.isconfigured === false) {\n        message = await getString('error_purposenotconfigured', 'local_ai_manager');\n        return message;\n    }\n    if (chatConfig.lockedforrole === true) {\n        message = await getString('error_http403blocked', 'local_ai_manager');\n        return message;\n    }\n    if (chatConfig.limitreached === true) {\n        message = await getString('error_limitreached', 'local_ai_manager');\n        return message;\n    }\n    return '';\n};\n\n/**\n * Change to openfull view when screen is small.\n * @param {*} e\n */\nconst handleScreenWidthChange = (e) => {\n    const body = document.querySelector('body');\n    if (e.matches) {\n        // Screen width is less than 576px\n        body.classList.remove(VIEW_CHATWINDOW, VIEW_OPENFULL, VIEW_DOCKRIGHT);\n        body.classList.add(VIEW_OPENFULL);\n    } else {\n        body.classList.remove(VIEW_CHATWINDOW, VIEW_OPENFULL, VIEW_DOCKRIGHT);\n        body.classList.add(viewmode);\n    }\n};\n\n/**\n * Show personas modal.\n */\nconst showPersonasModal = () => {\n    // Add a dynamic form to add a systemprompt/persona to a block instance.\n    // Always create the dynamic form modal, since it is being destroyed.\n    personaForm = new ModalForm({\n        formClass: \"block_ai_chat\\\\form\\\\persona_form\",\n        moduleName: \"block_ai_chat/modal_save_delete_cancel\",\n        args: {\n            contextid: contextid,\n        },\n        modalConfig: {\n            title: strDefinePersona,\n        },\n    });\n\n    // Show modal.\n    personaForm.show();\n\n    // If select[template] is changed, change textarea[prompt].\n    // For this, we want to get the value of the hidden input with name=\"prompts\".\n    // So we wait for the modalForm() to be LOADED to get the modal object.\n    // On the modal object we wait for the bodyRendered event to read the input.\n    personaForm.addEventListener(personaForm.events.LOADED, () => {\n        personaForm.modal.getRoot().on(ModalEvents.bodyRendered, () => {\n            const inputprompts = document.querySelector('input[name=\"prompts\"]');\n            const prompts = JSON.parse(inputprompts.value);\n            const select = document.querySelector('select[name=\"template\"]');\n            const addpersona = document.querySelector('#add_persona');\n            const copypersona = document.querySelector('#copy_persona');\n            personaNewname = document.querySelector('input[name=\"name\"]');\n            personaInputprompt = document.querySelector('textarea[name=\"prompt\"]');\n            personaUserinfo = document.querySelector('textarea[name=\"userinfo\"]');\n            const inputtemplateids = document.querySelector('input[name=\"templateids\"]');\n            const templateids = JSON.parse(inputtemplateids.value);\n            const inputuserinfos = document.querySelector('input[name=\"userinfos\"]');\n            const userinfos = JSON.parse(inputuserinfos.value);\n            personaButtondelete = document.querySelector('[data-custom=\"delete\"]');\n            systemTemplateHiddenInput = document.querySelector('[data-type=\"systemtemplate\"]');\n\n            personaNewname.value = personaNewname.value.trim();\n\n            // Sort personal templates to the end, so we can make two categories in the dropdown.\n            select.options.forEach((option) => {\n                if (!templateids.map(id => parseInt(id)).includes(parseInt(option.value)) && parseInt(option.value) !== 0) {\n                    select.options[select.options.length - 1].after(option);\n                }\n            });\n\n            // Disable delete/name on system templates.\n            manageInputs(false, templateids, select.value);\n\n            // Now we can add a listener to reflect select[template] to textarea[prompt].\n            select.addEventListener('change', (event) => {\n                let selectValue = event.target.value;\n                let selectText = event.target.options[select.selectedIndex].text.trim();\n                // Enable all.\n                manageInputs(true);\n\n                // Reflect prompt, name and userinfos.\n                if (typeof prompts[selectValue] !== 'undefined') {\n                    personaInputprompt.value = prompts[selectValue];\n                    // For personaNewname, get_formdata needs setAttribute,\n                    // but .value is used to repopulate after placeholder is used.\n                    personaNewname.value = selectText;\n                    personaNewname.setAttribute('placeholder', '');\n                    personaNewname.setAttribute('value', selectText);\n                    personaUserinfo.value = userinfos[selectValue];\n                    personaUserinfo.disabled = false;\n                    personaInputprompt.disabled = false;\n                } else {\n                    // Should be selection \"No Persona\"\n                    personaNewname.setAttribute('value', '');\n                    personaInputprompt.value = '';\n                    personaInputprompt.disabled = true;\n                    personaUserinfo.value = '';\n                    personaUserinfo.disabled = true;\n                }\n                // Disable delete/name on system templates.\n                manageInputs(false, templateids, selectValue);\n            });\n\n            // Remove newpersona signifier option on click.\n            select.addEventListener('click', () => {\n                let option = document.querySelector('.new-persona-placeholder');\n                if (option) {\n                    select.removeChild(option);\n                }\n            });\n\n            // Add headlines and spacing to the template select element.\n            // But before adding options make a comparison to check for usertemplates.\n            const useroptions = select.options.length > templateids.length;\n            // Add spacing after \"No persona\".\n            const spacer = new Option('', '', false, false);\n            spacer.disabled = true;\n            spacer.classList.add('select-spacer');\n            select.insertBefore(spacer, select.options[1]);\n            // Add systemtemplates heading.\n            const systemtemplates = new Option(strSystemTemplates, '', false, false);\n            systemtemplates.disabled = true;\n            select.insertBefore(systemtemplates, select.options[2]);\n            // // Add usertemplates heading.\n            if (useroptions) {\n                // Get last systemtemplate position.\n                const maxValue = Math.max(...templateids.map(id => parseInt(id)));\n                const lastSystemOption = Array.from(select.options).find(opt => parseInt(opt.value) === maxValue);\n                // Add heading.\n                const usertemplates = new Option(strUserTemplates, '', false, false);\n                usertemplates.disabled = true;\n                select.insertBefore(usertemplates, lastSystemOption.nextSibling);\n            }\n\n            // Add listener to addPersona icon.\n            addpersona.addEventListener('click', () => {\n                addPersona(false, select);\n            });\n\n            // Add listener to copyPersona icon.\n            copypersona.addEventListener('click', () => {\n                addPersona(true, select);\n            });\n\n            // To use process_dynamic_submission() for deletion, we use a save button but add a delete hidden input.\n            // Make sure it is set 1 on deletion and to 0 on actual saving process.\n            const actionbuttons = document.querySelectorAll('[data-action=\"save\"]');\n            actionbuttons.forEach((button) => {\n                button.addEventListener('click', async(e) => {\n                    const deleteinput = document.querySelector('input[name=\"delete\"]');\n                    if (e.target.dataset.custom == 'delete') {\n                        deleteinput.value = '1';\n                        if (e.target.dataset.confirmed !== \"1\") {\n                            e.stopPropagation();\n                            await confirmModal(getString('delete', 'core'),\n                                getString('areyousuredelete', 'block_ai_chat'),\n                                getString('delete', 'core'),\n                                null,\n                                () => {\n                                    e.target.dataset.confirmed = \"1\";\n                                    e.target.click();\n                                },\n                                null\n                            );\n                        }\n                    } else {\n                        deleteinput.value = '0';\n                        if (select.value == \"\" && isAdmin && e.target.dataset.confirmed !== \"1\") {\n                            e.stopPropagation();\n                            const modal = await SaveCancelModal.create({\n                                title: getString('systemorpersonal_title', 'block_ai_chat'),\n                                body: getString('systemorpersonal_question', 'block_ai_chat'),\n                                buttons: {\n                                    save: getString('systemtemplate', 'block_ai_chat'),\n                                    cancel: getString('personaltemplate', 'block_ai_chat')\n                                },\n                                removeOnClose: true,\n                                show: true\n                            });\n                            modal.getRoot().on(ModalEvents.save,\n                                () => {\n                                    systemTemplateHiddenInput.value = 1;\n                                    e.target.dataset.confirmed = \"1\";\n                                    e.target.click();\n                                }\n                            );\n                            modal.getRoot().on(ModalEvents.cancel,\n                                () => {\n                                    systemTemplateHiddenInput.value = 0;\n                                    e.target.dataset.confirmed = \"1\";\n                                    e.target.click();\n                                }\n                            );\n                        }\n                    }\n                });\n            });\n        });\n    });\n\n    // Enable admintemplate name input on save.\n    personaForm.addEventListener(personaForm.events.SUBMIT_BUTTON_PRESSED, () => {\n        manageInputs(true);\n    });\n\n\n    // Also enable admintemplate name input on error.\n    personaForm.addEventListener(personaForm.events.SERVER_VALIDATION_ERROR, () => {\n        manageInputs(true);\n    });\n\n    // Reload persona and rewrite info on submission.\n    personaForm.addEventListener(personaForm.events.FORM_SUBMITTED, async() => {\n        let reply = await externalServices.reloadPersona(contextid);\n        personaPrompt = reply.prompt;\n        personaInfo = reply.info;\n        showUserinfo(false);\n    });\n};\n\n\n/**\n * Show options modal.\n */\nconst showOptionsModal = () => {\n    // Add a dynamic form to add options.\n    // Always create the dynamic form modal, since it is being destroyed.\n    optionsForm = new ModalForm({\n        formClass: \"block_ai_chat\\\\form\\\\options_form\",\n        moduleName: \"core/modal_save_cancel\",\n        args: {\n            contextid: contextid,\n        },\n        modalConfig: {\n            title: getString('options'),\n        },\n    });\n\n    // Show modal.\n    optionsForm.show();\n};\n\n/**\n * Click on add new persona, make input writable and reset if no copy.\n * @param {bool} copy\n * @param {HTMLElement} select\n */\nconst addPersona = (copy, select) => {\n    // Enable inputs and set a placeholder.\n    personaNewname.disabled = false;\n    personaNewname.placeholder = strNewPersona;\n    personaNewname.value = '';\n    personaInputprompt.disabled = false;\n    personaUserinfo.disabled = false;\n    if (!copy) {\n        personaInputprompt.value = '';\n        personaUserinfo.value = '';\n    }\n    // Add option to signify new persona.\n    let option = document.querySelector('.new-persona-placeholder');\n    if (!option) {\n        let signifierOption = new Option(strNewPersona, '', true, true);\n        signifierOption.classList.add('new-persona-placeholder');\n        select.add(signifierOption);\n    }\n};\n\nconst manageInputs = (switchon, templateids = [], selectValue = 42) => {\n    // Switch all inputs on.\n    if ((switchon || isAdmin) && selectValue != 0) {\n        // Enable everything except for \"No persona\".\n        personaNewname.disabled = false;\n        personaButtondelete.disabled = false;\n        personaInputprompt.disabled = false;\n        personaUserinfo.disabled = false;\n        return;\n    }\n    // Abort on reload if validation failed.\n    if (document.querySelector('.is-invalid') !== null) {\n        return;\n    }\n    // Switch input between admin and user templates.\n    if (templateids.includes(selectValue) || selectValue == 0) {\n        personaNewname.disabled = true;\n        personaButtondelete.disabled = true;\n        personaInputprompt.disabled = true;\n        personaUserinfo.disabled = true;\n    } else {\n        personaNewname.disabled = false;\n        personaButtondelete.disabled = false;\n        personaInputprompt.disabled = false;\n        personaUserinfo.disabled = false;\n    }\n};\n\nconst showUserinfo = async(first) => {\n    // If persona is updated, delete current infobox.\n    if (!first) {\n        const toDelete = document.querySelector('.local_ai_manager-infobox.alert.alert-info');\n        if (toDelete) {\n            toDelete.remove();\n        }\n    }\n\n    if (personaInfo.trim() != '') {\n        const targetElement = document.querySelector('.block_ai_chat_modal_body .infobox');\n        const templateContext = {\n            'persona': personaInfo,\n            'personainfourl': personaLink,\n        };\n        const {html, js} = await Templates.renderForPromise('block_ai_chat/persona_infobox', templateContext);\n        Templates.appendNodeContents(targetElement, html, js);\n    }\n};\n"], "names": ["VIEW_CHATWINDOW", "VIEW_OPENFULL", "VIEW_DOCKRIGHT", "strHistory", "strNewDialog", "str<PERSON><PERSON><PERSON>", "strYesterday", "strDefinePersona", "strNew<PERSON><PERSON><PERSON>", "strUserTemplates", "strSystemTemplates", "badge", "viewmode", "modal", "personaForm", "persona<PERSON>rompt", "personaInfo", "personaLink", "personaNewname", "personaButtondelete", "personaUserinfo", "personaInputprompt", "systemTemplateHiddenInput", "showPersona", "optionsForm", "showOptions", "isAdmin", "modalopen", "conversation", "id", "messages", "allConversations", "userid", "contextid", "firstLoad", "aiAtWork", "maxHist<PERSON>", "max<PERSON><PERSON><PERSON><PERSON><PERSON>nings", "Set", "tenantConfig", "chatConfig", "DialogModal", "Modal", "configure", "modalConfig", "show", "removeOnClose", "isVerticallyCentered", "titletest", "setTitletest", "value", "hide", "document", "querySelector", "classList", "remove", "async", "params", "new", "history", "persona", "new<PERSON>a", "usertemplates", "systemtemplates", "personaprompt", "personainfo", "showpersona", "showoptions", "personalink", "isadmin", "aiConfig", "purposes", "find", "p", "purpose", "create", "templateContext", "title", "getRoot", "on", "e", "target", "add", "ModalEvents", "outsideClick", "event", "checkOutsideClick", "<PERSON><PERSON><PERSON><PERSON>", "getElementById", "addEventListener", "textarea", "addTextareaListener", "clickSubmitButton", "getConversations", "showConversation", "reply", "externalServices", "getConversationcontextLimit", "limit", "newDialog", "deleteCurrentDialog", "showHistory", "btnDefinePersona", "showPersonasModal", "btnOptions", "showOptionsModal", "warningBoxSelector", "showUserinfo", "message", "userAllowed", "notice", "aiUtilsButton", "uniqid", "Math", "random", "toString", "slice", "TinyAiUtils", "init", "TinyAiConstants", "modalModes", "standalone", "selectionObject", "window", "getSelection", "range", "getRangeAt", "container", "createElement", "append<PERSON><PERSON><PERSON>", "cloneContents", "images", "querySelectorAll", "length", "src", "image", "fetchResult", "fetch", "data", "blob", "getDatamanager", "setSelectionImg", "setSelection", "editor<PERSON><PERSON><PERSON>", "TinyAiEditorUtils", "setEditorUtils", "displayDialogue", "helper", "focustextarea", "showModal", "matchMedia", "handleScreenWidthChange", "innerWidth", "getAllConversations", "error", "x", "at", "clearMessages", "setModalHeader", "showMessages", "renderMathjax", "enterQuestion", "question", "showMessage", "convHistory", "checkMessageHistoryLengthLimit", "trim", "shift", "options", "<PERSON><PERSON><PERSON>", "getNewConversationId", "timecreated", "floor", "Date", "now", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "itemid", "<PERSON><PERSON>ult", "manager", "askLocalAiManager", "code", "errorHandling", "copy", "copyToClipboard", "showReply", "result", "saveConversationLocally", "innerHTML", "fields", "field", "text", "awaitdivs", "scrollHeight", "clientHeight", "item", "sender", "answer", "templateData", "html", "js", "Templates", "renderForPromise", "appendNodeContents", "attachCopyListenerLast", "scrollToBottom", "deleted", "undefined", "push", "then", "deleteConversation", "removeFromHistory", "catch", "groupedByDate", "for<PERSON>ach", "convo", "date", "today", "getFullYear", "getMonth", "getDate", "yesterday", "twoWeeksAgo", "setDate", "weekday", "day", "month", "monthOptions", "year", "dateString", "toLocaleDateString", "hours", "getHours", "minutes", "getMinutes", "padStart", "convItem", "groups", "Object", "keys", "map", "key", "objects", "filter", "obj", "hideinput", "output", "input", "style", "display", "setTitle", "modalheader", "textareaOnKeydown", "height", "computedStyles", "getComputedStyle", "lineHeight", "parseFloat", "paddingTop", "paddingBottom", "borderTop", "borderTopWidth", "borderBottom", "borderBottomWidth", "maxHeight", "newHeight", "min", "shift<PERSON>ey", "preventDefault", "errorString", "JSON", "parse", "answerdivs", "closest", "shortenedMessages", "has", "maxHistoryString", "warningErrorString", "mode", "savedmode", "LocalStorage", "get", "set", "body", "tenantenabled", "userconfirmed", "link", "location", "origin", "userlocked", "isconfigured", "<PERSON><PERSON><PERSON><PERSON>", "limitreached", "matches", "ModalForm", "formClass", "moduleName", "args", "events", "LOADED", "bodyRendered", "inputprompts", "prompts", "select", "addpersona", "copypersona", "inputtemplateids", "templateids", "inputuserinfos", "userinfos", "option", "parseInt", "includes", "after", "manageInputs", "selectValue", "selectText", "selectedIndex", "setAttribute", "disabled", "<PERSON><PERSON><PERSON><PERSON>", "useroptions", "spacer", "Option", "insertBefore", "maxValue", "max", "lastSystemOption", "Array", "from", "opt", "nextS<PERSON>ling", "add<PERSON>ersona", "button", "deleteinput", "dataset", "custom", "confirmed", "stopPropagation", "click", "SaveCancelModal", "buttons", "save", "cancel", "SUBMIT_BUTTON_PRESSED", "SERVER_VALIDATION_ERROR", "FORM_SUBMITTED", "reload<PERSON><PERSON>a", "prompt", "info", "placeholder", "signifierOption", "switchon", "first", "toDelete", "targetElement"], "mappings": "m8EAqCMA,gBAAkB,2BAClBC,cAAgB,yBAChBC,eAAiB,8BAKnBC,WACAC,aACAC,SACAC,aACAC,iBACAC,cACAC,iBACAC,mBAcAC,MACAC,SAvBAC,MAAQ,GASRC,YAAc,GACdC,cAAgB,GAChBC,YAAc,GACdC,YAAc,GACdC,eAAiB,GACjBC,oBAAsB,GACtBC,gBAAkB,GAClBC,mBAAqB,GACrBC,0BAA4B,GAC5BC,aAAc,EACdC,YAAc,GACdC,aAAc,EACdC,SAAU,EAGVC,WAAY,EAGZC,aAAe,CACfC,GAAI,EACJC,SAAU,IAGVC,iBAAmB,GAEnBC,OAAS,EAETC,UAAY,EAEZC,WAAY,EAEZC,UAAW,EAEXC,WAAa,EAEbC,mBAAqB,IAAIC,IAEzBC,aAAe,GACfC,WAAa,SAEXC,oBAAoBC,eAItBC,UAAUC,aAENA,YAAYC,MAAO,EAGnBD,YAAYE,eAAgB,EAE5BF,YAAYG,sBAAuB,QAE7BJ,UAAUC,aAGZA,YAAYI,gBACPC,aAAaL,YAAYI,WAItCC,aAAaC,YACJF,UAAYE,MAGrBC,aACUA,OAENxB,WAAY,EACCyB,SAASC,cAAc,QAC/BC,UAAUC,OAlFJ,uCAoDbd,mBACY,8CADZA,uBAEgB,4CAgCFe,MAAAA,SAEhBxB,OAASyB,OAAOzB,OAChBC,UAAYwB,OAAOxB,UACnB7B,aAAeqD,OAAOC,IACtBvD,WAAasD,OAAOE,QACpBpD,iBAAmBkD,OAAOG,QAC1BpD,cAAgBiD,OAAOI,WACvBpD,iBAAmBgD,OAAOK,cAC1BpD,mBAAqB+C,OAAOM,gBAC5BhD,cAAgB0C,OAAOO,cACvBhD,YAAcyC,OAAOQ,YACrB1C,YAAckC,OAAOS,YACrBzC,YAAcgC,OAAOU,YACrBlD,YAAcwC,OAAOW,YACrB1C,QAAU+B,OAAOY,QACjB1D,MAAQ8C,OAAO9C,MAEfA,OAAQ,QAGF2D,eAAiB,yBACvB/B,aAAe+B,SACf9B,WAAa8B,SAASC,SAASC,MAAKC,GAAmB,SAAdA,EAAEC,UAG3C7D,YAAc4B,YAAYkC,OAAO,CAC7BC,gBAAiB,CACbC,MAAOzE,aACPO,MAXA,MAYAY,YAAaA,YACbE,YAAaA,eAKrBZ,MAAMiE,UAAUC,GAAG,eAAe,SAASC,GACvCA,EAAEC,OAAO3B,UAAU4B,IAAI,0BAI3BrE,MAAMiE,UAAUC,GAAGI,sBAAYC,cAAcC,QACzCC,kBAAkBD,UAItBE,UAGanC,SAASoC,eAAe,kBAC9BC,iBAAiB,aAAajC,+BAyBjC7B,sBACAd,MAAMsC,aAKJtC,MAAMgC,OACZlB,WAAY,EACCyB,SAASC,cAAc,QAC/BC,UAAU4B,IA1KA,4BA6KTQ,SAAWtC,SAASoC,eAAe,0BACzCG,oBAAoBD,aACLtC,SAASoC,eAAe,2BAChCC,iBAAiB,SAAUJ,QAC9BO,kBAAkBP,UAGlBnD,UAAW,OAEL2D,yBAGAC,uBAGFC,YAAcC,iBAAiBC,4BAA4BhE,WAC/DG,WAAa2D,MAAMG,MAIE9C,SAASoC,eAAe,4BAChCC,iBAAiB,SAAS,KACnCU,eAEoB/C,SAASoC,eAAe,+BAChCC,iBAAiB,SAAS,KACtCW,yBAEmBhD,SAASoC,eAAe,8BAChCC,iBAAiB,SAAS,KACrCY,uBAEEC,iBAAmBlD,SAASoC,eAAe,gCAC7Cc,kBACAA,iBAAiBb,iBAAiB,SAASjC,UACnC9B,cACM,0BACF,kBAAU,SAAU,kBACpB,kBAAU,4BAA6B,kBACvC,kBAAU,UAAW,QACrB,KACA6E,kBACA,YAGEA,6BAIZC,WAAapD,SAASoC,eAAe,yBACvCgB,YACAA,WAAWf,iBAAiB,SAAS,KACjCgB,sBAIcrD,SAASoC,eAAexF,iBAChCyF,iBAAiB,SAAS,KACpCF,QAAQvF,oBAESoD,SAASoC,eAAevF,eAChCwF,iBAAiB,SAAS,KACnCF,QAAQtF,kBAESmD,SAASoC,eAAetF,gBAChCuF,iBAAiB,SAAS,KACnCF,QAAQrF,yBAIN,8BAAgB,2BAA4B,CAAC,eAE7C,0BACF,gBAAiB8B,OAAQ,sEAAuE,CAAC,eAG/F0E,mBAAqB,+BACvBtD,SAASC,cAAcqD,2BACjB,gCAAiBA,oBAGL,KAAlB3F,eACA4F,cAAa,SAIXC,cAAgBC,iBACN,KAAZD,QAAgB,OACVE,aAAe,kBAAU,SAAU,uBACnC,uBAAaA,OAAQF,eAGzBG,cAAgB3D,SAASC,cAAc,+BACvC2D,OAASC,KAAKC,SAASC,SAAS,IAAIC,MAAM,SAE1CC,YAAYC,KAAKN,OAAQO,qBAAgBC,WAAWC,YAC1DV,cAActB,iBAAiB,SAASjC,gBAE9BkE,gBAAkBC,OAAOC,eACzBC,MAAQH,gBAAgBI,WAAW,GACnCC,UAAY3E,SAAS4E,cAAc,OACzCD,UAAUE,YAAYJ,MAAMK,uBACtBC,OAASJ,UAAUK,iBAAiB,UACtCD,OAAOE,OAAS,GAAKF,OAAO,GAAGG,IAAK,OAE9BC,MAAQJ,OAAO,GAEfK,kBAAoBC,MAAMF,MAAMD,KAChCI,WAAaF,YAAYG,OAC/BtB,YAAYuB,eAAe5B,QAAQ6B,gBAAgBH,MAInDhB,gBAAgBP,YAAcO,gBAAgBP,WAAWkB,OAAS,GAClEhB,YAAYuB,eAAe5B,QAAQ8B,aAAapB,gBAAgBP,kBAG9D4B,YAAc,IAAIC,sBAAkBhC,OAAQ,gBAAiB/E,UAAWD,OAAQ,MACtFqF,YAAY4B,eAAejC,OAAQ+B,mBAC7BA,YAAYG,qBAGtBhH,WAAY,EAGhBiH,OAAOC,gBAjKHC,MAIJhJ,eAAiB,kBAAU,QAAS,QACpCC,mBAAqB,kBAAU,YAAa,iBAGzBqH,OAAO2B,WAAW,sBAG1B7D,iBAAiB,SAAU8D,yBAGlC5B,OAAO6B,YAAc,KACrBjE,QAAQtF,sBAyJV4F,iBAAmBrC,cAEjBzB,uBAAyBiE,iBAAiByD,oBAAoBzH,OAAQC,WACxE,MAAOyH,mCACYA,SAQnB5D,iBAAmBtC,qBAAM3B,0DAAK,EAE5BM,WAIO,IAAPN,GAEAD,aAAeG,iBAAiByC,MAAKmF,GAAKA,EAAE9H,KAAOA,UACb,IAAxBE,iBAAiB,GAE/BH,aAAeG,iBAAiB6H,GAAG7H,iBAAiBsG,OAAS,GAC1B,IAA5BtG,iBAAiBsG,QAExBlC,WAAU,GAEd0D,gBACAC,uBACMC,eACNZ,OAAOa,kBAGX5G,SAAS0C,iBAAmBA,uBAOtBmE,cAAgBzG,MAAAA,cAGF,IAAZ0G,qBACA/H,UAAW,SAGTyE,cAAgBC,iBACN,KAAZD,QAAgB,OACVE,aAAe,kBAAU,oBAAqB,8BAC9C,uBAAaA,OAAQF,cAC3BzE,UAAW,GAKfgI,YAAYD,SAAU,QAAQ,GAI9BtI,aAAaE,SAAS,GAAK,SACZf,qBACD,cAIVqJ,kBAAoBC,+BAA+BzI,aAAaE,UAGvC,KAAzBf,cAAcuJ,QACdF,YAAYG,cAIVC,QAAU,WACC,oCACUJ,gBAIH,IAApBxI,aAAaC,GAAU,SAEf4I,eAAiBzE,iBAAiB0E,qBAAqBzI,WAC3DL,aAAaC,GAAK4I,SAAS5I,GAC3BD,aAAa+I,YAAc1D,KAAK2D,MAAMC,KAAKC,MAAQ,KACnDhB,gBAAe,uBAAWI,WAC5B,MAAOR,mCACYA,OAErBc,QAAQO,gBAAiB,EAI7BP,QAAQQ,OAASpJ,aAAaC,OAG1BoJ,oBAAsBC,QAAQC,kBAAkB,OAAQjB,SAAUjI,UAAWuI,SAGvD,KAAtBS,cAAcG,OACdH,oBAAsBI,cAAcJ,cAAef,SAAUjI,UAAWuI,cAIxEc,KAAOlI,SAASC,cAAc,2CAClCiI,KAAK7F,iBAAiB,aAAa,KAC/B0D,OAAOoC,gBAAgBD,eAIrBE,UAAUP,cAAcQ,QAG9BtC,OAAOa,gBAGP7H,UAAW,EAGe,KAAtB8I,cAAcG,MACdM,wBAAwBxB,SAAUe,cAAcQ,QAIlCrI,SAASoC,eAAe,2BAChCmG,UAAY,kCACN,2BAA4B,CAAC,UAO3CH,UAAYhI,MAAAA,WAEVoI,OAASxI,SAASgF,iBAAiB,iDACjCyD,MAAQD,OAAOA,OAAOvD,OAAS,GAErCwD,MAAMF,UAAYG,KAClBD,MAAMvI,UAAUC,OAAO,aAGnBwI,UAAY3I,SAASgF,iBAAiB,qCACzB2D,UAAUA,UAAU1D,OAAS,GACrC/E,UAAUC,OAAO,qBAGpBwE,UAAY3E,SAASC,cAAc,iCACrCwI,MAAMG,aAAejE,UAAUkE,6CAKjClC,aAAevG,cACZ,MAAM0I,QAAQtK,aAAaE,eACtBqI,YAAY+B,KAAKtF,QAASsF,KAAKC,SAUvChC,YAAc3G,eAAMsI,UAAMK,8DAAS,GAAIC,qEAE1B,WAAXD,cAIW,OAAXA,SACAA,OAAS,IAGRC,SACDN,MAAO,uBAAWA,aAGhBO,aAAe,QACPF,eACCL,YACDM,SAGRE,KAACA,KAADC,GAAOA,UAAYC,mBAAUC,iBAAiB,wBAAyBJ,iCACnEK,mBAAmB,wBAAyBJ,KAAMC,IAG5DpD,OAAOwD,yBAGPxD,OAAOyD,kBAOLzG,UAAY3C,qBAAMqJ,gEAChB1K,gBAIyD2K,IAAzD/K,iBAAiByC,MAAKmF,GAAKA,EAAE9H,KAAOD,aAAaC,MAAsBgL,SACvE9K,iBAAiBgL,KAAKnL,cAG1BA,aAAe,CACXC,GAAI,EACJC,SAAU,IAEd+H,gBACAC,eAAe1J,cACf+I,OAAOC,kBAMLhD,oBAAsB,4CAEpB,kBAAU,SAAU,kBACpB,kBAAU,gBAAiB,kBAC7B4G,MAAKxJ,aACqB,IAApB5B,aAAaC,aAEamE,iBAAiBiH,mBAAmBhL,UAAWD,OAAQJ,aAAaC,MAEtFqL,0BACMpH,oBAEZ,MAAO4D,mCACYA,WAI1ByD,OAAM,UAQP9G,YAAc7C,eAE6CsJ,IAAzD/K,iBAAiByC,MAAKmF,GAAKA,EAAE9H,KAAOD,aAAaC,MACjDE,iBAAiBgL,KAAKnL,kBAGtBiD,MAAQ,gFAAkF1E,WAAa,OAC3G0J,eAAc,GACdC,eAAejF,OACKzB,SAASoC,eAAe,0BAChCC,iBAAiB,SAASjC,UACV,IAApB5B,aAAaC,SACPiE,iBAAiBlE,aAAaC,IAEpCsE,YAEJ0D,gBACAC,oBAIQ1G,SAASC,cAAc,wBAC7BC,UAAU4B,IAAI,qBAGhBkI,cAAgB,GACpBrL,iBAAiBsL,SAASC,gBACW,IAAtBA,MAAMxL,SAAS,GAAoB,KAEtC+C,MAAQyI,MAAMxL,SAAS,GAAG8E,cAGxBkE,IAAM,IAAID,KACV0C,KAAO,IAAI1C,KAAyB,IAApByC,MAAM3C,aACtB6C,MAAQ,IAAI3C,KAAKC,IAAI2C,cAAe3C,IAAI4C,WAAY5C,IAAI6C,WACxDC,UAAY,IAAI/C,KAAKC,IAAI2C,cAAe3C,IAAI4C,WAAY5C,IAAI6C,UAAY,GACxEE,YAAc,IAAIhD,KAAKC,KAC7B+C,YAAYC,QAAQhD,IAAI6C,UAAY,UAE9BnD,QAAU,CAACuD,QAAS,OAAQC,IAAK,UAAWC,MAAO,WACnDC,aAAe,CAACD,MAAO,OAAQE,KAAM,eAGvCC,WAAa,GAEbA,WADAb,MAAQC,MACKnN,SACNkN,MAAQK,UACFtN,aACNiN,MAAQM,YACFN,KAAKc,wBAAmBvB,EAAWtC,SAEnC+C,KAAKc,wBAAmBvB,EAAWoB,oBAI9CI,MAAQf,KAAKgB,WACbC,QAAUjB,KAAKkB,aAAatH,WAAWuH,SAAS,EAAG,SAErDC,SAAW,OACF9J,qBACSyI,MAAMzL,QAChByM,MAAQ,IAAME,SAIrBpB,cAAcgB,cACfhB,cAAcgB,YAAc,IAEhChB,cAAcgB,YAAYrB,KAAK4B,oBAajCtC,aAAe,OARP,CACVuC,OAAQC,OAAOC,KAAK1B,eAAe2B,KAAIC,OACnCA,IAAKA,IACLC,QAAS7B,cAAc4B,UAMVJ,SAEftC,KAACA,KAADC,GAAOA,UAAYC,mBAAUC,iBAAiB,wBAAyBJ,iCACnEK,mBAAmB,6CAA8CJ,KAAMC,IAG5DnJ,SAASoC,eAAe,8BAChCC,iBAAiB,aAAa,KACvCU,gBAOF+G,kBAAoB,KAEE,IAApBtL,aAAaC,SAAqEiL,IAAzD/K,iBAAiByC,MAAKmF,GAAKA,EAAE9H,KAAOD,aAAaC,OAE1EE,iBAAmBA,iBAAiBmN,QAAOC,KAAOA,IAAItN,KAAOD,aAAaC,OAS5E6J,wBAA0B,CAACxB,SAAUnE,aAEnCa,QAAU,SAAYsD,gBAAoB,QAC9CtI,aAAaE,SAASiL,KAAKnG,SAC3BA,QAAU,SAAYb,aAAiB,MACvCnE,aAAaE,SAASiL,KAAKnG,UAOzBiD,cAAgB,eAACuF,wEACbC,OAASjM,SAASC,cAAc,yBACtCgM,OAAO1D,UAAY,OAEf2D,MAAQlM,SAASC,cAAc,wBAE/BiM,MAAMC,MAAMC,QADZJ,UACsB,OAEA,QAQxBtF,eAAiB,eAAC2F,gEAAW,GAC3BC,YAActM,SAASC,cAAc,yCACrCwB,MAAQ,GACQ,OAAhB6K,cAAyB9N,aAAaE,SAASuG,OAAS,GAAKoH,SAASpH,UAIlExD,MAHC4K,SAASpH,OAGFoH,SAFA7N,aAAaE,SAAS,GAAG8E,QAIrC8I,YAAY/D,UAAY9G,WAGxBhE,MAAQuC,SAASC,cAAc,wBACnCxC,MAAMyC,UAAUC,OAAO,kBAOrBoC,oBAAuBD,WACzBA,SAASD,iBAAiB,WAAYJ,QAElCsK,kBAAkBtK,OAIlBK,SAAS6J,MAAMK,OAAS,aAGlBC,eAAiBlI,OAAOmI,iBAAiBpK,UACzCqK,WAAaC,WAAWH,eAAeE,YACvCE,WAAaD,WAAWH,eAAeI,YACvCC,cAAgBF,WAAWH,eAAeK,eAC1CC,UAAYH,WAAWH,eAAeO,gBACtCC,aAAeL,WAAWH,eAAeS,mBAGzCC,UAA0B,EAAbR,WAAkBE,WAAaC,cAAgBC,UAAYE,aAGxEG,UAAYvJ,KAAKwJ,IAAI/K,SAASsG,aAAemE,UAAYE,aAAcE,WAG7E7K,SAAS6J,MAAMK,OAASY,UAAY,SAQtCb,kBAAqBtK,QACL,UAAdA,MAAM2J,KAAoB7M,UAAakD,MAAMqL,WAC7CvO,UAAW,EACX8H,cAAc5E,MAAMJ,OAAO/B,OAC3BmC,MAAMsL,iBACNtL,MAAMJ,OAAO/B,MAAQ,KAOvB0C,kBAAoB,SAEjBzD,SAAU,CACXA,UAAW,QACLuD,SAAWtC,SAASoC,eAAe,0BACzCyE,cAAcvE,SAASxC,OACvBwC,SAASxC,MAAQ,KAYnBmI,cAAgB7H,MAAMyH,cAAef,SAAUjI,UAAWuI,cAGlC,KAAtBS,cAAcG,UACe,KAAtBH,cAAcG,MAAa,SAEtBX,eAAiBzE,iBAAiB0E,qBAAqBzI,WAC3DL,aAAaC,GAAK4I,SAAS5I,GAC3B2I,QAAQQ,OAASpJ,aAAaC,GAChC,MAAO6H,mCACYA,cAGrBuB,oBAAsBC,QAAQC,kBAAkB,OAAQjB,SAAUjI,UAAWuI,eAM/EoG,kBAAoB,kBAAU,gBAAiB,gBAAiB3F,cAAcG,MAC9EK,OAASoF,KAAKC,MAAM7F,cAAcQ,cAClC,uBAAamF,YAAanF,OAAO7E,eAGjCmK,WAAa3N,SAASgF,iBAAiB,uBAC3B2I,WAAWA,WAAW1I,OAAS,GACpB2I,QAAQ,YAC1B1N,UAAU4B,IAAI,eAGzB+F,cAAcQ,aAAe,kBAAU,QAAS,iBACzCR,eAQLZ,+BAAiC7G,MAAAA,cACpB1B,SAASuG,OACXjG,WAAY,KAEjB6O,kBAAoB,CAACnP,SAAS,MAAOA,SAASsF,OAAOhF,iBAGpDC,mBAAmB6O,IAAItP,aAAaC,IAAK,OACpCsP,uBAAyB,kBAAU,aAAc,gBAAiB/O,YAClEgP,yBAA2B,kBAAU,oBAAqB,gBAAiBhP,kBAC3E,uBAAa+O,iBAAkBC,oBAErC/O,mBAAmB6C,IAAItD,aAAaC,WAEjCoP,yBAGJnP,UAOLwD,kBAAqBD,QAEnBzE,UAAYX,eACZoF,MAAMsL,kBAQRpL,QAAU/B,qBAAM6N,4DAAO,SACnBrC,UAAY,iBAAK,WAAahN,YAEhCsP,UAAYC,sBAAaC,IAAIxC,KACrB,IAARqC,OAKIA,KAJCC,WAEMtR,uCAMFyR,IAAIzC,IAAKqC,MACtBzQ,SAAWyQ,WAGLK,KAAOtO,SAASC,cAAc,QACpCqO,KAAKpO,UAAUC,OAAOvD,gBAAiBC,cAAeC,gBACtDwR,KAAKpO,UAAU4B,IAAImM,OAOjBxK,YAAcrD,cACZoD,YAC+B,IAA/BrE,aAAaoP,qBACb/K,cAAgB,kBAAU,wBAAyB,oBAC5CA,YAEwB,IAA/BrE,aAAaqP,cAAyB,CACtChL,cAAgB,kBAAU,4BAA6B,oBACvDA,SAAW,WACLiL,KAAOlK,OAAOmK,SAASC,OAAS,gDACtCnL,eAAiB,kBAAU,mBAAoB,gBAAiBiL,MACzDjL,eAEqB,IAA5BrE,aAAayP,YACbpL,cAAgB,kBAAU,uBAAwB,oBAC3CA,UAEqB,IAA5BpE,WAAWyP,cACXrL,cAAgB,kBAAU,6BAA8B,oBACjDA,UAEsB,IAA7BpE,WAAW0P,eACXtL,cAAgB,kBAAU,uBAAwB,oBAC3CA,UAEqB,IAA5BpE,WAAW2P,cACXvL,cAAgB,kBAAU,qBAAsB,oBACzCA,SAEJ,IAOL2C,wBAA2BvE,UACvB0M,KAAOtO,SAASC,cAAc,QAChC2B,EAAEoN,SAEFV,KAAKpO,UAAUC,OAAOvD,gBAAiBC,cAAeC,gBACtDwR,KAAKpO,UAAU4B,IAAIjF,iBAEnByR,KAAKpO,UAAUC,OAAOvD,gBAAiBC,cAAeC,gBACtDwR,KAAKpO,UAAU4B,IAAItE,YAOrB2F,kBAAoB,KAGtBzF,YAAc,IAAIuR,mBAAU,CACxBC,UAAW,oCACXC,WAAY,yCACZC,KAAM,CACFvQ,UAAWA,WAEfW,YAAa,CACTiC,MAAOtE,oBAKfO,YAAY+B,OAMZ/B,YAAY2E,iBAAiB3E,YAAY2R,OAAOC,QAAQ,KACpD5R,YAAYD,MAAMiE,UAAUC,GAAGI,sBAAYwN,cAAc,WAC/CC,aAAexP,SAASC,cAAc,yBACtCwP,QAAUhC,KAAKC,MAAM8B,aAAa1P,OAClC4P,OAAS1P,SAASC,cAAc,2BAChC0P,WAAa3P,SAASC,cAAc,gBACpC2P,YAAc5P,SAASC,cAAc,iBAC3CnC,eAAiBkC,SAASC,cAAc,sBACxChC,mBAAqB+B,SAASC,cAAc,2BAC5CjC,gBAAkBgC,SAASC,cAAc,mCACnC4P,iBAAmB7P,SAASC,cAAc,6BAC1C6P,YAAcrC,KAAKC,MAAMmC,iBAAiB/P,OAC1CiQ,eAAiB/P,SAASC,cAAc,2BACxC+P,UAAYvC,KAAKC,MAAMqC,eAAejQ,OAC5C/B,oBAAsBiC,SAASC,cAAc,0BAC7C/B,0BAA4B8B,SAASC,cAAc,gCAEnDnC,eAAegC,MAAQhC,eAAegC,MAAMoH,OAG5CwI,OAAOtI,QAAQ6C,SAASgG,SACfH,YAAYnE,KAAIlN,IAAMyR,SAASzR,MAAK0R,SAASD,SAASD,OAAOnQ,SAAsC,IAA3BoQ,SAASD,OAAOnQ,QACzF4P,OAAOtI,QAAQsI,OAAOtI,QAAQnC,OAAS,GAAGmL,MAAMH,WAKxDI,cAAa,EAAOP,YAAaJ,OAAO5P,OAGxC4P,OAAOrN,iBAAiB,UAAWJ,YAC3BqO,YAAcrO,MAAMJ,OAAO/B,MAC3ByQ,WAAatO,MAAMJ,OAAOuF,QAAQsI,OAAOc,eAAe9H,KAAKxB,OAEjEmJ,cAAa,QAGuB,IAAzBZ,QAAQa,cACfrS,mBAAmB6B,MAAQ2P,QAAQa,aAGnCxS,eAAegC,MAAQyQ,WACvBzS,eAAe2S,aAAa,cAAe,IAC3C3S,eAAe2S,aAAa,QAASF,YACrCvS,gBAAgB8B,MAAQkQ,UAAUM,aAClCtS,gBAAgB0S,UAAW,EAC3BzS,mBAAmByS,UAAW,IAG9B5S,eAAe2S,aAAa,QAAS,IACrCxS,mBAAmB6B,MAAQ,GAC3B7B,mBAAmByS,UAAW,EAC9B1S,gBAAgB8B,MAAQ,GACxB9B,gBAAgB0S,UAAW,GAG/BL,cAAa,EAAOP,YAAaQ,gBAIrCZ,OAAOrN,iBAAiB,SAAS,SACzB4N,OAASjQ,SAASC,cAAc,4BAChCgQ,QACAP,OAAOiB,YAAYV,iBAMrBW,YAAclB,OAAOtI,QAAQnC,OAAS6K,YAAY7K,OAElD4L,OAAS,IAAIC,OAAO,GAAI,IAAI,GAAO,GACzCD,OAAOH,UAAW,EAClBG,OAAO3Q,UAAU4B,IAAI,iBACrB4N,OAAOqB,aAAaF,OAAQnB,OAAOtI,QAAQ,UAErCzG,gBAAkB,IAAImQ,OAAOxT,mBAAoB,IAAI,GAAO,MAClEqD,gBAAgB+P,UAAW,EAC3BhB,OAAOqB,aAAapQ,gBAAiB+O,OAAOtI,QAAQ,IAEhDwJ,YAAa,OAEPI,SAAWnN,KAAKoN,OAAOnB,YAAYnE,KAAIlN,IAAMyR,SAASzR,OACtDyS,iBAAmBC,MAAMC,KAAK1B,OAAOtI,SAAShG,MAAKiQ,KAAOnB,SAASmB,IAAIvR,SAAWkR,WAElFtQ,cAAgB,IAAIoQ,OAAOzT,iBAAkB,IAAI,GAAO,GAC9DqD,cAAcgQ,UAAW,EACzBhB,OAAOqB,aAAarQ,cAAewQ,iBAAiBI,aAIxD3B,WAAWtN,iBAAiB,SAAS,KACjCkP,YAAW,EAAO7B,WAItBE,YAAYvN,iBAAiB,SAAS,KAClCkP,YAAW,EAAM7B,WAKC1P,SAASgF,iBAAiB,wBAClCiF,SAASuH,SACnBA,OAAOnP,iBAAiB,SAASjC,MAAAA,UACvBqR,YAAczR,SAASC,cAAc,2BACZ,UAA3B2B,EAAEC,OAAO6P,QAAQC,OACjBF,YAAY3R,MAAQ,IACe,MAA/B8B,EAAEC,OAAO6P,QAAQE,YACjBhQ,EAAEiQ,wBACI,0BAAa,kBAAU,SAAU,SACnC,kBAAU,mBAAoB,kBAC9B,kBAAU,SAAU,QACpB,MACA,KACIjQ,EAAEC,OAAO6P,QAAQE,UAAY,IAC7BhQ,EAAEC,OAAOiQ,UAEb,eAIRL,YAAY3R,MAAQ,IACA,IAAhB4P,OAAO5P,OAAexB,SAA0C,MAA/BsD,EAAEC,OAAO6P,QAAQE,UAAmB,CACrEhQ,EAAEiQ,wBACIpU,YAAcsU,2BAAgBxQ,OAAO,CACvCE,OAAO,kBAAU,yBAA0B,iBAC3C6M,MAAM,kBAAU,4BAA6B,iBAC7C0D,QAAS,CACLC,MAAM,kBAAU,iBAAkB,iBAClCC,QAAQ,kBAAU,mBAAoB,kBAE1CxS,eAAe,EACfD,MAAM,IAEVhC,MAAMiE,UAAUC,GAAGI,sBAAYkQ,MAC3B,KACI/T,0BAA0B4B,MAAQ,EAClC8B,EAAEC,OAAO6P,QAAQE,UAAY,IAC7BhQ,EAAEC,OAAOiQ,WAGjBrU,MAAMiE,UAAUC,GAAGI,sBAAYmQ,QAC3B,KACIhU,0BAA0B4B,MAAQ,EAClC8B,EAAEC,OAAO6P,QAAQE,UAAY,IAC7BhQ,EAAEC,OAAOiQ,wBAWzCpU,YAAY2E,iBAAiB3E,YAAY2R,OAAO8C,uBAAuB,KACnE9B,cAAa,MAKjB3S,YAAY2E,iBAAiB3E,YAAY2R,OAAO+C,yBAAyB,KACrE/B,cAAa,MAIjB3S,YAAY2E,iBAAiB3E,YAAY2R,OAAOgD,gBAAgBjS,cACxDuC,YAAcC,iBAAiB0P,cAAczT,WACjDlB,cAAgBgF,MAAM4P,OACtB3U,YAAc+E,MAAM6P,KACpBjP,cAAa,OAQfF,iBAAmB,KAGrBjF,YAAc,IAAI6Q,mBAAU,CACxBC,UAAW,oCACXC,WAAY,yBACZC,KAAM,CACFvQ,UAAWA,WAEfW,YAAa,CACTiC,OAAO,kBAAU,cAKzBrD,YAAYqB,QAQV8R,WAAa,CAACrJ,KAAMwH,aAEtB5R,eAAe4S,UAAW,EAC1B5S,eAAe2U,YAAcrV,cAC7BU,eAAegC,MAAQ,GACvB7B,mBAAmByS,UAAW,EAC9B1S,gBAAgB0S,UAAW,EACtBxI,OACDjK,mBAAmB6B,MAAQ,GAC3B9B,gBAAgB8B,MAAQ,KAGfE,SAASC,cAAc,4BACvB,KACLyS,gBAAkB,IAAI5B,OAAO1T,cAAe,IAAI,GAAM,GAC1DsV,gBAAgBxS,UAAU4B,IAAI,2BAC9B4N,OAAO5N,IAAI4Q,mBAIbrC,aAAe,SAACsC,cAAU7C,mEAAc,GAAIQ,mEAAc,OAEvDqC,UAAYrU,UAA2B,GAAfgS,mBAEzBxS,eAAe4S,UAAW,EAC1B3S,oBAAoB2S,UAAW,EAC/BzS,mBAAmByS,UAAW,OAC9B1S,gBAAgB0S,UAAW,GAIe,OAA1C1Q,SAASC,cAAc,iBAIvB6P,YAAYK,SAASG,cAA+B,GAAfA,aACrCxS,eAAe4S,UAAW,EAC1B3S,oBAAoB2S,UAAW,EAC/BzS,mBAAmByS,UAAW,EAC9B1S,gBAAgB0S,UAAW,IAE3B5S,eAAe4S,UAAW,EAC1B3S,oBAAoB2S,UAAW,EAC/BzS,mBAAmByS,UAAW,EAC9B1S,gBAAgB0S,UAAW,KAI7BnN,aAAenD,MAAAA,YAEZwS,MAAO,OACFC,SAAW7S,SAASC,cAAc,8CACpC4S,UACAA,SAAS1S,YAIS,IAAtBvC,YAAYsJ,OAAc,OACpB4L,cAAgB9S,SAASC,cAAc,sCACvCuB,gBAAkB,SACT5D,2BACOC,cAEhBqL,KAACA,KAADC,GAAOA,UAAYC,mBAAUC,iBAAiB,gCAAiC7H,oCAC3E8H,mBAAmBwJ,cAAe5J,KAAMC"}