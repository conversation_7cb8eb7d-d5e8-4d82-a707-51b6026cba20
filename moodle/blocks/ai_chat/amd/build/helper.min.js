define("block_ai_chat/helper",["exports","filter_mathjaxloader/loader"],(function(_exports,_loader){Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.scrollToBottom=_exports.renderMathjax=_exports.hash=_exports.focustextarea=_exports.escapeHTML=_exports.copyToClipboard=_exports.attachCopyListenerLast=void 0;const copyToClipboard=element=>{const textElement=element.nextElementSibling,textToCopy=textElement.innerText||textElement.textContent;navigator.clipboard.writeText(textToCopy);const toast=element.previousElementSibling;toast.style.visibility="visible",setTimeout((()=>{toast.style.visibility="hidden"}),750)};_exports.copyToClipboard=copyToClipboard;_exports.attachCopyListenerLast=()=>{const elements=document.querySelectorAll(".block_ai_chat_modal .copy"),lastquestion=elements[elements.length-2];lastquestion&&lastquestion.addEventListener("click",(function(){copyToClipboard(lastquestion)}));const lastanswer=elements[elements.length-1];lastanswer&&lastanswer.addEventListener("click",(function(){copyToClipboard(lastanswer)}))};_exports.focustextarea=()=>{document.getElementById("block_ai_chat-input-id").focus()};_exports.scrollToBottom=()=>{const modalContent=document.querySelector(".block_ai_chat_modal .modal-body .block_ai_chat-output-wrapper");modalContent.scrollTop=modalContent.scrollHeight};_exports.escapeHTML=str=>{if(null==str)return"";const escapeMap={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","`":"&#x60;","/":"&#x2F;"};return String(str).replace(/[&<>"'`/]/g,(function(match){return escapeMap[match]}))};_exports.hash=async stringToHash=>{const data=(new TextEncoder).encode(stringToHash),hashAsArrayBuffer=await window.crypto.subtle.digest("SHA-256",data),uint8ViewOfHash=new Uint8Array(hashAsArrayBuffer);return Array.from(uint8ViewOfHash).map((b=>b.toString(16).padStart(2,"0"))).join("")};_exports.renderMathjax=()=>{if(void 0!==window.MathJax){window.MathJax.Hub.Config({tex2jax:{inlineMath:[["$","$"],["\\(","\\)"]],displayMath:[["$$","$$"],["\\[","\\]"]]}});const content=document.querySelector(".block_ai_chat-output");content&&(window.MathJax.Hub.Queue(["Typeset",window.MathJax.Hub,content]),(0,_loader.typeset)(content))}}}));

//# sourceMappingURL=helper.min.js.map