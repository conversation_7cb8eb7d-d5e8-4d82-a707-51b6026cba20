{"version": 3, "file": "webservices.min.js", "sources": ["../src/webservices.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// <PERSON><PERSON>le is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\nimport {call as fetchMany} from 'core/ajax';\n\n/**\n * Get all converstations a User can see.\n * @param {int} userid\n * @param {int} contextid\n * @returns {mixed}\n */\nexport const getAllConversations = (\n    userid,\n    contextid,\n) => fetchMany([{\n    methodname: 'block_ai_chat_get_all_conversations',\n    args: {\n        userid,\n        contextid\n}}])[0];\n\n/**\n * Get all converstations a User can see.\n * @param {int} contextid\n * @returns {mixed}\n */\nexport const getNewConversationId = (\n    contextid,\n) => fetchMany([{\n    methodname: 'block_ai_chat_get_new_conversation_id',\n    args: {\n        contextid,\n}}])[0];\n\n/**\n * Get all converstations a User can see.\n * @param {int} contextid\n * @param {int} userid\n * @param {int} conversationid\n * @returns {mixed}\n */\nexport const deleteConversation = (\n    contextid,\n    userid,\n    conversationid,\n) => fetchMany([{\n    methodname: 'block_ai_chat_delete_conversation',\n    args: {\n        contextid,\n        userid,\n        conversationid,\n}}])[0];\n\n/**\n * Get conversationcontext message limit.\n * @param {int} contextid\n * @returns {mixed}\n */\nexport const getConversationcontextLimit = (\n    contextid,\n) => fetchMany([{\n    methodname: 'block_ai_chat_get_conversationcontext_limit',\n    args: {\n        contextid\n    }\n}])[0];\n\n\n/**\n * Get current persona.\n * @param {int} contextid\n * @returns {mixed}\n */\nexport const reloadPersona = (\n    contextid,\n) => fetchMany([{\n    methodname: 'block_ai_chat_reload_persona',\n    args: {\n        contextid\n    }\n}])[0];\n"], "names": ["userid", "contextid", "methodname", "args", "conversationid"], "mappings": "oUAuBmC,CAC/BA,OACAC,aACC,cAAU,CAAC,CACZC,WAAY,sCACZC,KAAM,CACFH,OAAAA,OACAC,UAAAA,cACH,iCAQDA,YACC,cAAU,CAAC,CACZC,WAAY,wCACZC,KAAM,CACFF,UAAAA,cACH,+BAS6B,CAC9BA,UACAD,OACAI,kBACC,cAAU,CAAC,CACZF,WAAY,oCACZC,KAAM,CACFF,UAAAA,UACAD,OAAAA,OACAI,eAAAA,mBACH,wCAQDH,YACC,cAAU,CAAC,CACZC,WAAY,8CACZC,KAAM,CACFF,UAAAA,cAEJ,0BASAA,YACC,cAAU,CAAC,CACZC,WAAY,+BACZ<PERSON>,KAAM,CACFF,UAAAA,cAEJ"}