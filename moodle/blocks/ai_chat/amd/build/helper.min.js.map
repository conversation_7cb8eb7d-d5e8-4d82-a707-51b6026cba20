{"version": 3, "file": "helper.min.js", "sources": ["../src/helper.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// <PERSON><PERSON>le is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\nimport {typeset} from 'filter_mathjaxloader/loader';\n\n/**\n * Copy ai reply to clipboard.\n * @param {*} element\n */\nexport const copyToClipboard = (element) => {\n\n    // Find the adjacent text container.\n    const textElement = element.nextElementSibling;\n\n    // Get the text content.\n    const textToCopy = textElement.innerText || textElement.textContent;\n\n    // Copy to clipboard using the Clipboard API.\n    navigator.clipboard.writeText(textToCopy);\n\n    // Briefly show toast.\n    const toast = element.previousElementSibling;\n    toast.style.visibility = 'visible';\n    setTimeout(() => {\n       toast.style.visibility = 'hidden';\n    }, 750);\n\n};\n\n/**\n * Attach copy listener to all elements.\n */\nexport const attachCopyListenerLast = () => {\n    const elements = document.querySelectorAll(\".block_ai_chat_modal .copy\");\n    const lastquestion = elements[elements.length - 2];\n    if (lastquestion) {\n        lastquestion.addEventListener('click', function() {\n            copyToClipboard(lastquestion);\n        });\n    }\n    const lastanswer = elements[elements.length - 1];\n    if (lastanswer) {\n        lastanswer.addEventListener('click', function() {\n            copyToClipboard(lastanswer);\n        });\n    }\n};\n\n\n/**\n * Focus textarea.\n */\nexport const focustextarea = () => {\n    const textarea = document.getElementById('block_ai_chat-input-id');\n    textarea.focus();\n};\n\n\n/**\n * Scroll to bottom of modal body.\n */\nexport const scrollToBottom = () => {\n    const modalContent = document.querySelector('.block_ai_chat_modal .modal-body .block_ai_chat-output-wrapper');\n    modalContent.scrollTop = modalContent.scrollHeight;\n};\n\n\n/**\n * Escape html.\n * @param {*} str\n */\nexport const escapeHTML = (str) => {\n    if (str === null || str === undefined) {\n        return '';\n    }\n    const escapeMap = {\n        '&': '&amp;',\n        '<': '&lt;',\n        '>': '&gt;',\n        '\"': '&quot;',\n        \"'\": '&#39;',\n        '`': '&#x60;',\n        '/': '&#x2F;',\n    };\n\n    return String(str).replace(/[&<>\"'`/]/g, function(match) {\n        return escapeMap[match];\n    });\n};\n\n/**\n * Hash function to get a hash of a string.\n *\n * @param {string} stringToHash the string to hash\n * @returns {Promise<string>} the promise containing a hex representation of the string encoded by SHA-256\n */\nexport const hash = async(stringToHash) => {\n    const encoder = new TextEncoder();\n    const data = encoder.encode(stringToHash);\n    const hashAsArrayBuffer = await window.crypto.subtle.digest(\"SHA-256\", data);\n    const uint8ViewOfHash = new Uint8Array(hashAsArrayBuffer);\n    return Array.from(uint8ViewOfHash)\n        .map((b) => b.toString(16).padStart(2, \"0\"))\n        .join(\"\");\n};\n\n/**\n * Render mathjax formulas.\n *  @returns {void}\n */\nexport const renderMathjax = () => {\n    // Render formulas with mathjax 2.7.9.\n    if (typeof window.MathJax !== \"undefined\") {\n        // Change delimiters so they work with chatgpt.\n        window.MathJax.Hub.Config({\n            tex2jax: {\n                inlineMath: [['$', '$'], ['\\\\(', '\\\\)']],\n                displayMath: [['$$', '$$'], ['\\\\[', '\\\\]']],\n            },\n        });\n        const content = document.querySelector('.block_ai_chat-output');\n        if (content) {\n            // Maybe somebody knows why it works if you use mathjax .Queue and typeset().\n            // I just know that it does.\n            // Claude says: This works because you're essentially giving MathJax two chances to render - the first call\n            // queues it up, and the second call (Moodle's built-in function) ensures it completes. While it might seem\n            // redundant, if it's working reliably, there's nothing wrong with this approach.\n            window.MathJax.Hub.Queue([\"Typeset\", window.MathJax.Hub, content]);\n            typeset(content);\n        }\n    }\n};\n"], "names": ["copyToClipboard", "element", "textElement", "nextElement<PERSON><PERSON>ling", "textToCopy", "innerText", "textContent", "navigator", "clipboard", "writeText", "toast", "previousElementSibling", "style", "visibility", "setTimeout", "elements", "document", "querySelectorAll", "lastquestion", "length", "addEventListener", "<PERSON><PERSON><PERSON>", "getElementById", "focus", "modalContent", "querySelector", "scrollTop", "scrollHeight", "str", "escapeMap", "String", "replace", "match", "async", "data", "TextEncoder", "encode", "stringToHash", "hashAs<PERSON><PERSON><PERSON><PERSON><PERSON>er", "window", "crypto", "subtle", "digest", "uint8ViewOfHash", "Uint8Array", "Array", "from", "map", "b", "toString", "padStart", "join", "MathJax", "<PERSON><PERSON>", "Config", "tex2jax", "inlineMath", "displayMath", "content", "Queue"], "mappings": "0UAqBaA,gBAAmBC,gBAGtBC,YAAcD,QAAQE,mBAGtBC,WAAaF,YAAYG,WAAaH,YAAYI,YAGxDC,UAAUC,UAAUC,UAAUL,kBAGxBM,MAAQT,QAAQU,uBACtBD,MAAME,MAAMC,WAAa,UACzBC,YAAW,KACRJ,MAAME,MAAMC,WAAa,WACzB,+EAO+B,WAC5BE,SAAWC,SAASC,iBAAiB,8BACrCC,aAAeH,SAASA,SAASI,OAAS,GAC5CD,cACAA,aAAaE,iBAAiB,SAAS,WACnCpB,gBAAgBkB,uBAGlBG,WAAaN,SAASA,SAASI,OAAS,GAC1CE,YACAA,WAAWD,iBAAiB,SAAS,WACjCpB,gBAAgBqB,uCASC,KACRL,SAASM,eAAe,0BAChCC,iCAOiB,WACpBC,aAAeR,SAASS,cAAc,kEAC5CD,aAAaE,UAAYF,aAAaG,kCAQfC,SACnBA,MAAAA,UACO,SAELC,UAAY,KACT,YACA,WACA,WACA,aACA,YACA,aACA,iBAGFC,OAAOF,KAAKG,QAAQ,cAAc,SAASC,cACvCH,UAAUG,yBAULC,MAAAA,qBAEVC,MADU,IAAIC,aACCC,OAAOC,cACtBC,wBAA0BC,OAAOC,OAAOC,OAAOC,OAAO,UAAWR,MACjES,gBAAkB,IAAIC,WAAWN,0BAChCO,MAAMC,KAAKH,iBACbI,KAAKC,GAAMA,EAAEC,SAAS,IAAIC,SAAS,EAAG,OACtCC,KAAK,4BAOe,aAEK,IAAnBZ,OAAOa,QAAyB,CAEvCb,OAAOa,QAAQC,IAAIC,OAAO,CACtBC,QAAS,CACLC,WAAY,CAAC,CAAC,IAAK,KAAM,CAAC,MAAO,QACjCC,YAAa,CAAC,CAAC,KAAM,MAAO,CAAC,MAAO,iBAGtCC,QAAU1C,SAASS,cAAc,yBACnCiC,UAMAnB,OAAOa,QAAQC,IAAIM,MAAM,CAAC,UAAWpB,OAAOa,QAAQC,IAAKK,8BACjDA"}