{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template block_ai_chat/message

    But<PERSON> to call ai_chat modal

    Example context (json):
    {
        "message":"content",
        "sender":""
    }
}}
{{! Show message from user or ai }}
{{#sender}}
<div class="message agent">    
{{/sender}}
{{^sender}}
<div class="message ai">
{{/sender}}
    <div class="content">
        <img src="{{globals.config.wwwroot}}/blocks/ai_chat/pix/ai-icon.svg" alt="" class="ai_chat_icon mr-2">
        <div class="copiedtoast">{{#str}} copied, block_ai_chat {{/str}}</div>
        <a class="copy"><i class="fa fa-copy"></i></a>
        <div class="text">
            {{{content}}}
        </div>
    </div>
</div>    
{{! Insert placeholder in case of pending question }}
{{^answer}}
<div class="message ai">
    <div class="content awaitanswer">
    <img src="{{globals.config.wwwroot}}/blocks/ai_chat/pix/ai-icon.svg" alt="" class="ai_chat_icon mr-2">
        <div class="copiedtoast">{{#str}} copied, block_ai_chat {{/str}}</div>
        <a class="copy"><i class="fa fa-copy"></i></a>
        <div class="text small">
            <div class="spinner-border text-secondary" role="status"></div>
            <span class="ml-2">{{#str}} awaitanswer, block_ai_chat {{/str}}</span>
        </div>
    </div>
</div>
{{/answer}}