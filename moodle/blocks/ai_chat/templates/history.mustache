{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template block_ai_chat/history

    Since dialogues are read ASC, template is build upside down and reversed per flex-direction: column-reverse

    Example context (json):
    {
        "key": "Heute",
        "objects":     [{
            "title": "Erste Frage",
            "conversationid": 9
        }]
    }
}}

<button class="btn btn-secondary mt-3 mb-3" id="ai_chat_history_new_dialog">
    <i class="icon fa fa-comment" aria-hidden="true"></i> {{#str}} newdialog, block_ai_chat {{/str}}
</button>

{{#dates}}
    <div class="block_ai-history-items card">
        {{#objects}}
            <div class="" data-id="{{conversationid}}">
                <a class="" onclick="document.showConversation({{conversationid}})" href="#"><i class="fa fa-comment mr-2"></i><span>{{{title}}}</span></a> {{time}}
            </div>
        {{/objects}}
    </div>
    <div class="block_ai-date mt-3 mb-2">
        {{key}}
    </div>
{{/dates}}
{{^dates}}
    <div class="">
        {{#str}} nohistory, block_ai_chat {{/str}}
    </div>
{{/dates}}
