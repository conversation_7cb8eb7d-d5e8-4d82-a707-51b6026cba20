{{!
    This file is part of Moodle - http://moodle.org/

    Moodle is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template block_ai_chat/modal_save_delete_cancel

    Moodle modal template with save, delete and cancel buttons.

    The purpose of this template is to render a modal.

    Classes required for JS:
    * none

    Data attributes required for JS:
    * none

    Context variables required for this template:
    * title A cleaned string (use clean_text()) to display.
    * body HTML content for the boday

    Example context (json):
    {
        "title": "Example delete cancel modal",
        "body": "Some example content for the body"
    }
}}

{{< core/modal }}
    {{$footer}}
        <button type="button" class="btn btn-danger" data-custom="delete" data-action="save">{{#str}} deletetemplate, block_ai_chat {{/str}}</button>
        <button type="button" class="btn btn-secondary" data-action="cancel">{{#str}} cancel {{/str}}</button>
        <button type="button" class="btn btn-primary" data-action="save">{{#str}} savechanges {{/str}}</button>
    {{/footer}}
{{/ core/modal }}
