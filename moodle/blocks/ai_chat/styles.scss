/* stylelint-disable */
#ai_chat_button {
    visibility: visible;
    position: fixed;
    bottom: 4.5rem;
    right: 2rem;
    transition: 0.2s;
    z-index: 1000;

    display: flex;
    justify-content: center;
    align-items: center;

    border: 1px solid #6c757d;;

    &:hover {
        filter: hue-rotate(10deg) brightness(90%);
    }
}
@media (max-width: 576px) {
    #ai_chat_button {
        top: 87vh;
        right: .5rem;
        bottom: unset;
    }
}

.drawer-right.show #ai_chat_button,
.show-drawer-right #ai_chat_button {
    right: calc(315px + 2rem);
}


.block_ai_chat_modal{
    .modal-dialog {
        min-height: 500px;
    }
    .modal-header {
        width: 100%;
        border-bottom: none;
        .modal-title {
           display: flex;
           justify-content: space-between;
           align-items: center;
           width: calc(100% - 2rem);
           font-size: 1rem;
           .block_ai_chat_title {
                text-overflow: ellipsis;
                white-space: nowrap;
                overflow: hidden;
           }
       }
    }

   .modal-body {
       display: flex;
       padding: 0 0 0 1rem;

       .block_ai_chat-dialog {
           display: flex;
           flex-direction: column;
           justify-content: space-between;
           width: 100%;
           height: 100%;
       }
   }

   .content {
       display: flex;
       align-items: flex-start;
       padding: .5rem 1rem .5rem 1rem;
       position: relative;
   }
   .message .copy {
       visibility: hidden;
       position: absolute;
       top: 0;
       right: 0;
       padding: .5rem .75rem 2.5rem 2.75rem;
   }
    .message:hover .copy {
       visibility: visible;
       cursor: pointer;
    }
    .message.ai .content {
        padding-left: 0;
        .text {
            margin-top: .4rem;
            p {
                margin-bottom: .3rem;
            }
        }
        .ai_chat_icon {
            margin-top: .8rem;
        }
        .spinner-border {
            width: 1.5rem;
            height: 1.5rem;
        }
        &.awaitanswer {            
            .ai_chat_icon,
            .copy {
                display: none;
            }
        }
    }
    .message.agent .content {
        background-color: #dee2e6;
        margin-left: 4rem;
        border-radius: .5rem;
        .ai_chat_icon {
            display: none;
        }
        p {
            margin-bottom: .1rem;
        }
    }
    .copiedtoast {
        position: absolute;
        top: -15px;
        right: 0;
        font-size: small;
        border: 1px solid;
        padding: 1px 3px;
        background-color: #fff;
        visibility: hidden;
    }
    .block_ai_chat-input-wrapper {
        position: sticky;
        bottom: 0;
        background-color: #fff;
        padding-right: 1rem;
    }
    .block_ai_chat-input {
        display: flex;
        margin-top: .15rem;
        padding: 0 .1rem 0 .1rem;
        position: relative;
        .ai_chat_icon_button {
            position: absolute;
            top: 0.6rem;
        }
    }
    .block_ai_chat-input textarea {
        height: inherit;
        padding: 1rem 4.1rem 1rem 2.2rem;
        resize: none;
        overflow: auto;
        line-height: 1.5rem;
        min-height: calc(1.5rem + 2rem);;
        max-height: calc((1.5rem * 4) + 2rem);
        box-sizing: border-box;
        margin-left: 2px;
        &::placeholder {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    }
    .block_ai_chat-input button[type="submit"] {
        position: absolute;
        display: flex;
        bottom: .5rem;
        right: .7rem;
        i {
            padding: 6.5px 0;
        }        
    }
    .headeroptions {
        display: flex;
        align-items: center;
        .badge-pill {
            display: flex;
            align-items: center;
            height: 1.3rem;
            font-size: 50%;
        }
        .dropdown-menu {
            left: 36px !important;
            #block_ai_chat_delete_dialog {
                color: #ff3333;
                &:hover {
                    background-color: #ffcccc;
                }
            }
        }
    }

    #block_ai_chat_userquota {
        text-align: right;
        min-height: 1rem;
        .local_ai_manager_userquota_infobox {
            color: #6c757d;
            background-color: inherit;
            font-size: .6rem;
            padding: 0;
            margin: 0;
            text-align: right;
        }
    }
    .gradient-overlay {
        content: '';
        position: sticky;
        top: 0;
        left: 0;
        right: 0;
        height: 2rem;
        background: linear-gradient(to bottom, #fff 10%, transparent);
        z-index: 33;
    }
    .block_ai_chat_modal_body {
        width: 100%;
        overflow: hidden;
        .infobox {
           display: flex;
           align-items: center;
           flex-direction: column;
           padding-bottom: .4rem;
           margin-top: -.5rem;
           z-index: 1;
           min-height: 2rem;
           .local_ai_manager-infobox.alert.alert-success,
           .local_ai_manager-ai-warning {
               color: #6c757d;
               background-color: inherit;
               font-size: .6rem;
               padding: 0;
               margin: 0;
           }
           .local_ai_manager-infobox {
                margin-top: .5rem;
           }
       }
    }
    .block_ai_chat-output-wrapper {
        overflow-y: auto;
        height: 100%;
        padding-right: 1rem;
    }
    .block_ai-history-items {
        padding: .5rem;
        a {
            display: flex;
            align-items: baseline;
            text-overflow: ellipsis;
            white-space: nowrap;
            overflow: hidden;
            p {
                margin: 0;
            }
        }
        & > div {
            display: flex;
            flex-wrap: nowrap;
            justify-content: space-between;
            gap: .2rem;
        }
        &.card {
            flex-direction: column-reverse;
        }
    }
    &.onhistorypage {
        .block_ai_chat-output {
            display: flex;
            flex-direction: column-reverse;            
        }
        .infobox,
        #block_ai_chat_userquota {
            display: none;
        }
        .gradient-overlay {
            display: none;
        } 
    }
}
@media (min-width: 497px) {
    .block_ai_chat_modal.onhistorypage {
        .modal-body {
            min-width: 497px;
        }
    }
}

body.block_ai_chat_chatwindow {
    .block_ai_chat_modal .modal-dialog {
        position: fixed;
        max-height: calc(100vh - 6rem);
        right: 2rem;
        bottom: 3rem;
        filter: drop-shadow(3px 3px 4px black);
    }
    a#block_ai_chat_chatwindow {
        color: #007bff;
    }
}
body.block_ai_chat_chatwindow,
body.block_ai_chat_dockright {
    overflow: initial;
    .modal-backdrop.show,
    .block_ai_chat_modal {
        width: 0;
        height: 0;
    }
    [data-content="local_ai_manager_infobox"],
    .local_ai_manager-ai-warning {
        width: 100%;
        div {
            justify-content: space-between;
            align-items: flex-start;
        }
    }
}

body.block_ai_chat_openfull {
    .block_ai_chat_modal {
        .modal-dialog {
            max-width: 95%;
            min-height: calc(100vh - 3.5rem);
        }
        .block_ai_chat_modal_body {
            max-width: 1080px;
            margin: 0 auto;
        }
    }
    a#block_ai_chat_openfull {
        color: #007bff;
    }
}
body.block_ai_chat_dockright {
    &.block_ai_chat_open #page-wrapper #page {
        margin-right: 50%;
    }
    .block_ai_chat_modal .modal-dialog {
        position: fixed;
        width: 50%;
        max-width: 50%;
        height: calc(100% - 60.2px);
        right: 0;
        bottom: 0;
        margin-top: 0;
        margin-bottom: 0;
        .modal-content {
            border-radius: 0;
            border-top: none;
            border-bottom: none;
            margin-top: -1px;
        }
    }
    a#block_ai_chat_dockright {
        color: #007bff;
    }
}
body.block_ai_chat_chatwindow,
body.block_ai_chat_dockright,
body.block_ai_chat_openfull {
    .block_floatingbutton-floatingicons {
        bottom: calc(2.5rem + 36px);
    }
}
@media (max-width: 576px) {
    .block_ai_chat_action_menu.views {
        display: none;
    }
}


body.block_ai_chat_replacehelp {
    // Replace question mark popover on the bottom right corner with ai_chat.
    #page-footer [data-region="footer-container-popover"] .btn-footer-popover[data-action="footer-popover"] {
        display: none;
    }
    #ai_chat_button {
        bottom: 1.9rem;
    }
}

#add_persona,
#copy_persona {
    font-size: 1.25rem;
    &:hover {
        cursor: pointer;
        filter: invert(30%);
    }
}

.select-spacer {
    font-size: .25rem;
}

