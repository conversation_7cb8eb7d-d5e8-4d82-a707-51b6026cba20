<?xml version="1.0" encoding="UTF-8" ?>
<XMLDB PATH="local/ai_manager/db" VERSION="20250604" COMMENT="XMLDB file for Moodle local/ai_manager"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="../../../lib/xmldb/xmldb.xsd"
>
  <TABLES>
    <TABLE NAME="local_ai_manager_instance" COMMENT="Table storing connector instances">
      <FIELDS>
        <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
        <FIELD NAME="name" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="tenant" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="connector" TYPE="char" LENGTH="50" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="endpoint" TYPE="char" LENGTH="1000" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="apikey" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="model" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="infolink" TYPE="char" LENGTH="1000" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="customfield1" TYPE="text" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="customfield2" TYPE="text" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="customfield3" TYPE="text" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="customfield4" TYPE="text" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="customfield5" TYPE="text" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="timecreated" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="timemodified" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
      </FIELDS>
      <KEYS>
        <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
      </KEYS>
    </TABLE>
    <TABLE NAME="local_ai_manager_config" COMMENT="Table for storing the tenant specific configurations">
      <FIELDS>
        <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
        <FIELD NAME="configkey" TYPE="char" LENGTH="50" NOTNULL="false" SEQUENCE="false" COMMENT="The key for the configuration"/>
        <FIELD NAME="configvalue" TYPE="char" LENGTH="1333" NOTNULL="false" SEQUENCE="false" COMMENT="Value for the config key"/>
        <FIELD NAME="tenant" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false" COMMENT="String identifier of the tenant the config key-value-pair belongs to"/>
      </FIELDS>
      <KEYS>
        <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
      </KEYS>
      <INDEXES>
        <INDEX NAME="tenant" UNIQUE="false" FIELDS="tenant"/>
        <INDEX NAME="configkey_tenant" UNIQUE="true" FIELDS="configkey, tenant"/>
      </INDEXES>
    </TABLE>
    <TABLE NAME="local_ai_manager_request_log" COMMENT="Table to log the used tokens">
      <FIELDS>
        <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
        <FIELD NAME="userid" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="tenant" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="value" TYPE="number" LENGTH="20" NOTNULL="false" SEQUENCE="false" DECIMALS="3"/>
        <FIELD NAME="customvalue1" TYPE="number" LENGTH="20" NOTNULL="false" SEQUENCE="false" DECIMALS="3"/>
        <FIELD NAME="customvalue2" TYPE="number" LENGTH="20" NOTNULL="false" SEQUENCE="false" DECIMALS="3"/>
        <FIELD NAME="purpose" TYPE="char" LENGTH="50" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="connector" TYPE="char" LENGTH="50" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="model" TYPE="char" LENGTH="50" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="modelinfo" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="duration" TYPE="number" LENGTH="20" NOTNULL="false" SEQUENCE="false" DECIMALS="3"/>
        <FIELD NAME="prompttext" TYPE="text" NOTNULL="false" SEQUENCE="false" COMMENT="The prompt which has been sent to the AI tool"/>
        <FIELD NAME="promptcompletion" TYPE="text" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="requestoptions" TYPE="text" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="component" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="contextid" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="coursecontextid" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="itemid" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="deleted" TYPE="int" LENGTH="1" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="timecreated" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
      </FIELDS>
      <KEYS>
        <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
        <KEY NAME="userid" TYPE="foreign" FIELDS="userid" REFTABLE="user" REFFIELDS="id"/>
        <KEY NAME="contextid" TYPE="foreign" FIELDS="contextid" REFTABLE="context" REFFIELDS="id"/>
      </KEYS>
      <INDEXES>
        <INDEX NAME="model" UNIQUE="false" FIELDS="model"/>
      </INDEXES>
    </TABLE>
    <TABLE NAME="local_ai_manager_userinfo" COMMENT="Storing of user specific information for the local_ai_manager plugin">
      <FIELDS>
        <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
        <FIELD NAME="userid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="role" TYPE="int" LENGTH="2" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="locked" TYPE="int" LENGTH="1" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="confirmed" TYPE="int" LENGTH="1" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="scope" TYPE="int" LENGTH="1" NOTNULL="false" SEQUENCE="false" COMMENT="Defines if the user is allowed to use AI tools outside courses"/>
        <FIELD NAME="timemodified" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
      </FIELDS>
      <KEYS>
        <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
        <KEY NAME="userid" TYPE="foreign-unique" FIELDS="userid" REFTABLE="user" REFFIELDS="id"/>
      </KEYS>
    </TABLE>
    <TABLE NAME="local_ai_manager_userusage" COMMENT="Table tracking the current usage of AI tools by the users">
      <FIELDS>
        <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
        <FIELD NAME="purpose" TYPE="char" LENGTH="100" NOTNULL="false" SEQUENCE="false" COMMENT="The purpose"/>
        <FIELD NAME="userid" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="currentusage" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="lastreset" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="timemodified" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
      </FIELDS>
      <KEYS>
        <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
        <KEY NAME="userid" TYPE="foreign" FIELDS="userid" REFTABLE="user" REFFIELDS="id"/>
      </KEYS>
      <INDEXES>
        <INDEX NAME="purpose-userid" UNIQUE="true" FIELDS="purpose, userid"/>
      </INDEXES>
    </TABLE>
  </TABLES>
</XMLDB>
