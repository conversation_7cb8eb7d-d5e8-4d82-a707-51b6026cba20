define("local_ai_manager/viewprompts",["exports","core/str","core/ajax","core/modal","core/templates"],(function(_exports,_str,_ajax,_modal,_templates){function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}
/**
   * Module handling and rendering the prompt views.
   *
   * @module     local_ai_manager/viewprompts
   * @copyright  2024 ISB Bayern
   * <AUTHOR>
   * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
   */Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.init=void 0,_modal=_interopRequireDefault(_modal),_templates=_interopRequireDefault(_templates);const templateContext={};_exports.init=tableWrapperId=>{const table=document.getElementById(tableWrapperId);table.querySelectorAll("[data-view-prompts-userid]").forEach((viewLink=>{viewLink.addEventListener("click",(async()=>{const userId=viewLink.dataset.viewPromptsUserid,contextId=table.dataset.contextid,contextDisplayName=table.dataset.contextdisplayname,userDisplayName=viewLink.dataset.viewPromptsUserdisplayname,currentTime=new Date,lastDay=Math.floor(new Date(currentTime.getTime()-864e5).getTime()/1e3),lastWeek=Math.floor(new Date(currentTime.getTime()-6048e5).getTime()/1e3),lastMonth=Math.floor(new Date(currentTime.getTime()-2592e6).getTime()/1e3);templateContext.lastday=lastDay,templateContext.lastweek=lastWeek,templateContext.lastmonth=lastMonth;const resultObject=await getPrompts(contextId,userId,templateContext.lastday);templateContext.userid=userId,templateContext.contextid=contextId,templateContext.heading=await(0,_str.getString)("promptsmodalheading","local_ai_manager",{contextDisplayName:contextDisplayName,userDisplayName:userDisplayName}),templateContext.promptsobjects=resultObject.result,templateContext.promptsdatesavailable=templateContext.promptsobjects.reduce(((acc,cur)=>acc||cur.viewpromptsdates),!1),templateContext.noprompts=0===templateContext.promptsobjects.length,templateContext.classes="local_ai_manager-prompts_view_modal";const modal=await _modal.default.create({template:"local_ai_manager/promptsmodal",templateContext:templateContext,show:!0,removeOnClose:!0});registerTimeselectorListener(modal,contextId,userId),modal.registerCloseOnCancel()}))}))};const registerTimeselectorListener=(modal,contextId,userId)=>{const timeselector=modal.getModal()[0].querySelector('[data-local_ai_manager-prompts_view="timeselector"]');timeselector.addEventListener("change",(async()=>{const resultObject=await getPrompts(contextId,userId,timeselector.value);templateContext.promptsobjects=resultObject.result,templateContext.promptsdatesavailable=templateContext.promptsobjects.reduce(((acc,cur)=>acc||cur.viewpromptsdates),!1),templateContext.noprompts=0===templateContext.promptsobjects.length;const{html:html,js:js}=await _templates.default.renderForPromise("local_ai_manager/promptsmodal_table",{...templateContext});_templates.default.replaceNode(modal.getModal()[0].querySelector('[data-local_ai_manager-prompts_view="table"]'),html,js)}))},getPrompts=async(contextid,userid,time)=>(0,_ajax.call)([{methodname:"local_ai_manager_get_prompts",args:{contextid:contextid,userid:userid,time:time}}])[0]}));

//# sourceMappingURL=viewprompts.min.js.map