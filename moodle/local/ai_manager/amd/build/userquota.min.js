define("local_ai_manager/userquota",["exports","core/ajax","core/str","core/templates"],(function(_exports,_ajax,_str,_templates){var obj;
/**
   * Module handling the form submission of the statistics tables of local_ai_manager.
   *
   * @module     local_ai_manager/userquota
   * @copyright  2024 ISB Bayern
   * <AUTHOR>
   * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
   */Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.renderUserQuota=void 0,_templates=(obj=_templates)&&obj.__esModule?obj:{default:obj};const constants_MAXUSAGE_UNLIMITED=999999,queryCountStrings={chat:"chat requests",chatShortened:"chat",feedback:"feedback requests",feedbackShortened:"feedback",imggen:"image generation generation requests",imggenShortened:"image generation generation",singleprompt:"text requests",singlepromptShortened:"text",translate:"translation requests",translateShortened:"translation",tts:"audio requests",ttsShortened:"audio",itt:"image analyse requests",ittShortened:"image analyse"};_exports.renderUserQuota=async(selector,purposes)=>{await localizeQueryCountTexts();const targetElement=document.querySelector(selector),userquotaData=await(0,_ajax.call)([{methodname:"local_ai_manager_get_user_quota",args:{}}])[0],purposeInfo=[];purposes.forEach((purpose=>{purposeInfo.push({purpose:purpose,currentusage:userquotaData.usage[purpose].currentusage,maxusage:userquotaData.usage[purpose].maxusage,querycounttext:queryCountStrings[purpose+"Shortened"],showmaxusage:userquotaData.usage[purpose].maxusage!==constants_MAXUSAGE_UNLIMITED,limitreached:userquotaData.usage[purpose].currentusage===userquotaData.usage[purpose].maxusage,islastelement:!1})})),purposeInfo[purposeInfo.length-1].islastelement=!0,purposeInfo[purposeInfo.length-1].querycounttext=queryCountStrings[purposeInfo[purposeInfo.length-1].purpose];const userquotaContentTemplateContext={purposes:purposeInfo,period:userquotaData.period,unlimited:"role_unlimited"===userquotaData.role},{html:html,js:js}=await _templates.default.renderForPromise("local_ai_manager/userquota",userquotaContentTemplateContext);_templates.default.appendNodeContents(targetElement,html,js)};const localizeQueryCountTexts=async()=>{const stringsToFetch=[];Object.keys(queryCountStrings).filter((key=>!key.endsWith("Shortened"))).forEach((key=>{stringsToFetch.push({key:"requestcount",component:"aipurpose_"+key}),stringsToFetch.push({key:"requestcount_shortened",component:"aipurpose_"+key})}));const strings=await(0,_str.getStrings)(stringsToFetch);let i=0;for(const key in queryCountStrings)queryCountStrings[key]=strings[i],i++}}));

//# sourceMappingURL=userquota.min.js.map