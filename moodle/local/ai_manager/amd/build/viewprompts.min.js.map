{"version": 3, "file": "viewprompts.min.js", "sources": ["../src/viewprompts.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Moodle is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Module handling and rendering the prompt views.\n *\n * @module     local_ai_manager/viewprompts\n * @copyright  2024 ISB Bayern\n * <AUTHOR>\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport {getString} from 'core/str';\nimport {call as fetchMany} from 'core/ajax';\nimport Modal from 'core/modal';\nimport Templates from 'core/templates';\n\nconst templateContext = {};\n\n\nexport const init = (tableWrapperId) => {\n    const table = document.getElementById(tableWrapperId);\n    table.querySelectorAll('[data-view-prompts-userid]').forEach(viewLink => {\n        viewLink.addEventListener('click', async() => {\n            const userId = viewLink.dataset.viewPromptsUserid;\n            const contextId = table.dataset.contextid;\n            const contextDisplayName = table.dataset.contextdisplayname;\n            const userDisplayName = viewLink.dataset.viewPromptsUserdisplayname;\n\n            const currentTime = new Date();\n            const lastDay = Math.floor(new Date(currentTime.getTime() - 24 * 60 * 60 * 1000).getTime() / 1000);\n            const lastWeek = Math.floor(new Date(currentTime.getTime() - 7 * 24 * 60 * 60 * 1000).getTime() / 1000);\n            const lastMonth = Math.floor(new Date(currentTime.getTime() - 30 * 24 * 60 * 60 * 1000).getTime() / 1000);\n            templateContext.lastday = lastDay;\n            templateContext.lastweek = lastWeek;\n            templateContext.lastmonth = lastMonth;\n\n            // By default, we load prompts of the last 24 hours.\n            const resultObject = await getPrompts(contextId, userId, templateContext.lastday);\n\n            templateContext.userid = userId;\n            templateContext.contextid = contextId;\n            templateContext.heading =\n                await getString('promptsmodalheading', 'local_ai_manager', {contextDisplayName, userDisplayName});\n            templateContext.promptsobjects = resultObject.result;\n            templateContext.promptsdatesavailable =\n                templateContext.promptsobjects.reduce((acc, cur) => acc || cur.viewpromptsdates, false);\n            templateContext.noprompts = templateContext.promptsobjects.length === 0;\n            templateContext.classes = 'local_ai_manager-prompts_view_modal';\n            const modal = await Modal.create({\n                template: 'local_ai_manager/promptsmodal',\n                templateContext,\n                show: true,\n                removeOnClose: true,\n            });\n            registerTimeselectorListener(modal, contextId, userId);\n            modal.registerCloseOnCancel();\n        });\n    });\n};\n\n/**\n * Registers a listener on the timeselector select that updates the prompts table.\n *\n * @param {object} modal the modal object\n * @param {int} contextId the context id of the main context to load prompts for\n * @param {int} userId the user id of the current user\n */\nconst registerTimeselectorListener = (modal, contextId, userId) => {\n    const timeselector = modal.getModal()[0].querySelector('[data-local_ai_manager-prompts_view=\"timeselector\"]');\n    timeselector.addEventListener('change', async() => {\n        const resultObject = await getPrompts(contextId, userId, timeselector.value);\n        templateContext.promptsobjects = resultObject.result;\n        templateContext.promptsdatesavailable =\n            templateContext.promptsobjects.reduce((acc, cur) => acc || cur.viewpromptsdates, false);\n        templateContext.noprompts = templateContext.promptsobjects.length === 0;\n\n        const {html, js} = await Templates.renderForPromise('local_ai_manager/promptsmodal_table', {...templateContext});\n        Templates.replaceNode(modal.getModal()[0].querySelector('[data-local_ai_manager-prompts_view=\"table\"]'), html, js);\n    });\n};\n\n/**\n * Fetch the prompts from the backend.\n *\n * @param {int} contextid The context id of the context to retrieve the prompts in\n * @param {int} userid the id of the user to retrieve the prompts for\n * @param {int} time the timestamp since when we want to retrieve prompts\n */\nconst getPrompts = async(contextid, userid, time) => fetchMany([{\n    methodname: 'local_ai_manager_get_prompts',\n    args: {\n        contextid,\n        userid,\n        time\n    },\n}])[0];\n\n\n"], "names": ["templateContext", "tableWrapperId", "table", "document", "getElementById", "querySelectorAll", "for<PERSON>ach", "viewLink", "addEventListener", "async", "userId", "dataset", "viewPromptsUserid", "contextId", "contextid", "contextDisplayName", "contextdisplayname", "userDisplayName", "viewPromptsUserdisplayname", "currentTime", "Date", "lastDay", "Math", "floor", "getTime", "lastWeek", "lastM<PERSON>h", "lastday", "lastweek", "lastmonth", "resultObject", "getPrompts", "userid", "heading", "promptsobjects", "result", "promptsdatesavailable", "reduce", "acc", "cur", "viewpromptsdates", "noprompts", "length", "classes", "modal", "Modal", "create", "template", "show", "removeOnClose", "registerTimeselectorListener", "registerCloseOnCancel", "timeselector", "getModal", "querySelector", "value", "html", "js", "Templates", "renderForPromise", "replaceNode", "time", "methodname", "args"], "mappings": ";;;;;;;;4KA6BMA,gBAAkB,iBAGHC,uBACXC,MAAQC,SAASC,eAAeH,gBACtCC,MAAMG,iBAAiB,8BAA8BC,SAAQC,WACzDA,SAASC,iBAAiB,SAASC,gBACzBC,OAASH,SAASI,QAAQC,kBAC1BC,UAAYX,MAAMS,QAAQG,UAC1BC,mBAAqBb,MAAMS,QAAQK,mBACnCC,gBAAkBV,SAASI,QAAQO,2BAEnCC,YAAc,IAAIC,KAClBC,QAAUC,KAAKC,MAAM,IAAIH,KAAKD,YAAYK,UAAY,OAAqBA,UAAY,KACvFC,SAAWH,KAAKC,MAAM,IAAIH,KAAKD,YAAYK,UAAY,QAAyBA,UAAY,KAC5FE,UAAYJ,KAAKC,MAAM,IAAIH,KAAKD,YAAYK,UAAY,QAA0BA,UAAY,KACpGxB,gBAAgB2B,QAAUN,QAC1BrB,gBAAgB4B,SAAWH,SAC3BzB,gBAAgB6B,UAAYH,gBAGtBI,mBAAqBC,WAAWlB,UAAWH,OAAQV,gBAAgB2B,SAEzE3B,gBAAgBgC,OAAStB,OACzBV,gBAAgBc,UAAYD,UAC5Bb,gBAAgBiC,cACN,kBAAU,sBAAuB,mBAAoB,CAAClB,mBAAAA,mBAAoBE,gBAAAA,kBACpFjB,gBAAgBkC,eAAiBJ,aAAaK,OAC9CnC,gBAAgBoC,sBACZpC,gBAAgBkC,eAAeG,QAAO,CAACC,IAAKC,MAAQD,KAAOC,IAAIC,mBAAkB,GACrFxC,gBAAgByC,UAAsD,IAA1CzC,gBAAgBkC,eAAeQ,OAC3D1C,gBAAgB2C,QAAU,4CACpBC,YAAcC,eAAMC,OAAO,CAC7BC,SAAU,gCACV/C,gBAAAA,gBACAgD,MAAM,EACNC,eAAe,IAEnBC,6BAA6BN,MAAO/B,UAAWH,QAC/CkC,MAAMO,qCAYZD,6BAA+B,CAACN,MAAO/B,UAAWH,gBAC9C0C,aAAeR,MAAMS,WAAW,GAAGC,cAAc,uDACvDF,aAAa5C,iBAAiB,UAAUC,gBAC9BqB,mBAAqBC,WAAWlB,UAAWH,OAAQ0C,aAAaG,OACtEvD,gBAAgBkC,eAAiBJ,aAAaK,OAC9CnC,gBAAgBoC,sBACZpC,gBAAgBkC,eAAeG,QAAO,CAACC,IAAKC,MAAQD,KAAOC,IAAIC,mBAAkB,GACrFxC,gBAAgByC,UAAsD,IAA1CzC,gBAAgBkC,eAAeQ,aAErDc,KAACA,KAADC,GAAOA,UAAYC,mBAAUC,iBAAiB,sCAAuC,IAAI3D,qCACrF4D,YAAYhB,MAAMS,WAAW,GAAGC,cAAc,gDAAiDE,KAAMC,QAWjH1B,WAAatB,MAAMK,UAAWkB,OAAQ6B,QAAS,cAAU,CAAC,CAC5DC,WAAY,+BACZC,KAAM,CACFjD,UAAAA,UACAkB,OAAAA,OACA6B,KAAAA,SAEJ"}