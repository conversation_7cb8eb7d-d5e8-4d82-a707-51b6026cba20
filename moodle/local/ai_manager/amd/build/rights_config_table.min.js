define("local_ai_manager/rights_config_table",["exports","core/pending","core/str","core_table/local/dynamic/events"],(function(_exports,_pending,_str,TableEvents){var obj;
/**
   * Module handling the form submission of the statistics tables of local_ai_manager.
   *
   * @module     local_ai_manager/rights_config_table
   * @copyright  2024 ISB Bayern
   * <AUTHOR>
   * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
   */function _getRequireWildcardCache(nodeInterop){if("function"!=typeof WeakMap)return null;var cacheBabelInterop=new WeakMap,cacheNodeInterop=new WeakMap;return(_getRequireWildcardCache=function(nodeInterop){return nodeInterop?cacheNodeInterop:cacheBabelInterop})(nodeInterop)}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.selectors=_exports.init=void 0,_pending=(obj=_pending)&&obj.__esModule?obj:{default:obj},TableEvents=function(obj,nodeInterop){if(!nodeInterop&&obj&&obj.__esModule)return obj;if(null===obj||"object"!=typeof obj&&"function"!=typeof obj)return{default:obj};var cache=_getRequireWildcardCache(nodeInterop);if(cache&&cache.has(obj))return cache.get(obj);var newObj={},hasPropertyDescriptor=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var key in obj)if("default"!==key&&Object.prototype.hasOwnProperty.call(obj,key)){var desc=hasPropertyDescriptor?Object.getOwnPropertyDescriptor(obj,key):null;desc&&(desc.get||desc.set)?Object.defineProperty(newObj,key,desc):newObj[key]=obj[key]}newObj.default=obj,cache&&cache.set(obj,newObj);return newObj}(TableEvents);let table=null;const selectors={CHECKBOX:"input[data-userid]",SELECTALLCHECKBOX:"#rights-table-selectall_checkbox",SELECTIONINFO:"#rights-table-selection_info",USERIDS_INPUT_FIELD:"#rights-table-userids",EXECUTE_BUTTON:"#id_executeaction"};_exports.selectors=selectors;_exports.init=id=>{const pendingPromise=new _pending.default("local_ai_manager/rights_config_table");table=document.getElementById(id),document.addEventListener(TableEvents.tableContentRefreshed,(()=>{table=document.getElementById(id),initCheckboxes()})),initCheckboxes(),pendingPromise.resolve()};const initCheckboxes=()=>{table.querySelectorAll(selectors.CHECKBOX).forEach((checkbox=>{checkbox.addEventListener("change",(event=>{updateSelectAllCheckboxState(),updateUserIds(event.target),updateSelectionCountInfo()}))})),table.querySelector(selectors.SELECTALLCHECKBOX).addEventListener("change",(event=>{updateSelection(event),updateUserIds()})),updateSelectionCountInfo(),updateUserIds()},updateUserIds=()=>{const userIdsInputField=document.querySelector(selectors.USERIDS_INPUT_FIELD),userIds=[];document.querySelectorAll(selectors.CHECKBOX).forEach((checkbox=>{checkbox.checked&&userIds.push(checkbox.dataset.userid)})),userIdsInputField.value=userIds.join(";")},updateSelection=changedEvent=>{const allBoxes=table.querySelectorAll(selectors.CHECKBOX);0!==allBoxes.length&&(changedEvent.target.checked?allBoxes.forEach((box=>{box.checked||(box.checked=!0)})):allBoxes.forEach((box=>{box.checked=!1})),updateSelectionCountInfo())},updateSelectAllCheckboxState=()=>{table.querySelector(selectors.SELECTALLCHECKBOX).checked=!!areAllBoxesChecked()},areAllBoxesChecked=()=>{const allBoxes=table.querySelectorAll(selectors.CHECKBOX);return Array.from(allBoxes).reduce(((a,b)=>a&&b.checked),!0)},checkedCheckboxesCount=()=>{const allBoxes=table.querySelectorAll(selectors.CHECKBOX);return Array.from(allBoxes).filter((checkbox=>checkbox.checked)).length},updateSelectionCountInfo=async()=>{const selectionCountInfoTarget=table.querySelector(selectors.SELECTIONINFO),infoText=await(0,_str.getString)("selecteduserscount","local_ai_manager",checkedCheckboxesCount());selectionCountInfoTarget.innerHTML=infoText,document.querySelector(selectors.EXECUTE_BUTTON).disabled=0===checkedCheckboxesCount()}}));

//# sourceMappingURL=rights_config_table.min.js.map