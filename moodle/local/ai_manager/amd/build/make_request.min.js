define("local_ai_manager/make_request",["exports","core/ajax"],(function(_exports,_ajax){Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.makeRequest=void 0;
/**
   * Module providing functions to send requests to the AI tools.
   *
   * @module     local_ai_manager/make_request
   * @copyright  2024 ISB Bayern
   * <AUTHOR>
   * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
   */
const execMakeRequest=(purpose,prompt,component,contextid,options)=>(0,_ajax.call)([{methodname:"local_ai_manager_post_query",args:{purpose:purpose,prompt:prompt,component:component,contextid:contextid,options:options}}])[0];_exports.makeRequest=async function(purpose,prompt,component){let contextid=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,options=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{};return execMakeRequest(purpose,prompt,component,contextid,JSON.stringify(options))}}));

//# sourceMappingURL=make_request.min.js.map