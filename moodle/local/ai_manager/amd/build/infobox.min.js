define("local_ai_manager/infobox",["exports","core/templates","core/config"],(function(_exports,Templates,config){function _getRequireWildcardCache(nodeInterop){if("function"!=typeof WeakMap)return null;var cacheBabelInterop=new WeakMap,cacheNodeInterop=new WeakMap;return(_getRequireWildcardCache=function(nodeInterop){return nodeInterop?cacheNodeInterop:cacheBabelInterop})(nodeInterop)}function _interopRequireWildcard(obj,nodeInterop){if(!nodeInterop&&obj&&obj.__esModule)return obj;if(null===obj||"object"!=typeof obj&&"function"!=typeof obj)return{default:obj};var cache=_getRequireWildcardCache(nodeInterop);if(cache&&cache.has(obj))return cache.get(obj);var newObj={},hasPropertyDescriptor=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var key in obj)if("default"!==key&&Object.prototype.hasOwnProperty.call(obj,key)){var desc=hasPropertyDescriptor?Object.getOwnPropertyDescriptor(obj,key):null;desc&&(desc.get||desc.set)?Object.defineProperty(newObj,key,desc):newObj[key]=obj[key]}return newObj.default=obj,cache&&cache.set(obj,newObj),newObj}
/**
   * Module handling the form submission of the statistics tables of local_ai_manager.
   *
   * @module     local_ai_manager/infobox
   * @copyright  2024 ISB Bayern
   * <AUTHOR> Memmel
   * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
   */Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.renderInfoBox=void 0,Templates=_interopRequireWildcard(Templates),config=_interopRequireWildcard(config);_exports.renderInfoBox=async function(component,userId,selector){let purposes=arguments.length>3&&void 0!==arguments[3]?arguments[3]:[];const targetElement=document.querySelector(selector),aiInfoUrl=new URL(config.wwwroot+"/local/ai_manager/ai_info.php");purposes.forEach((purpose=>{aiInfoUrl.searchParams.append("purposes[]",purpose)}));const templateContext={aiinfourl:aiInfoUrl},{html:html,js:js}=await Templates.renderForPromise("local_ai_manager/infobox",templateContext);Templates.prependNodeContents(targetElement,html,js)}}));

//# sourceMappingURL=infobox.min.js.map