define("local_ai_manager/warningbox",["exports","local_ai_manager/config","core/log","core/templates"],(function(_exports,_config,_log,_templates){function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}
/**
   * Module rendering the warning box to inform the users about misleading AI results.
   *
   * @module     local_ai_manager/warningbox
   * @copyright  2024 ISB Bayern
   * <AUTHOR>
   * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
   */Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.renderWarningBox=void 0,_log=_interopRequireDefault(_log),_templates=_interopRequireDefault(_templates);_exports.renderWarningBox=async selector=>{let aiConfig=null;try{aiConfig=await(0,_config.getAiConfig)()}catch(error){return void _log.default.debug(error)}const showAiWarningLink=aiConfig.aiwarningurl.length>0,targetElement=document.querySelector(selector),{html:html,js:js}=await _templates.default.renderForPromise("local_ai_manager/ai_info_warning",{showaiwarninglink:showAiWarningLink,aiwarningurl:aiConfig.aiwarningurl});_templates.default.appendNodeContents(targetElement,html,js)}}));

//# sourceMappingURL=warningbox.min.js.map