define("local_ai_manager/instanceaddmodal",["exports","local_ai_manager/config","core/str","core/modal"],(function(_exports,_config,_str,_modal){var obj;
/**
   * Module handling the form submission of the statistics tables of local_ai_manager.
   *
   * @module     local_ai_manager/userquota
   * @copyright  2024 ISB Bayern
   * <AUTHOR>
   * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
   */Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.renderInstanceAddModal=void 0,_modal=(obj=_modal)&&obj.__esModule?obj:{default:obj};_exports.renderInstanceAddModal=async instanceTableSelector=>{const instanceTable=document.querySelector(instanceTableSelector),aiConfig=await(0,_config.getAiConfig)(instanceTable.dataset.tenant),toolsContext=[],pluginnameStringsToFetch=[];aiConfig.tools.forEach((tool=>{pluginnameStringsToFetch.push({key:"pluginname",component:"aitool_"+tool.name})}));const pluginNameStrings=await(0,_str.getStrings)(pluginnameStringsToFetch),descriptionStringsToFetch=[];aiConfig.tools.forEach((tool=>{descriptionStringsToFetch.push({key:"adddescription",component:"aitool_"+tool.name})}));const descriptionStrings=await(0,_str.getStrings)(descriptionStringsToFetch);for(let i=0;i<pluginnameStringsToFetch.length;i++)toolsContext.push({linklabel:pluginNameStrings[i],addurl:aiConfig.tools[i].addurl,adddescription:descriptionStrings[i]});const templateContext={tools:toolsContext};document.getElementById("local_ai_manager-instanceadd_button").addEventListener("click",(async()=>{const instanceAddModal=await _modal.default.create({template:"local_ai_manager/instanceaddmodal",large:!0,templateContext:templateContext});await instanceAddModal.show()}))}}));

//# sourceMappingURL=instanceaddmodal.min.js.map