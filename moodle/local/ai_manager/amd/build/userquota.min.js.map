{"version": 3, "file": "userquota.min.js", "sources": ["../src/userquota.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Mo<PERSON>le is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Module handling the form submission of the statistics tables of local_ai_manager.\n *\n * @module     local_ai_manager/userquota\n * @copyright  2024 ISB Bayern\n * <AUTHOR>\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport {call as fetchMany} from 'core/ajax';\nimport {getStrings} from 'core/str';\nimport Templates from 'core/templates';\n\nconst constants = {\n    MAXUSAGE_UNLIMITED: 999999\n};\n\nconst queryCountStrings = {\n    chat: 'chat requests',\n    chatShortened: 'chat',\n    feedback: 'feedback requests',\n    feedbackShortened: 'feedback',\n    imggen: 'image generation generation requests',\n    imggenShortened: 'image generation generation',\n    singleprompt: 'text requests',\n    singlepromptShortened: 'text',\n    translate: 'translation requests',\n    translateShortened: 'translation',\n    tts: 'audio requests',\n    ttsShortened: 'audio',\n    itt: 'image analyse requests',\n    ittShortened: 'image analyse'\n};\n\nconst fetchUserquotaData = () => fetchMany([{\n    methodname: 'local_ai_manager_get_user_quota',\n    args: {},\n}])[0];\n\n/**\n * Renders the current user usage information into the element identified by the given selector.\n *\n * @param {string} selector the id of the element to insert the infobox\n * @param {string[]} purposes the purposes to show user quota for\n */\nexport const renderUserQuota = async(selector, purposes) => {\n    await localizeQueryCountTexts();\n\n    const targetElement = document.querySelector(selector);\n    const userquotaData = await fetchUserquotaData();\n    const purposeInfo = [];\n\n    purposes.forEach(purpose => {\n        purposeInfo.push(\n            {\n                purpose,\n                'currentusage': userquotaData.usage[purpose].currentusage,\n                maxusage: userquotaData.usage[purpose].maxusage,\n                'querycounttext': queryCountStrings[purpose + 'Shortened'],\n                showmaxusage: userquotaData.usage[purpose].maxusage !== constants.MAXUSAGE_UNLIMITED,\n                limitreached: userquotaData.usage[purpose].currentusage === userquotaData.usage[purpose].maxusage,\n                islastelement: false\n            });\n    });\n    purposeInfo[purposeInfo.length - 1].islastelement = true;\n    purposeInfo[purposeInfo.length - 1].querycounttext = queryCountStrings[purposeInfo[purposeInfo.length - 1].purpose];\n\n    const userquotaContentTemplateContext = {\n        purposes: purposeInfo,\n        period: userquotaData.period,\n        unlimited: userquotaData.role === 'role_unlimited'\n    };\n    const {html, js} = await Templates.renderForPromise('local_ai_manager/userquota', userquotaContentTemplateContext);\n    Templates.appendNodeContents(targetElement, html, js);\n};\n\nconst localizeQueryCountTexts = async() => {\n    const stringsToFetch = [];\n    Object.keys(queryCountStrings).filter(key => !key.endsWith('Shortened')).forEach((key) => {\n        stringsToFetch.push({key: 'requestcount', component: 'aipurpose_' + key});\n        stringsToFetch.push({key: 'requestcount_shortened', component: 'aipurpose_' + key});\n    });\n    const strings = await getStrings(stringsToFetch);\n    let i = 0;\n    for (const key in queryCountStrings) {\n        queryCountStrings[key] = strings[i];\n        i++;\n    }\n};\n"], "names": ["constants", "queryCountStrings", "chat", "chatShortened", "feedback", "feedbackShortened", "imggen", "imggenShortened", "singleprompt", "singlepromptShortened", "translate", "translateShortened", "tts", "ttsShortened", "itt", "ittShortened", "async", "selector", "purposes", "localizeQueryCountTexts", "targetElement", "document", "querySelector", "userquotaData", "methodname", "args", "purposeInfo", "for<PERSON>ach", "purpose", "push", "usage", "currentusage", "maxusage", "showmaxusage", "limitreached", "islastelement", "length", "querycounttext", "userquotaContentTemplateContext", "period", "unlimited", "role", "html", "js", "Templates", "renderForPromise", "appendNodeContents", "stringsToFetch", "Object", "keys", "filter", "key", "endsWith", "component", "strings", "i"], "mappings": ";;;;;;;;iKA4BMA,6BACkB,OAGlBC,kBAAoB,CACtBC,KAAM,gBACNC,cAAe,OACfC,SAAU,oBACVC,kBAAmB,WACnBC,OAAQ,uCACRC,gBAAiB,8BACjBC,aAAc,gBACdC,sBAAuB,OACvBC,UAAW,uBACXC,mBAAoB,cACpBC,IAAK,iBACLC,aAAc,QACdC,IAAK,yBACLC,aAAc,0CAcaC,MAAMC,SAAUC,kBACrCC,gCAEAC,cAAgBC,SAASC,cAAcL,UACvCM,oBAfuB,cAAU,CAAC,CACxCC,WAAY,kCACZC,KAAM,MACN,GAaMC,YAAc,GAEpBR,SAASS,SAAQC,UACbF,YAAYG,KACR,CACID,QAAAA,qBACgBL,cAAcO,MAAMF,SAASG,aAC7CC,SAAUT,cAAcO,MAAMF,SAASI,wBACrB/B,kBAAkB2B,QAAU,aAC9CK,aAAcV,cAAcO,MAAMF,SAASI,WAAahC,6BACxDkC,aAAcX,cAAcO,MAAMF,SAASG,eAAiBR,cAAcO,MAAMF,SAASI,SACzFG,eAAe,OAG3BT,YAAYA,YAAYU,OAAS,GAAGD,eAAgB,EACpDT,YAAYA,YAAYU,OAAS,GAAGC,eAAiBpC,kBAAkByB,YAAYA,YAAYU,OAAS,GAAGR,eAErGU,gCAAkC,CACpCpB,SAAUQ,YACVa,OAAQhB,cAAcgB,OACtBC,UAAkC,mBAAvBjB,cAAckB,OAEvBC,KAACA,KAADC,GAAOA,UAAYC,mBAAUC,iBAAiB,6BAA8BP,oDACxEQ,mBAAmB1B,cAAesB,KAAMC,WAGhDxB,wBAA0BH,gBACtB+B,eAAiB,GACvBC,OAAOC,KAAKhD,mBAAmBiD,QAAOC,MAAQA,IAAIC,SAAS,eAAczB,SAASwB,MAC9EJ,eAAelB,KAAK,CAACsB,IAAK,eAAgBE,UAAW,aAAeF,MACpEJ,eAAelB,KAAK,CAACsB,IAAK,yBAA0BE,UAAW,aAAeF,eAE5EG,cAAgB,mBAAWP,oBAC7BQ,EAAI,MACH,MAAMJ,OAAOlD,kBACdA,kBAAkBkD,KAAOG,QAAQC,GACjCA"}