{"version": 3, "file": "infobox.min.js", "sources": ["../src/infobox.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// <PERSON><PERSON>le is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Module handling the form submission of the statistics tables of local_ai_manager.\n *\n * @module     local_ai_manager/infobox\n * @copyright  2024 ISB Bayern\n * <AUTHOR>\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport * as Templates from 'core/templates';\nimport * as config from 'core/config';\n\n/**\n * Inserts the infobox into the beginning of element with the given selector.\n *\n * Also triggers a confirmation modal the first time it is being rendered by a component.\n *\n * @param {string} component The component name from which this is being called\n * @param {int} userId id of the user\n * @param {string} selector the id of the element to insert the infobox\n * @param {string[]} purposes the purposes which are being used\n */\nexport const renderInfoBox = async(component, userId, selector, purposes = []) => {\n    const targetElement = document.querySelector(selector);\n    const aiInfoUrl = new URL(config.wwwroot + '/local/ai_manager/ai_info.php');\n    purposes.forEach(purpose => {\n        aiInfoUrl.searchParams.append('purposes[]', purpose);\n    });\n    const templateContext = {\n        'aiinfourl': aiInfoUrl\n    };\n    const {html, js} = await Templates.renderForPromise('local_ai_manager/infobox', templateContext);\n    Templates.prependNodeContents(targetElement, html, js);\n};\n"], "names": ["async", "component", "userId", "selector", "purposes", "targetElement", "document", "querySelector", "aiInfoUrl", "URL", "config", "wwwroot", "for<PERSON>ach", "purpose", "searchParams", "append", "templateContext", "html", "js", "Templates", "renderForPromise", "prependNodeContents"], "mappings": ";;;;;;;;sMAqC6BA,eAAMC,UAAWC,OAAQC,cAAUC,gEAAW,SACjEC,cAAgBC,SAASC,cAAcJ,UACvCK,UAAY,IAAIC,IAAIC,OAAOC,QAAU,iCAC3CP,SAASQ,SAAQC,UACbL,UAAUM,aAAaC,OAAO,aAAcF,kBAE1CG,gBAAkB,WACPR,YAEXS,KAACA,KAADC,GAAOA,UAAYC,UAAUC,iBAAiB,2BAA4BJ,iBAChFG,UAAUE,oBAAoBhB,cAAeY,KAAMC"}