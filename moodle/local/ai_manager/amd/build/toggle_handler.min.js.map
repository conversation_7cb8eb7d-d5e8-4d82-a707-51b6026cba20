{"version": 3, "file": "toggle_handler.min.js", "sources": ["../src/toggle_handler.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// <PERSON><PERSON>le is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Toggle handler.\n *\n * @module      local_ai_manager/toggle_handler\n * @copyright   2024 ISB Bayern\n * <AUTHOR>\n * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nexport const init = (inputSelector) => {\n    const toggleContainer = document.querySelector(inputSelector);\n    if (toggleContainer) {\n        const toggle = toggleContainer.querySelector('input');\n\n        toggleContainer.addEventListener('click', () => {\n            // Click event will fire before status is being updated, so we have to invert 0 and 1 here.\n            toggleContainer.dataset.checked = toggle.checked ? '0' : '1';\n            toggle.checked = !toggle.checked;\n            toggle.dispatchEvent(new Event('change'));\n        });\n\n        // To make the toggle also usable without directly loading a page on changing the state\n        // we only add the listener here if both target attributes are set and not empty.\n        const useUrlsOnChange = toggle.dataset.targetwhenchecked && toggle.dataset.targetwhenchecked.length > 0\n            && toggle.dataset.targetwhennotchecked && toggle.dataset.targetwhennotchecked.length > 0;\n        if (useUrlsOnChange) {\n            toggle.addEventListener('change', () => {\n                // New state incoming.\n                if (!toggle.checked) {\n                    window.location.replace(toggle.dataset.targetwhenchecked);\n                } else {\n                    window.location.replace(toggle.dataset.targetwhennotchecked);\n                }\n                return false;\n            });\n        }\n    }\n};\n\n\n"], "names": ["inputSelector", "toggleC<PERSON><PERSON>", "document", "querySelector", "toggle", "addEventListener", "dataset", "checked", "dispatchEvent", "Event", "targetwhenchecked", "length", "targetwhennotchecked", "window", "location", "replace"], "mappings": "oKAwBqBA,sBACXC,gBAAkBC,SAASC,cAAcH,kBAC3CC,gBAAiB,OACXG,OAASH,gBAAgBE,cAAc,SAE7CF,gBAAgBI,iBAAiB,SAAS,KAEtCJ,gBAAgBK,QAAQC,QAAUH,OAAOG,QAAU,IAAM,IACzDH,OAAOG,SAAWH,OAAOG,QACzBH,OAAOI,cAAc,IAAIC,MAAM,cAKXL,OAAOE,QAAQI,mBAAqBN,OAAOE,QAAQI,kBAAkBC,OAAS,GAC/FP,OAAOE,QAAQM,sBAAwBR,OAAOE,QAAQM,qBAAqBD,OAAS,GAEvFP,OAAOC,iBAAiB,UAAU,KAEzBD,OAAOG,QAGRM,OAAOC,SAASC,QAAQX,OAAOE,QAAQM,sBAFvCC,OAAOC,SAASC,QAAQX,OAAOE,QAAQI,oBAIpC"}