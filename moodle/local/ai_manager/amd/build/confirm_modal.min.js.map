{"version": 3, "file": "confirm_modal.min.js", "sources": ["../src/confirm_modal.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Moodle is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Confirmation modal for confirming the usage of the AI tools.\n *\n * @module     local_ai_manager/confirm_modal\n * @copyright  2024 ISB Bayern\n * <AUTHOR>\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport Modal from 'core/modal';\n\nexport default class ModalConfirm extends Modal {\n    static TYPE = 'local_ai_manager/confirm_modal';\n    static TEMPLATE = 'local_ai_manager/confirm_modal';\n\n    configure(modalConfig) {\n        modalConfig.show = true;\n        modalConfig.removeOnClose = true;\n        // Button in our template has data-action=\"cancel\", so we use the modal function\n        // to register the event for properly closing the modal on click.\n        this.registerCloseOnCancel();\n        super.configure(modalConfig);\n    }\n}\n"], "names": ["ModalConfirm", "Modal", "configure", "modalConfig", "show", "removeOnClose", "registerCloseOnCancel"], "mappings": "oZA0BqBA,qBAAqBC,eAItCC,UAAUC,aACNA,YAAYC,MAAO,EACnBD,YAAYE,eAAgB,OAGvBC,8BACCJ,UAAUC,mEAVHH,oBACH,kDADGA,wBAEC"}