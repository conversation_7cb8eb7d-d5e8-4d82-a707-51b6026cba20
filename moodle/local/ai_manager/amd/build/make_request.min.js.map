{"version": 3, "file": "make_request.min.js", "sources": ["../src/make_request.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// <PERSON><PERSON><PERSON> is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Module providing functions to send requests to the AI tools.\n *\n * @module     local_ai_manager/make_request\n * @copyright  2024 ISB Bayern\n * <AUTHOR>\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\nimport {call as fetchMany} from 'core/ajax';\n\n/**\n * Call to store input value\n * @param {string} purpose the purpose to use for the request\n * @param {string} prompt the prompt of the request\n * @param {string} component the component from which the request is being done\n * @param {number} contextid the id of the context from which the request is being done, or 0 for system context\n * @param {string} options additional options, as json encoded string, or an empty string if no additional options\n * @returns {Promise} the request promise\n */\nconst execMakeRequest = (\n    purpose,\n    prompt,\n    component,\n    contextid,\n    options\n) => fetchMany([{\n    methodname: 'local_ai_manager_post_query',\n    args: {\n        purpose,\n        prompt,\n        component,\n        contextid,\n        options\n    },\n}])[0];\n\n/**\n * Executes the call to store input value.\n *\n * @param {string} purpose the purpose to use for the request\n * @param {string} prompt the prompt of the request\n * @param {string} component the component from which the request is being done\n * @param {number} contextid the id of the context from which the request is being done,\n *  will default to 0 (which means system context)\n * @param {object} options additional options\n * @returns {mixed}\n */\nexport const makeRequest = async(purpose, prompt, component, contextid = 0, options = {}) => {\n    return execMakeRequest(purpose, prompt, component, contextid, JSON.stringify(options));\n};\n"], "names": ["execMakeRequest", "purpose", "prompt", "component", "contextid", "options", "methodname", "args", "async", "JSON", "stringify"], "mappings": ";;;;;;;;;MAkCMA,gBAAkB,CACpBC,QACAC,OACAC,UACAC,UACAC,WACC,cAAU,CAAC,CACZC,WAAY,8BACZC,KAAM,CACFN,QAAAA,QACAC,OAAAA,OACAC,UAAAA,UACAC,UAAAA,UACAC,QAAAA,YAEJ,wBAauBG,eAAMP,QAASC,OAAQC,eAAWC,iEAAY,EAAGC,+DAAU,UAC3EL,gBAAgBC,QAASC,OAAQC,UAAWC,UAAWK,KAAKC,UAAUL"}