{"version": 3, "file": "instanceaddmodal.min.js", "sources": ["../src/instanceaddmodal.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Moodle is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Module handling the form submission of the statistics tables of local_ai_manager.\n *\n * @module     local_ai_manager/userquota\n * @copyright  2024 ISB Bayern\n * <AUTHOR>\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport {getAiConfig} from 'local_ai_manager/config';\nimport {getStrings} from 'core/str';\nimport Modal from 'core/modal';\n\nexport const renderInstanceAddModal = async(instanceTableSelector) => {\n    const instanceTable = document.querySelector(instanceTableSelector);\n    const aiConfig = await getAiConfig(instanceTable.dataset.tenant);\n    const toolsContext = [];\n    const pluginnameStringsToFetch = [];\n    aiConfig.tools.forEach((tool) => {\n        pluginnameStringsToFetch.push({key: 'pluginname', component: 'aitool_' + tool.name});\n    });\n    const pluginNameStrings = await getStrings(pluginnameStringsToFetch);\n\n    const descriptionStringsToFetch = [];\n    aiConfig.tools.forEach((tool) => {\n        descriptionStringsToFetch.push({key: 'adddescription', component: 'aitool_' + tool.name});\n    });\n    const descriptionStrings = await getStrings(descriptionStringsToFetch);\n\n    for (let i = 0; i < pluginnameStringsToFetch.length; i++) {\n        toolsContext.push({\n            linklabel: pluginNameStrings[i],\n            addurl: aiConfig.tools[i].addurl,\n            adddescription: descriptionStrings[i],\n        });\n    }\n    const templateContext = {\n        tools: toolsContext\n    };\n    document.getElementById('local_ai_manager-instanceadd_button').addEventListener('click', async() => {\n        const instanceAddModal = await Modal.create({\n            template: 'local_ai_manager/instanceaddmodal',\n            large: true,\n            templateContext\n        });\n        await instanceAddModal.show();\n    });\n};\n"], "names": ["async", "instanceTable", "document", "querySelector", "instanceTableSelector", "aiConfig", "dataset", "tenant", "toolsContext", "pluginnameStringsToFetch", "tools", "for<PERSON>ach", "tool", "push", "key", "component", "name", "pluginNameStrings", "descriptionStringsToFetch", "descriptionStrings", "i", "length", "linklabel", "addurl", "adddescription", "templateContext", "getElementById", "addEventListener", "instanceAddModal", "Modal", "create", "template", "large", "show"], "mappings": ";;;;;;;;0LA4BsCA,MAAAA,8BAC5BC,cAAgBC,SAASC,cAAcC,uBACvCC,eAAiB,uBAAYJ,cAAcK,QAAQC,QACnDC,aAAe,GACfC,yBAA2B,GACjCJ,SAASK,MAAMC,SAASC,OACpBH,yBAAyBI,KAAK,CAACC,IAAK,aAAcC,UAAW,UAAYH,KAAKI,gBAE5EC,wBAA0B,mBAAWR,0BAErCS,0BAA4B,GAClCb,SAASK,MAAMC,SAASC,OACpBM,0BAA0BL,KAAK,CAACC,IAAK,iBAAkBC,UAAW,UAAYH,KAAKI,gBAEjFG,yBAA2B,mBAAWD,+BAEvC,IAAIE,EAAI,EAAGA,EAAIX,yBAAyBY,OAAQD,IACjDZ,aAAaK,KAAK,CACdS,UAAWL,kBAAkBG,GAC7BG,OAAQlB,SAASK,MAAMU,GAAGG,OAC1BC,eAAgBL,mBAAmBC,WAGrCK,gBAAkB,CACpBf,MAAOF,cAEXN,SAASwB,eAAe,uCAAuCC,iBAAiB,SAAS3B,gBAC/E4B,uBAAyBC,eAAMC,OAAO,CACxCC,SAAU,oCACVC,OAAO,EACPP,gBAAAA,wBAEEG,iBAAiBK"}