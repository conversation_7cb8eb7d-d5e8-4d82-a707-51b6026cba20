{"version": 3, "file": "vertexcachestatus.min.js", "sources": ["../src/vertexcachestatus.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// <PERSON><PERSON><PERSON> is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Module rendering the warning box to inform the users about misleading AI results.\n *\n * @module     local_ai_manager/vertexcachestatus\n * @copyright  2024 ISB Bayern\n * <AUTHOR>\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport Templates from 'core/templates';\nimport {call as fetchMany} from 'core/ajax';\nimport {alert as alertModal, exception as displayException} from 'core/notification';\nimport {getString} from 'core/str';\n\n/**\n * Fetches the current cache status of the specified service account.\n *\n * @param {string} serviceaccountinfo the stringified JSON with the service account info\n */\nconst fetchCurrentCacheStatus = (serviceaccountinfo) => fetchMany([{\n    methodname: 'local_ai_manager_vertex_cache_status',\n    args: {\n        serviceaccountinfo\n    }\n}])[0];\n\n/**\n * Updates the current cache status.\n *\n * @param {string} serviceaccountinfo the stringified JSON with the service account info\n * @param {boolean} newstatus true if the cache should be enabled, false if it should be disabled\n */\nconst setCurrentCacheStatus = (serviceaccountinfo, newstatus) => fetchMany([{\n    methodname: 'local_ai_manager_vertex_cache_status',\n    args: {\n        serviceaccountinfo,\n        newstatus\n    }\n}])[0];\n\n/**\n * Controls and renders the Google Vertex AI cache status elements.\n *\n * @param {string} selector the CSS selector of the status element to operate on\n */\nexport const init = async(selector) => {\n    const statusElement = document.querySelector(selector);\n    const refreshButton = statusElement.querySelector('[data-action=\"refresh\"]');\n    const enableCachingButton = statusElement.querySelector('[data-action=\"enablecaching\"]');\n    const disableCachingButton = statusElement.querySelector('[data-action=\"disablecaching\"]');\n    const serviceaccountinfoTextArea = document.getElementById('id_serviceaccountjson');\n    let serviceaccountinfo = serviceaccountinfoTextArea.value;\n    // We want to keep track of the current serviceaccountinfo data, also if the user changes it.\n    serviceaccountinfoTextArea.addEventListener('input', (event) => {\n        serviceaccountinfo = event.target.value;\n    });\n\n    refreshButton.addEventListener('click', async(event) => {\n        event.preventDefault();\n        await updateCachingStatusDisplay(serviceaccountinfo, statusElement);\n    });\n\n    if (enableCachingButton) {\n        enableCachingButton.addEventListener('click', async(event) => {\n            event.preventDefault();\n            enableCachingButton.disabled = true;\n            await updateCachingStatus(serviceaccountinfo, statusElement, true);\n        });\n    }\n    if (disableCachingButton) {\n        disableCachingButton.addEventListener('click', async(event) => {\n            event.preventDefault();\n            disableCachingButton.disabled = true;\n            await updateCachingStatus(serviceaccountinfo, statusElement, false);\n        });\n    }\n};\n\n/**\n * Updates the caching status display.\n *\n * @param {string} serviceaccountinfo the stringified JSON with the service account info\n * @param {string} statusElement the HTML element to operate on\n */\nconst updateCachingStatusDisplay = async(serviceaccountinfo, statusElement) => {\n    let queryResult = null;\n    try {\n        queryResult = await fetchCurrentCacheStatus(serviceaccountinfo);\n    } catch (error) {\n        await displayException(error);\n        return;\n    }\n    if (queryResult.code !== 200) {\n        const errorTitleString = await getString('vertex_error_cachestatus', 'local_ai_manager');\n        await alertModal(errorTitleString, queryResult.error);\n    }\n    const templateContext = {\n        cachingEnabled: queryResult.cachingEnabled,\n        noStatus: false\n    };\n\n    const {html, js} = await Templates.renderForPromise('local_ai_manager/vertexcachestatus', templateContext);\n    Templates.replaceNode(statusElement, html, js);\n};\n\n/**\n * Updates the caching status and updates the DOM to reflect the current state.\n *\n * @param {string} serviceaccountinfo the stringified JSON with the service account info\n * @param {string} statusElement the HTML element to operate on\n * @param {boolean} newstatus the status to set the caching configuration to (true or false)\n */\nconst updateCachingStatus = async(serviceaccountinfo, statusElement, newstatus) => {\n    let queryResult = null;\n    try {\n        queryResult = await setCurrentCacheStatus(serviceaccountinfo, newstatus);\n    } catch (error) {\n        await displayException(error);\n        return;\n    }\n    if (queryResult.code !== 200) {\n        const errorTitleString = await getString('vertex_error_cachestatus', 'local_ai_manager');\n        await alertModal(errorTitleString, queryResult.error);\n        return;\n    }\n    await updateCachingStatusDisplay(serviceaccountinfo, statusElement);\n};\n"], "names": ["async", "statusElement", "document", "querySelector", "selector", "refreshButton", "enableCachingButton", "disableCachingButton", "serviceaccountinfoTextArea", "getElementById", "serviceaccountinfo", "value", "addEventListener", "event", "target", "preventDefault", "updateCachingStatusDisplay", "disabled", "updateCachingStatus", "query<PERSON><PERSON>ult", "methodname", "args", "fetchCurrentCacheStatus", "error", "code", "errorTitleString", "templateContext", "cachingEnabled", "noStatus", "html", "js", "Templates", "renderForPromise", "replaceNode", "newstatus", "setCurrentCacheStatus"], "mappings": ";;;;;;;;8JA4DoBA,MAAAA,iBACVC,cAAgBC,SAASC,cAAcC,UACvCC,cAAgBJ,cAAcE,cAAc,2BAC5CG,oBAAsBL,cAAcE,cAAc,iCAClDI,qBAAuBN,cAAcE,cAAc,kCACnDK,2BAA6BN,SAASO,eAAe,6BACvDC,mBAAqBF,2BAA2BG,MAEpDH,2BAA2BI,iBAAiB,SAAUC,QAClDH,mBAAqBG,MAAMC,OAAOH,SAGtCN,cAAcO,iBAAiB,SAASZ,MAAAA,QACpCa,MAAME,uBACAC,2BAA2BN,mBAAoBT,kBAGrDK,qBACAA,oBAAoBM,iBAAiB,SAASZ,MAAAA,QAC1Ca,MAAME,iBACNT,oBAAoBW,UAAW,QACzBC,oBAAoBR,mBAAoBT,eAAe,MAGjEM,sBACAA,qBAAqBK,iBAAiB,SAASZ,MAAAA,QAC3Ca,MAAME,iBACNR,qBAAqBU,UAAW,QAC1BC,oBAAoBR,mBAAoBT,eAAe,aAWnEe,2BAA6BhB,MAAMU,mBAAoBT,qBACrDkB,YAAc,SAEdA,iBApEyBT,CAAAA,qBAAuB,cAAU,CAAC,CAC/DU,WAAY,uCACZC,KAAM,CACFX,mBAAAA,uBAEJ,GA+DwBY,CAAwBZ,oBAC9C,MAAOa,yBACC,2BAAiBA,UAGF,MAArBJ,YAAYK,KAAc,OACpBC,uBAAyB,kBAAU,2BAA4B,0BAC/D,uBAAWA,iBAAkBN,YAAYI,aAE7CG,gBAAkB,CACpBC,eAAgBR,YAAYQ,eAC5BC,UAAU,IAGRC,KAACA,KAADC,GAAOA,UAAYC,mBAAUC,iBAAiB,qCAAsCN,oCAChFO,YAAYhC,cAAe4B,KAAMC,KAUzCZ,oBAAsBlB,MAAMU,mBAAoBT,cAAeiC,iBAC7Df,YAAc,SAEdA,iBAnFsB,EAACT,mBAAoBwB,aAAc,cAAU,CAAC,CACxEd,WAAY,uCACZC,KAAM,CACFX,mBAAAA,mBACAwB,UAAAA,cAEJ,GA6EwBC,CAAsBzB,mBAAoBwB,WAChE,MAAOX,yBACC,2BAAiBA,UAGF,MAArBJ,YAAYK,WAKVR,2BAA2BN,mBAAoBT,0BAJ3CwB,uBAAyB,kBAAU,2BAA4B,0BAC/D,uBAAWA,iBAAkBN,YAAYI"}