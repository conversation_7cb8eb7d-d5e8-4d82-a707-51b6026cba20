{"version": 3, "file": "rights_config_table.min.js", "sources": ["../src/rights_config_table.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Mo<PERSON>le is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Module handling the form submission of the statistics tables of local_ai_manager.\n *\n * @module     local_ai_manager/rights_config_table\n * @copyright  2024 ISB Bayern\n * <AUTHOR>\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport Pending from 'core/pending';\nimport {getString} from 'core/str';\nimport * as TableEvents from 'core_table/local/dynamic/events';\n\nlet table = null;\n\nexport const selectors = {\n    CHECKBOX: 'input[data-userid]',\n    SELECTALLCHECKBOX: '#rights-table-selectall_checkbox',\n    SELECTIONINFO: '#rights-table-selection_info',\n    USERIDS_INPUT_FIELD: '#rights-table-userids',\n    EXECUTE_BUTTON: '#id_executeaction'\n};\n\n/**\n * Initialize the bulk handling on the statistics table.\n * @param {string} id the id of the table to operate on\n */\nexport const init = (id) => {\n    const pendingPromise = new Pending('local_ai_manager/rights_config_table');\n    table = document.getElementById(id);\n\n    document.addEventListener(TableEvents.tableContentRefreshed, () => {\n        // Update the table object, because it has been newly created.\n        table = document.getElementById(id);\n        initCheckboxes();\n    });\n\n    initCheckboxes();\n\n    pendingPromise.resolve();\n};\n\n/**\n * Add the proper listeners to the checkboxes in the table.\n */\nconst initCheckboxes = () => {\n    table.querySelectorAll(selectors.CHECKBOX).forEach(checkbox => {\n        checkbox.addEventListener('change', event => {\n            updateSelectAllCheckboxState();\n            updateUserIds(event.target);\n            updateSelectionCountInfo();\n        });\n    });\n    table.querySelector(selectors.SELECTALLCHECKBOX).addEventListener('change', event => {\n        updateSelection(event);\n        // If we set the status of the checkboxes via JS there is no change event on the checkboxes,\n        // so we need to manually trigger the update of the user ids.\n        updateUserIds();\n    });\n    updateSelectionCountInfo();\n    // In case the browser remembered the state after site reload, we need to set the initial state of user ids dependent on the\n    // boxes' current state.\n    updateUserIds();\n};\n\n/**\n * Update the user ids input field for form submission.\n */\nconst updateUserIds = () => {\n    const userIdsInputField = document.querySelector(selectors.USERIDS_INPUT_FIELD);\n    const userIds = [];\n    document.querySelectorAll(selectors.CHECKBOX).forEach(checkbox => {\n        if (checkbox.checked) {\n            userIds.push(checkbox.dataset.userid);\n        }\n    });\n    userIdsInputField.value = userIds.join(';');\n};\n\n/**\n * Updates the checked states of the user checkboxes according to the change of the \"select/deselect all\" checkbox.\n *\n * @param {object} changedEvent the changed event of the \"select/deselect all\" checkbox\n */\nconst updateSelection = (changedEvent) => {\n    const allBoxes = table.querySelectorAll(selectors.CHECKBOX);\n    if (allBoxes.length === 0) {\n        return;\n    }\n    if (changedEvent.target.checked) {\n        allBoxes.forEach((box) => {\n            if (!box.checked) {\n                box.checked = true;\n            }\n        });\n    } else {\n        allBoxes.forEach((box) => {\n            box.checked = false;\n        });\n    }\n    updateSelectionCountInfo();\n};\n\n/**\n * Updates the \"select/deselect all\" checkbox according to the state of the other checkboxes.\n */\nconst updateSelectAllCheckboxState = () => {\n    const selectAllCheckbox = table.querySelector(selectors.SELECTALLCHECKBOX);\n    selectAllCheckbox.checked = !!areAllBoxesChecked();\n};\n\n/**\n * Helper function to determine if all user checkboxes are checked or not.\n *\n * @returns {bool} true if all boxes are checked, false otherwise\n */\nconst areAllBoxesChecked = () => {\n    const allBoxes = table.querySelectorAll(selectors.CHECKBOX);\n    return Array.from(allBoxes).reduce((a, b) => a && b.checked, true);\n};\n\n/**\n * Returns the amount of currently checked checkboxes.\n *\n * @returns {number} the count of currently checked checkboxes\n */\nconst checkedCheckboxesCount = () => {\n    const allBoxes = table.querySelectorAll(selectors.CHECKBOX);\n    const checkedBoxes = Array.from(allBoxes).filter(checkbox => checkbox.checked);\n    return checkedBoxes.length;\n};\n\n/**\n * Updates the selection count info text box.\n */\nconst updateSelectionCountInfo = async() => {\n    const selectionCountInfoTarget = table.querySelector(selectors.SELECTIONINFO);\n    const infoText = await getString('selecteduserscount', 'local_ai_manager', checkedCheckboxesCount());\n    selectionCountInfoTarget.innerHTML = infoText;\n    document.querySelector(selectors.EXECUTE_BUTTON).disabled = checkedCheckboxesCount() === 0;\n};\n"], "names": ["table", "selectors", "CHECKBOX", "SELECTALLCHECKBOX", "SELECTIONINFO", "USERIDS_INPUT_FIELD", "EXECUTE_BUTTON", "id", "pendingPromise", "Pending", "document", "getElementById", "addEventListener", "TableEvents", "tableContentRefreshed", "initCheckboxes", "resolve", "querySelectorAll", "for<PERSON>ach", "checkbox", "event", "updateSelectAllCheckboxState", "updateUserIds", "target", "updateSelectionCountInfo", "querySelector", "updateSelection", "userIdsInputField", "userIds", "checked", "push", "dataset", "userid", "value", "join", "changedEvent", "allBoxes", "length", "box", "areAllBoxesChecked", "Array", "from", "reduce", "a", "b", "checkedCheckboxesCount", "filter", "async", "selectionCountInfoTarget", "infoText", "innerHTML", "disabled"], "mappings": ";;;;;;;;4lCA4BIA,MAAQ,WAECC,UAAY,CACrBC,SAAU,qBACVC,kBAAmB,mCACnBC,cAAe,+BACfC,oBAAqB,wBACrBC,eAAgB,gEAOCC,WACXC,eAAiB,IAAIC,iBAAQ,wCACnCT,MAAQU,SAASC,eAAeJ,IAEhCG,SAASE,iBAAiBC,YAAYC,uBAAuB,KAEzDd,MAAQU,SAASC,eAAeJ,IAChCQ,oBAGJA,iBAEAP,eAAeQ,iBAMbD,eAAiB,KACnBf,MAAMiB,iBAAiBhB,UAAUC,UAAUgB,SAAQC,WAC/CA,SAASP,iBAAiB,UAAUQ,QAChCC,+BACAC,cAAcF,MAAMG,QACpBC,iCAGRxB,MAAMyB,cAAcxB,UAAUE,mBAAmBS,iBAAiB,UAAUQ,QACxEM,gBAAgBN,OAGhBE,mBAEJE,2BAGAF,iBAMEA,cAAgB,WACZK,kBAAoBjB,SAASe,cAAcxB,UAAUI,qBACrDuB,QAAU,GAChBlB,SAASO,iBAAiBhB,UAAUC,UAAUgB,SAAQC,WAC9CA,SAASU,SACTD,QAAQE,KAAKX,SAASY,QAAQC,WAGtCL,kBAAkBM,MAAQL,QAAQM,KAAK,MAQrCR,gBAAmBS,qBACfC,SAAWpC,MAAMiB,iBAAiBhB,UAAUC,UAC1B,IAApBkC,SAASC,SAGTF,aAAaZ,OAAOM,QACpBO,SAASlB,SAASoB,MACTA,IAAIT,UACLS,IAAIT,SAAU,MAItBO,SAASlB,SAASoB,MACdA,IAAIT,SAAU,KAGtBL,6BAMEH,6BAA+B,KACPrB,MAAMyB,cAAcxB,UAAUE,mBACtC0B,UAAYU,sBAQ5BA,mBAAqB,WACjBH,SAAWpC,MAAMiB,iBAAiBhB,UAAUC,iBAC3CsC,MAAMC,KAAKL,UAAUM,QAAO,CAACC,EAAGC,IAAMD,GAAKC,EAAEf,UAAS,IAQ3DgB,uBAAyB,WACrBT,SAAWpC,MAAMiB,iBAAiBhB,UAAUC,iBAC7BsC,MAAMC,KAAKL,UAAUU,QAAO3B,UAAYA,SAASU,UAClDQ,QAMlBb,yBAA2BuB,gBACvBC,yBAA2BhD,MAAMyB,cAAcxB,UAAUG,eACzD6C,eAAiB,kBAAU,qBAAsB,mBAAoBJ,0BAC3EG,yBAAyBE,UAAYD,SACrCvC,SAASe,cAAcxB,UAAUK,gBAAgB6C,SAAwC,IAA7BN"}