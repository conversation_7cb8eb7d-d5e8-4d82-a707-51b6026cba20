{"version": 3, "file": "warningbox.min.js", "sources": ["../src/warningbox.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Mo<PERSON>le is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Module rendering the warning box to inform the users about misleading AI results.\n *\n * @module     local_ai_manager/warningbox\n * @copyright  2024 ISB Bayern\n * <AUTHOR>\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport {getAiConfig} from 'local_ai_manager/config';\nimport Log from 'core/log';\nimport Templates from 'core/templates';\n\n\n/**\n * Renders the warning box.\n *\n * @param {string} selector the selector where the warning box should be rendered into\n */\nexport const renderWarningBox = async(selector) => {\n    let aiConfig = null;\n    try {\n        aiConfig = await getAiConfig();\n    } catch (error) {\n        // This typically happens if we do not have the capabilities to retrieve the AI config.\n        // So we just eventually log in debug mode and do not render anything.\n        Log.debug(error);\n        return;\n    }\n    const showAiWarningLink = aiConfig.aiwarningurl.length > 0;\n    const targetElement = document.querySelector(selector);\n    const {html, js} = await Templates.renderForPromise('local_ai_manager/ai_info_warning', {\n        showaiwarninglink: showAiWarningLink,\n        aiwarningurl: aiConfig.aiwarningurl\n    });\n    Templates.appendNodeContents(targetElement, html, js);\n};\n"], "names": ["async", "aiConfig", "error", "debug", "showAiWarningLink", "a<PERSON><PERSON><PERSON><PERSON><PERSON>", "length", "targetElement", "document", "querySelector", "selector", "html", "js", "Templates", "renderForPromise", "showaiwarninglink", "appendNodeContents"], "mappings": ";;;;;;;;wMAkCgCA,MAAAA,eACxBC,SAAW,SAEXA,eAAiB,yBACnB,MAAOC,gCAGDC,MAAMD,aAGRE,kBAAoBH,SAASI,aAAaC,OAAS,EACnDC,cAAgBC,SAASC,cAAcC,WACvCC,KAACA,KAADC,GAAOA,UAAYC,mBAAUC,iBAAiB,mCAAoC,CACpFC,kBAAmBX,kBACnBC,aAAcJ,SAASI,kCAEjBW,mBAAmBT,cAAeI,KAAMC"}