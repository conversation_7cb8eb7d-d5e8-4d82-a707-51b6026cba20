{"version": 3, "file": "table_filter.min.js", "sources": ["../src/table_filter.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// <PERSON><PERSON><PERSON> is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Table filter management.\n *\n * Basically copied from core_user/participants_filter.\n *\n * @module     local_ai_manager/tablefilter\n * @copyright  2025 ISB Bayern\n * <AUTHOR>\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport CoreFilter from 'core/datafilter';\nimport * as DynamicTable from 'core_table/dynamic';\nimport Selectors from 'core/datafilter/selectors';\nimport Notification from 'core/notification';\nimport Pending from 'core/pending';\nimport Log from 'core/log';\n\n/**\n * Initialise the participants filter on the element with the given id.\n *\n * @param {String} filterRegionId The id for the filter element.\n */\nexport const init = filterRegionId => {\n\n    const filterSet = document.getElementById(filterRegionId);\n\n    // Create and initialize filter.\n    const coreFilter = new CoreFilter(filterSet, function(filters, pendingPromise) {\n        DynamicTable.setFilters(\n            DynamicTable.getTableFromId(filterSet.dataset.tableRegion),\n            {\n                jointype: parseInt(filterSet.querySelector(Selectors.filterset.fields.join).value, 10),\n                filters,\n            }\n        )\n            .then(result => {\n                pendingPromise.resolve();\n\n                return result;\n            })\n            .catch(Notification.exception);\n    });\n    // This is a dirty hack, because the core/datafilter for some reason initializes a courseid filter which we do not want.\n    coreFilter.removeFilterObject('courseid');\n    coreFilter.init();\n\n    /**\n     * Set the current filter options based on a provided configuration.\n     *\n     * @param {Object} config\n     * @param {Number} config.jointype\n     * @param {Object} config.filters\n     * @returns {Promise}\n     */\n    const setFilterFromConfig = config => {\n        const filterConfig = Object.entries(config.filters);\n\n        if (!filterConfig.length) {\n            // There are no filters to set from.\n            return Promise.resolve();\n        }\n\n        // Set the main join type.\n        filterSet.querySelector(Selectors.filterset.fields.join).value = config.jointype;\n\n        const filterPromises = filterConfig.map(([filterType, filterData]) => {\n            if (filterType === 'courseid') {\n                // The courseid is a special case.\n                return false;\n            }\n\n            const filterValues = filterData.values;\n\n            if (!filterValues.length) {\n                // There are no values for this filter.\n                // Skip it.\n                return false;\n            }\n            return coreFilter.addFilterRow()\n                .then(([filterRow]) => {\n                    coreFilter.addFilter(filterRow, filterType, filterValues);\n                    return;\n                });\n        }).filter(promise => promise);\n\n        if (!filterPromises.length) {\n            return Promise.resolve();\n        }\n\n        return Promise.all(filterPromises)\n            .then(() => {\n                return coreFilter.removeEmptyFilters();\n            })\n            .then(() => {\n                coreFilter.updateFiltersOptions();\n                return;\n            })\n            .then(() => {\n                coreFilter.updateTableFromFilter();\n                return;\n            });\n    };\n\n    // Initialize DynamicTable for showing result.\n    const tableRoot = DynamicTable.getTableFromId(filterSet.dataset.tableRegion);\n    const initialFilters = DynamicTable.getFilters(tableRoot);\n    if (initialFilters) {\n        const initialFilterPromise = new Pending('core/filter:setFilterFromConfig');\n        // Apply the initial filter configuration.\n        setFilterFromConfig(initialFilters)\n            .then(() => initialFilterPromise.resolve())\n            .catch(Log.error);\n    }\n};\n"], "names": ["filterRegionId", "filterSet", "document", "getElementById", "coreFilter", "<PERSON><PERSON><PERSON>er", "filters", "pendingPromise", "DynamicTable", "setFilters", "getTableFromId", "dataset", "tableRegion", "jointype", "parseInt", "querySelector", "Selectors", "filterset", "fields", "join", "value", "then", "result", "resolve", "catch", "Notification", "exception", "removeFilterObject", "init", "tableRoot", "initialFilters", "getFilters", "initialFilterPromise", "Pending", "config", "filterConfig", "Object", "entries", "length", "Promise", "filterPromises", "map", "_ref", "filterType", "filterData", "filterValues", "values", "addFilterRow", "_ref2", "filterRow", "addFilter", "filter", "promise", "all", "removeEmptyFilters", "updateFiltersOptions", "updateTableFromFilter", "setFilterFromConfig", "Log", "error"], "mappings": ";;;;;;;;;;s+BAsCoBA,uBAEVC,UAAYC,SAASC,eAAeH,gBAGpCI,WAAa,IAAIC,oBAAWJ,WAAW,SAASK,QAASC,gBAC3DC,aAAaC,WACTD,aAAaE,eAAeT,UAAUU,QAAQC,aAC9C,CACIC,SAAUC,SAASb,UAAUc,cAAcC,mBAAUC,UAAUC,OAAOC,MAAMC,MAAO,IACnFd,QAAAA,UAGHe,MAAKC,SACFf,eAAegB,UAERD,UAEVE,MAAMC,sBAAaC,cAG5BtB,WAAWuB,mBAAmB,YAC9BvB,WAAWwB,aA4DLC,UAAYrB,aAAaE,eAAeT,UAAUU,QAAQC,aAC1DkB,eAAiBtB,aAAauB,WAAWF,cAC3CC,eAAgB,OACVE,qBAAuB,IAAIC,iBAAQ,mCArDjBC,CAAAA,eAClBC,aAAeC,OAAOC,QAAQH,OAAO5B,aAEtC6B,aAAaG,cAEPC,QAAQhB,UAInBtB,UAAUc,cAAcC,mBAAUC,UAAUC,OAAOC,MAAMC,MAAQc,OAAOrB,eAElE2B,eAAiBL,aAAaM,KAAIC,WAAEC,WAAYC,oBAC/B,aAAfD,kBAEO,QAGLE,aAAeD,WAAWE,eAE3BD,aAAaP,QAKXlC,WAAW2C,eACb1B,MAAK2B,YAAEC,iBACJ7C,WAAW8C,UAAUD,UAAWN,WAAYE,oBAGrDM,QAAOC,SAAWA,iBAEhBZ,eAAeF,OAIbC,QAAQc,IAAIb,gBACdnB,MAAK,IACKjB,WAAWkD,uBAErBjC,MAAK,KACFjB,WAAWmD,0BAGdlC,MAAK,KACFjB,WAAWoD,2BAZRjB,QAAQhB,WAuBnBkC,CAAoB3B,gBACfT,MAAK,IAAMW,qBAAqBT,YAChCC,MAAMkC,aAAIC"}