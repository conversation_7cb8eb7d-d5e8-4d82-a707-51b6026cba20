define("local_ai_manager/toggle_handler",["exports"],(function(_exports){Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.init=void 0;_exports.init=inputSelector=>{const toggleContainer=document.querySelector(inputSelector);if(toggleContainer){const toggle=toggleContainer.querySelector("input");toggleContainer.addEventListener("click",(()=>{toggleContainer.dataset.checked=toggle.checked?"0":"1",toggle.checked=!toggle.checked,toggle.dispatchEvent(new Event("change"))}));toggle.dataset.targetwhenchecked&&toggle.dataset.targetwhenchecked.length>0&&toggle.dataset.targetwhennotchecked&&toggle.dataset.targetwhennotchecked.length>0&&toggle.addEventListener("change",(()=>(toggle.checked?window.location.replace(toggle.dataset.targetwhennotchecked):window.location.replace(toggle.dataset.targetwhenchecked),!1)))}}}));

//# sourceMappingURL=toggle_handler.min.js.map