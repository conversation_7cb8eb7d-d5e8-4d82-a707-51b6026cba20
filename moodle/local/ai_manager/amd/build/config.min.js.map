{"version": 3, "file": "config.min.js", "sources": ["../src/config.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// <PERSON><PERSON><PERSON> is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Module handling the retrieving of the ai config object.\n *\n * @module     local_ai_manager/config\n * @copyright  2024 ISB Bayern\n * <AUTHOR>\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport {call as fetchMany} from 'core/ajax';\nimport {exception as displayException} from 'core/notification';\n\n\nlet aiConfig = null;\n\n/**\n * Make request for retrieving the purpose configuration for current tenant.\n *\n * @param {string} tenant the tenant identifier or null, if the tenant of the user should be used\n */\nconst fetchAiConfig = (tenant = null) => fetchMany([{\n    methodname: 'local_ai_manager_get_ai_config',\n    args: {\n        tenant\n    },\n}])[0];\n\nconst fetchPurposeOptions = (purpose) => fetchMany([{\n    methodname: 'local_ai_manager_get_purpose_options',\n    args: {\n        purpose\n    },\n}])[0];\n\n/**\n * Executes the call to store input value.\n *\n * @param {string} tenant the tenant identifier or null, if the tenant of the user should be used\n */\nexport const getAiConfig = async(tenant = null) => {\n    if (aiConfig === null) {\n        aiConfig = await fetchAiConfig(tenant);\n    }\n    return aiConfig;\n};\n\nexport const getPurposeOptions = async(purpose) => {\n    let purposeOptions = null;\n    try {\n        purposeOptions = await fetchPurposeOptions(purpose);\n    } catch (exception) {\n        await displayException(exception);\n    }\n    return purposeOptions;\n};\n"], "names": ["aiConfig", "fetchAiConfig", "tenant", "methodname", "args", "async", "purposeOptions", "purpose", "fetchPurposeOptions", "exception"], "mappings": ";;;;;;;;;IA4BIA,SAAW,WAOTC,cAAgB,eAACC,8DAAS,YAAS,cAAU,CAAC,CAChDC,WAAY,iCACZC,KAAM,CACFF,OAAAA,WAEJ,yBAcuBG,qBAAMH,8DAAS,YACrB,OAAbF,WACAA,eAAiBC,cAAcC,SAE5BF,qCAGsBK,MAAAA,cACzBC,eAAiB,SAEjBA,oBAtBqBC,CAAAA,UAAY,cAAU,CAAC,CAChDJ,WAAY,uCACZC,KAAM,CACFG,QAAAA,YAEJ,GAiB2BC,CAAoBD,SAC7C,MAAOE,iBACC,2BAAiBA,kBAEpBH"}