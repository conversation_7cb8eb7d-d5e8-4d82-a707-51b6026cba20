define("local_ai_manager/config",["exports","core/ajax","core/notification"],(function(_exports,_ajax,_notification){Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.getPurposeOptions=_exports.getAiConfig=void 0;
/**
   * Module handling the retrieving of the ai config object.
   *
   * @module     local_ai_manager/config
   * @copyright  2024 ISB Bayern
   * <AUTHOR>
   * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
   */
let aiConfig=null;const fetchAiConfig=function(){let tenant=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;return(0,_ajax.call)([{methodname:"local_ai_manager_get_ai_config",args:{tenant:tenant}}])[0]};_exports.getAiConfig=async function(){let tenant=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;return null===aiConfig&&(aiConfig=await fetchAiConfig(tenant)),aiConfig};_exports.getPurposeOptions=async purpose=>{let purposeOptions=null;try{purposeOptions=await(purpose=>(0,_ajax.call)([{methodname:"local_ai_manager_get_purpose_options",args:{purpose:purpose}}])[0])(purpose)}catch(exception){await(0,_notification.exception)(exception)}return purposeOptions}}));

//# sourceMappingURL=config.min.js.map