define("local_ai_manager/vertexcachestatus",["exports","core/templates","core/ajax","core/notification","core/str"],(function(_exports,_templates,_ajax,_notification,_str){var obj;
/**
   * Module rendering the warning box to inform the users about misleading AI results.
   *
   * @module     local_ai_manager/vertexcachestatus
   * @copyright  2024 ISB Bayern
   * <AUTHOR>
   * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
   */Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.init=void 0,_templates=(obj=_templates)&&obj.__esModule?obj:{default:obj};_exports.init=async selector=>{const statusElement=document.querySelector(selector),refreshButton=statusElement.querySelector('[data-action="refresh"]'),enableCachingButton=statusElement.querySelector('[data-action="enablecaching"]'),disableCachingButton=statusElement.querySelector('[data-action="disablecaching"]'),serviceaccountinfoTextArea=document.getElementById("id_serviceaccountjson");let serviceaccountinfo=serviceaccountinfoTextArea.value;serviceaccountinfoTextArea.addEventListener("input",(event=>{serviceaccountinfo=event.target.value})),refreshButton.addEventListener("click",(async event=>{event.preventDefault(),await updateCachingStatusDisplay(serviceaccountinfo,statusElement)})),enableCachingButton&&enableCachingButton.addEventListener("click",(async event=>{event.preventDefault(),enableCachingButton.disabled=!0,await updateCachingStatus(serviceaccountinfo,statusElement,!0)})),disableCachingButton&&disableCachingButton.addEventListener("click",(async event=>{event.preventDefault(),disableCachingButton.disabled=!0,await updateCachingStatus(serviceaccountinfo,statusElement,!1)}))};const updateCachingStatusDisplay=async(serviceaccountinfo,statusElement)=>{let queryResult=null;try{queryResult=await(serviceaccountinfo=>(0,_ajax.call)([{methodname:"local_ai_manager_vertex_cache_status",args:{serviceaccountinfo:serviceaccountinfo}}])[0])(serviceaccountinfo)}catch(error){return void await(0,_notification.exception)(error)}if(200!==queryResult.code){const errorTitleString=await(0,_str.getString)("vertex_error_cachestatus","local_ai_manager");await(0,_notification.alert)(errorTitleString,queryResult.error)}const templateContext={cachingEnabled:queryResult.cachingEnabled,noStatus:!1},{html:html,js:js}=await _templates.default.renderForPromise("local_ai_manager/vertexcachestatus",templateContext);_templates.default.replaceNode(statusElement,html,js)},updateCachingStatus=async(serviceaccountinfo,statusElement,newstatus)=>{let queryResult=null;try{queryResult=await((serviceaccountinfo,newstatus)=>(0,_ajax.call)([{methodname:"local_ai_manager_vertex_cache_status",args:{serviceaccountinfo:serviceaccountinfo,newstatus:newstatus}}])[0])(serviceaccountinfo,newstatus)}catch(error){return void await(0,_notification.exception)(error)}if(200===queryResult.code)await updateCachingStatusDisplay(serviceaccountinfo,statusElement);else{const errorTitleString=await(0,_str.getString)("vertex_error_cachestatus","local_ai_manager");await(0,_notification.alert)(errorTitleString,queryResult.error)}}}));

//# sourceMappingURL=vertexcachestatus.min.js.map