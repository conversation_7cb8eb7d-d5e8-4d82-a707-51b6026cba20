define("local_ai_manager/confirm_modal",["exports","core/modal"],(function(_exports,_modal){var obj;function _defineProperty(obj,key,value){return key in obj?Object.defineProperty(obj,key,{value:value,enumerable:!0,configurable:!0,writable:!0}):obj[key]=value,obj}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0,_modal=(obj=_modal)&&obj.__esModule?obj:{default:obj};class ModalConfirm extends _modal.default{configure(modalConfig){modalConfig.show=!0,modalConfig.removeOnClose=!0,this.registerCloseOnCancel(),super.configure(modalConfig)}}return _exports.default=ModalConfirm,_defineProperty(ModalConfirm,"TYPE","local_ai_manager/confirm_modal"),_defineProperty(ModalConfirm,"TEMPLATE","local_ai_manager/confirm_modal"),_exports.default}));

//# sourceMappingURL=confirm_modal.min.js.map