<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Lang strings for aitool_gemini - EN.
 *
 * @package    aitool_gemini
 * @copyright  ISB Bayern, 2024
 * <AUTHOR>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

$string['adddescription'] = 'Gemini, Google DeepMind\'s AI model, combines advanced language models (LLMs) with tools and multimodal capabilities.';
$string['googlebackend'] = 'Google Backend to use';
$string['googlebackendgoogleai'] = 'Google AI';
$string['googlebackendvertexai'] = 'Vertex AI';
$string['pluginname'] = 'Gemini';
$string['privacy:metadata'] = 'The local ai_manager tool subplugin "Gemini" does not store any personal data.';
