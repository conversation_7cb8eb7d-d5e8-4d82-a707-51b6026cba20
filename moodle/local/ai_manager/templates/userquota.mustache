{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template local_ai_manager/userquota

    Template for rendering the user quota info box.

    Example context (json):
    {
        "purposes": [
            {
                "currentusage": "5",
                "showmaxusage": true,
                "maxusage": "10",
                "querycounttext": "Chat requests",
                "limitreached": false,
                "islastelement": false
            },
            {
                "currentusage": "3",
                "showmaxusage": true,
                "maxusage": "15",
                "querycounttext": "Image Generation requests",
                "limitreached": false,
                "islastelement": true
            }
        ],
        "unlimited": false,
        "period": "per day"
    }
}}
<span class="local_ai_manager_userquota_infobox" data-content="local_ai_manager_userquota">
    {{#purposes}}
        {{#limitreached}}<span class="local_ai_manager-userquota_limitreached">{{/limitreached}}
            {{currentusage}}{{#showmaxusage}}/{{maxusage}}{{/showmaxusage}} {{querycounttext}}{{#limitreached}}</span>{{/limitreached}}{{^islastelement}}, {{/islastelement}}

    {{/purposes}}
    {{#unlimited}}{{#str}}within, local_ai_manager{{/str}}{{/unlimited}}
    {{^unlimited}}{{#str}}per, local_ai_manager{{/str}}{{/unlimited}}{{!
    }} {{period}}
</span>
