{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template local_ai_manager/promptsmodal_table

    Template for the AI prompts view modal.

    Example context (json):
    {

    }
}}

<div data-local_ai_manager-prompts_view="table" class="local_ai_manager-prompts_view">
    {{#noprompts}}
        <div class="alert alert-info">{{#str}}noprompts, local_ai_manager{{/str}}</div>
    {{/noprompts}}
    {{^noprompts}}
        <table class="table table-striped table-bordered">
            <tr>
                <th class="local_ai_manager-prompts_view_table-context">{{#str}}context, local_ai_manager{{/str}}</th>
                <th class="local_ai_manager-prompts_view_table-prompt">{{#str}}prompt, local_ai_manager{{/str}}</th>
                <th class="local_ai_manager-prompts_view_table-promptcompletion">{{#str}}airesponse, local_ai_manager{{/str}}</th>
                {{#promptsdatesavailable}}
                    <th class="local_ai_manager-prompts_view_table-date">{{#str}}date{{/str}}</th>
                {{/promptsdatesavailable}}
            </tr>
            {{#promptsobjects}}
                {{#prompts}}
                    <tr>
                        {{#firstprompt}}
                            <td data-view-prompts-contextid="{{contextid}}" rowspan="{{promptscount}}">{{contextdisplayname}}</td>
                        {{/firstprompt}}
                        <td>
                            <div>
                                <a data-toggle="collapse" href="#prompt_prompt_{{sequencenumber}}" role="button" aria-expanded="false"
                                   aria-controls="prompt_prompt_{{sequencenumber}}">
                                    {{!We intentionally do not use the "shortentext" mustache helper, because it wraps the text
                                        in an additional div which causes styling issues in the table.}}
                                    {{{promptshortened}}}
                                </a>
                            </div>
                            <div class="collapse" id="prompt_prompt_{{sequencenumber}}">
                                <div class="card card-body">
                                    {{{prompt}}}
                                </div>
                            </div>
                        </td>
                        <td>
                            <div>
                                <a data-toggle="collapse" href="#promptcompletion_prompt_{{sequencenumber}}" role="button" aria-expanded="false"
                                   aria-controls="promptcompletion_prompt_{{sequencenumber}}">
                                    {{!We intentionally do not use the "shortentext" mustache helper, because it wraps the text
                                        in an additional div which causes styling issues in the table.}}
                                    {{{promptcompletionshortened}}}
                                </a>
                            </div>
                            <div class="collapse" id="promptcompletion_prompt_{{sequencenumber}}">
                                <div class="card card-body">
                                    {{{promptcompletion}}}
                                </div>
                            </div>
                        </td>
                        {{#viewpromptsdates}}
                            <td>{{#userdate}}{{date}}, {{#str}} strftimedatetimeshort, core_langconfig {{/str}}{{/userdate}}</td>
                        {{/viewpromptsdates}}
                    </tr>
                {{/prompts}}
            {{/promptsobjects}}
        </table>
    {{/noprompts}}
</div>
