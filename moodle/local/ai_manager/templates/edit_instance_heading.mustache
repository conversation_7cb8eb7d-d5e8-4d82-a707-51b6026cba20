{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template local_ai_manager/edit_instance_heading

    Example context (json):
    {
        "heading": "Edit the 'Chat' instance",
        "showdeletebutton": true,
        "deleteurl": "https://mymoodle.com/editinstance.php?id=5&delete=1"
    }
}}
<div class="d-flex justify-content-between mb-5 align-items-center w-100" style="max-width: 80%">
    {{{heading}}}
    {{#showdeletebutton}}
        <div>
            <button class="btn"{{!
                 }} data-confirmation="modal"{{!
                 }} data-confirmation-title-str='["delete", "core"]'{{!
                 }} data-confirmation-content-str='["instancedeleteconfirm", "local_ai_manager"]'{{!
                 }} data-confirmation-yes-button-str='["delete", "core"]'{{!
                 }} data-confirmation-destination='{{{deleteurl}}}'>
                <i class="fa fa-trash"></i>
            </button>
        </div>
    {{/showdeletebutton}}
</div>
