{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template local_ai_manager/vertexcachestatus

    Template for showing and editing the Google Vertex AI cache status.

    Example context (json):
    {
        "noStatus": false,
        "cachingEnabled": true
    }
}}
<div id="local_ai_manager-vertexcachestatus-{{uniqid}}" class="local_ai_manager-vertexcachestatus d-flex flex-row justify-content-between mt-0 mb-5">
    {{#noStatus}}
        <div class="alert-info mr-3" data-status="nostatus">{{#str}}vertex_nocachestatus, local_ai_manager{{/str}}</div>
    {{/noStatus}}
    {{^noStatus}}
        <div class="flex-row d-flex align-items-center">
        {{#cachingEnabled}}
            <div class="local_ai_manager-caching_enabled mr-3 p-2 rounded border border-danger" data-status="cachingenabled">{{#str}}vertex_cachingenabled, local_ai_manager{{/str}}</div>
            <button class="btn btn-primary" data-action="disablecaching">{{#str}}vertex_disablecaching, local_ai_manager{{/str}}</button>
        {{/cachingEnabled}}
        {{^cachingEnabled}}
            <div class="local_ai_manager-caching_disabled mr-3 p-2 rounded border border-success" data-status="cachingdisabled">{{#str}}vertex_cachingdisabled, local_ai_manager{{/str}}</div>
            <button class="btn btn-primary" data-action="enablecaching">{{#str}}vertex_enablecaching, local_ai_manager{{/str}}</button>
        {{/cachingEnabled}}
        </div>
    {{/noStatus}}
    <div class="d-flex align-items-center"><button class="btn btn-secondary" data-action="refresh"><i class="fa-solid fa-rotate"></i></button></div>
</div>
{{#js}}
    require(['local_ai_manager/vertexcachestatus'], function(vertexcachestatus) {
    vertexcachestatus.init('#local_ai_manager-vertexcachestatus-{{uniqid}}');
    });
{{/js}}
